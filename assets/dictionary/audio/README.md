# Dictionary Audio

This directory is reserved for audio files related to the dictionary functionality.

## TTS Services for Dictionary Pronunciation

The app provides pronunciation for Chinese characters and words using a hybrid approach:

1. **Primary**: Flutter TTS (System TTS) - Works offline using device's built-in TTS engine
2. **Fallback**: Google Translate TTS API - Used when system TTS fails and internet is available

## Implementation Details

### System TTS (Primary)
- Uses `flutter_tts` package for offline pronunciation
- Configured specifically for Chinese Simplified (`zh-CN`)
- Works without internet connection
- Separate from the reading interface TTS system
- Optimized speech rate (0.5) for pronunciation clarity

### Google Translate TTS (Fallback)
- Used only when system TTS fails and device is online
- Endpoint: `https://translate.google.com/translate_tts`
- Parameters: `ie=UTF-8&q={text}&tl=zh-CN&client=tw-ob`
- No authentication required

## Benefits

1. **Offline Capability**: Primary system TTS works without internet
2. **Reliability**: Fallback ensures pronunciation is available when possible
3. **Separation**: Dictionary TTS is independent from reading interface TTS
4. **Performance**: No need to store large audio files locally

If you need to add local audio files for specific use cases, you can place them in this directory with the following naming convention:

- Single character: `[character].mp3` (e.g., `你.mp3`)
- Words: `[word].mp3` (e.g., `你好.mp3`)

However, the default behavior is to use online TTS services.

## Directory Structure

- `audio/` - Root directory for all audio files
  - `[character].mp3`
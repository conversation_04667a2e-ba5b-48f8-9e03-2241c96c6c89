# Chinese Dictionary Database

This directory should contain the Chinese dictionary database file:

- `chinese_dictionary.db`: SQLite database containing Chinese dictionary entries and character information

## Database Structure

The database should have the following tables:

### Dictionary Table

```sql
CREATE TABLE dictionary (
  id INTEGER PRIMARY KEY,
  traditional TEXT,
  simplified TEXT,
  pinyin TEXT,
  definitions TEXT,
  hsk_level INTEGER,
  frequency INTEGER
)
```

### Character Info Table

```sql
CREATE TABLE character_info (
  character TEXT PRIMARY KEY,
  radical TEXT,
  components TEXT
)
```

## Creating the Dictionary Database

1. Download the CC-CEDICT dictionary data from [CC-CEDICT website](https://cc-cedict.org/wiki/)
2. Convert the data to SQLite format using the provided conversion script
3. Place the resulting `chinese_dictionary.db` file in this directory

## Data Sources

- Dictionary data: [CC-CEDICT](https://cc-cedict.org/wiki/) (CC BY-SA 4.0)
- Character components: [UNIHAN database](https://www.unicode.org/charts/unihan.html) (Unicode License)

## License Information

Please ensure you comply with the licenses of the data sources used in your dictionary database. 
import 'dart:math';

import 'package:dasso_reader/models/hsk_character.dart';

/// Holds the data for a single practice round.
class JavaPracticeRound {
  /// The HSK character object that is the correct answer for this round.
  final <PERSON><PERSON><PERSON><PERSON>cter correctCharacter;

  /// A list of HSK character objects representing the options for this round (including the correct one).
  final List<HskCharacter> options;

  /// The index of the correct character within the original list of 30 HSK characters passed to the adapter.
  final int correctCharacterMasterListIndex;

  JavaPracticeRound({
    required this.correctCharacter,
    required this.options,
    required this.correctCharacterMasterListIndex,
  });
}

/// Adapts the logic from the Java MetricsPractice class for HSK character practice.
///
/// This adapter manages the state of a practice session, including character selection,
/// scoring, an adaptive delay mechanism, and a "Sudden Death" mode.
///
/// IMPORTANT: This adapter is designed to work with a fixed set of 30 HSK characters,
/// mirroring the constraints of the original Java implementation.
class JavaPracticeAdapter {
  // --- Constants from Java logic ---
  static const int _lim1 =
      6; // Threshold for a character to be considered "initially learned"
  static const int _lim2Mastered =
      500; // State indicating character is well-mastered
  static const int _lim2RecentlyCorrectAfterMastered =
      501; // State after correct answer for a mastered char
  static const int _lim2Default =
      502; // Default state for comparison if not otherwise set

  // --- Configuration ---
  final int characterCount = 30;
  final int roundOptionCount =
      7; // Number of options in a round (1 correct + 6 distractors, matching PracticeActivity.java myButtons = new Button[7])

  // --- Original HSK Characters ---
  List<HskCharacter>
      masterHskCharacterList; // The 30 characters for this session

  // --- State Variables (mirrored from Java's MetricsPractice) ---
  bool finished = false;
  bool sdFlag =
      false; // True when Sudden Death mode is about to begin (for UI cue)
  bool sdMode = false; // True when actively in Sudden Death mode

  List<int> cArr; // Mastery state for each character index (see _lim constants)
  int countVar = 0; // Total number of rounds/questions presented so far
  List<int>
      curRun; // Indices of characters in the current standard mode round pool
  List<int>
      dArr; // Temporary array used for shuffling options in Sudden Death mode
  bool missFlag = false; // Flag set if the last answer was incorrect
  int nLoop =
      0; // Tracks how many characters from the initial shuffled list (_sArr) have been introduced
  List<int> pLast; // Stores _countVar when a character was last practiced
  int rVar =
      0; // Index (in _masterHskCharacterList) of the correct character for the current round
  List<int>
      sArr; // Shuffled list of indices (0 to _characterCount-1) representing the overall practice sequence
  int sdCount = 0; // Number of successful rounds in Sudden Death mode
  int stCount = 0; // Number of characters considered "learned" in standard mode
  int timeDelayMs =
      1000; // Adaptive delay (in ms) after a correct answer before the next round
  int unknownCount =
      0; // Count of characters marked as "unknown" (missed on first try)

  int currentLim2 = _lim2Default;

  /// Constructor for JavaPracticeAdapter.
  ///
  /// Requires a list of exactly 30 [HskCharacter] objects.
  JavaPracticeAdapter({required List<HskCharacter> hskCharacters})
      : masterHskCharacterList = List.from(hskCharacters),
        cArr = List.filled(30, 0),
        curRun = List.filled(7, 0),
        dArr = List.generate(30, (i) => i),
        pLast = List.filled(30, 0),
        sArr = List.generate(30, (i) => i) {
    if (hskCharacters.length != characterCount) {
      throw ArgumentError(
        'JavaPracticeAdapter requires exactly $characterCount HSK characters. Received ${hskCharacters.length}',
      );
    }
    _init();
  }

  void _init() {
    cArr = List.filled(characterCount, 0);
    pLast = List.filled(characterCount, 0);
    dArr = List.generate(characterCount, (index) => index);
    sArr = List.generate(characterCount, (index) => index);

    countVar = 0;
    stCount = 0;
    sdCount = 0;
    unknownCount = 0;
    timeDelayMs = 1000;
    finished = false;
    sdFlag = false;
    sdMode = false;
    missFlag = false;
    currentLim2 = _lim2Default;

    _ranSeq(); // Shuffle _sArr

    for (int i = 0; i < roundOptionCount; i++) {
      if (i < sArr.length) {
        curRun[i] = sArr[i];
      }
    }
    nLoop = roundOptionCount;
    if (characterCount < roundOptionCount) {
      nLoop = characterCount;
    }
  }

  void _ranSeq() {
    sArr.shuffle(Random());
  }

  List<int> _randomizeIntArray(List<int> inArr) {
    final listCopy = List<int>.from(inArr);
    listCopy.shuffle(Random());
    return listCopy;
  }

  List<int> _randomizeIntArrayInit(List<int> inArr, int initVal) {
    final listCopy = List<int>.from(inArr);
    listCopy.shuffle(Random());

    final initIndex = listCopy.indexOf(initVal);
    if (initIndex != -1 && initIndex != 0) {
      final temp = listCopy[0];
      listCopy[0] = listCopy[initIndex];
      listCopy[initIndex] = temp;
    }
    return listCopy;
  }

  JavaPracticeRound getRound() {
    countVar++;
    List<int> roundCharacterIndices;

    if (sdMode) {
      roundCharacterIndices = _sdRound();
    } else {
      roundCharacterIndices = _stRound();
    }

    HskCharacter correctChar = masterHskCharacterList[rVar];

    List<HskCharacter> optionChars = [];
    for (int index in roundCharacterIndices) {
      if (index >= 0 && index < masterHskCharacterList.length) {
        optionChars.add(masterHskCharacterList[index]);
      }
    }

    Set<HskCharacter> uniqueOptionsSet = <HskCharacter>{};
    List<HskCharacter> finalOptions = [];

    // Add the correct character first if it's part of the indices, ensure it's included
    if (roundCharacterIndices.contains(rVar)) {
      uniqueOptionsSet.add(correctChar);
      finalOptions.add(correctChar);
    }

    for (int index in roundCharacterIndices) {
      if (finalOptions.length >= roundOptionCount) break;
      if (index >= 0 && index < masterHskCharacterList.length) {
        HskCharacter charToAdd = masterHskCharacterList[index];
        if (uniqueOptionsSet.add(charToAdd)) {
          // Add returns true if element was added (i.e. was unique)
          finalOptions.add(charToAdd);
        }
      }
    }

    // If not enough unique options, fill with other random chars excluding the correct one and existing options
    if (finalOptions.length < roundOptionCount) {
      List<HskCharacter> distractors = List.from(masterHskCharacterList);
      for (var opt in uniqueOptionsSet) {
        distractors.remove(opt);
      } // Remove all already in finalOptions
      distractors.shuffle(Random());

      int needed = roundOptionCount - finalOptions.length;
      if (distractors.length >= needed) {
        finalOptions.addAll(distractors.take(needed));
      }
    }

    finalOptions.shuffle(Random());

    // Ensure correct character is definitely in the options and list has correct size
    if (!finalOptions.contains(correctChar)) {
      if (finalOptions.isNotEmpty && finalOptions.length >= roundOptionCount) {
        finalOptions.removeLast();
      } else if (finalOptions.isEmpty && roundOptionCount == 0) {
        /* do nothing */
      } else if (finalOptions.length < roundOptionCount) {
        /* it will be added */
      } else {
        /* this case should be rare, if list is full but no correct char */ finalOptions
            .removeLast();
      }

      if (finalOptions.length < roundOptionCount) {
        finalOptions.add(correctChar);
      }
      finalOptions.shuffle(Random());
    }
    // Ensure the list is exactly _roundOptionCount long.
    while (finalOptions.length < roundOptionCount &&
        masterHskCharacterList.isNotEmpty) {
      // This case should ideally be rare if previous logic is robust
      HskCharacter randomFallback = masterHskCharacterList[
          Random().nextInt(masterHskCharacterList.length)];
      if (!finalOptions.contains(randomFallback)) {
        finalOptions.add(randomFallback);
      } // else try again or have a larger pool
    }
    if (finalOptions.length > roundOptionCount) {
      finalOptions = finalOptions.sublist(0, roundOptionCount);
    }

    return JavaPracticeRound(
      correctCharacter: correctChar,
      options: finalOptions,
      correctCharacterMasterListIndex: rVar,
    );
  }

  List<int> _sdRound() {
    int ini = sArr[sdCount %
        characterCount]; // Use modulo to prevent overflow if sdCount > _characterCount
    dArr = _randomizeIntArrayInit(List.generate(characterCount, (i) => i), ini);

    List<int> myOut = List.filled(roundOptionCount, 0);
    for (int i = 0; i < roundOptionCount; i++) {
      if (i < dArr.length) myOut[i] = dArr[i];
    }
    sdCount++;
    rVar = ini;
    return myOut;
  }

  List<int> _stRound() {
    rVar = curRun[0];
    return List<int>.from(curRun.take(roundOptionCount));
  }

  void _setIncVar() {
    timeDelayMs -= 50;
    if (timeDelayMs < 50) {
      timeDelayMs = 50;
    }
  }

  void hit(int characterMasterListIndex) {
    _hitUpdateStats();
    _hitNextRound();
    if (sdMode && sdCount >= characterCount) {
      finished = true;
    }
  }

  void _hitUpdateStats() {
    if (!missFlag) {
      _setIncVar();
      if (cArr[rVar] == 0) {
        stCount++;
        cArr[rVar] = _lim1;
      } else {
        cArr[rVar]++;
      }
    }
    missFlag = false;
    pLast[rVar] = countVar + 1;
  }

  void _hitNextRound() {
    if (sdMode) {
      return;
    }

    if (_checkGood()) {
      _initSdMode();
    } else {
      _updateCurRun();
    }
  }

  void _updateCurRun() {
    if (curRun.isEmpty) {
      // Should not happen if initialized correctly
      _prodNew(); // Try to populate curRun
      if (curRun.isEmpty) return; // Still empty, something is wrong
    }

    if (cArr[curRun[0]] == _lim1) {
      cArr[curRun[0]] = _lim2Mastered;
      _prodNew();
    } else if (cArr[curRun[0]] >= currentLim2) {
      // Changed to >= to catch 500,501,502 properly
      _prodNew();
    }

    if (roundOptionCount > 1 && curRun.length >= roundOptionCount) {
      int curShuf = 1;
      for (int i = 2; i < roundOptionCount; i++) {
        if (_getUrg(curRun[i]) > _getUrg(curRun[curShuf])) {
          curShuf = i;
        }
      }

      int tempCharIndex = curRun[0];
      curRun[0] = curRun[curShuf];
      curRun[curShuf] = tempCharIndex;

      int charToMoveToEnd = curRun[curShuf];
      for (int i = curShuf; i < roundOptionCount - 1; i++) {
        curRun[i] = curRun[i + 1];
      }
      curRun[roundOptionCount - 1] = charToMoveToEnd;
    } else if (roundOptionCount == 1 && curRun.isNotEmpty) {
      _prodNew();
    }
  }

  void _prodNew() {
    if (curRun.isEmpty && roundOptionCount > 0) {
      curRun = List.filled(roundOptionCount, 0);
    } else if (curRun.isEmpty && roundOptionCount == 0) {
      return; // Nothing to produce into
    }

    if (nLoop < characterCount) {
      curRun[0] = sArr[nLoop];
      nLoop++;
      return;
    }

    if (nLoop == characterCount) {
      _setLim();
      sArr = _randomizeIntArray(List.from(sArr));
      nLoop++;
    }

    for (int i = 0; i < characterCount; i++) {
      int charIndexToCheck = sArr[i];
      if (cArr[charIndexToCheck] < currentLim2 &&
          _checkAlready(charIndexToCheck)) {
        curRun[0] = charIndexToCheck;
        return;
      }
    }
  }

  bool _checkAlready(int val) {
    // Check if val is already in the first _roundOptionCount elements of _curRun, excluding index 0 (which is being replaced)
    for (int i = 1; i < roundOptionCount; i++) {
      if (i < curRun.length && curRun[i] == val) {
        return false;
      }
    }
    return true;
  }

  void _setLim() {
    if (unknownCount <= 5) {
      currentLim2 = _lim2Mastered;
    } else if (unknownCount <= 15) {
      currentLim2 = _lim2RecentlyCorrectAfterMastered;
    } else {
      // Original Java had LIM_2 = 501 then 500. If unknown > 15, it would be default LIM_2 (502 from context)
      // This implies that if many are unknown, the threshold for considering something "mastered enough to skip" is higher.
      currentLim2 = _lim2Default;
    }
  }

  bool _checkGood() {
    if (countVar == 10 && unknownCount == 0) {
      return true; // Exact round 10: Perfect mastery checkpoint
    }
    if (countVar == 15 && unknownCount < 2) {
      return true; // Exact round 15: Near-perfect mastery checkpoint
    }

    for (int i = 0; i < characterCount; i++) {
      if (cArr[i] < _lim2Mastered) {
        return false;
      }
    }
    sdFlag =
        true; // Set sdFlag here if all mastered, so UI can react before next round call.
    return true;
  }

  void miss(int characterMasterListIndex) {
    missFlag = true;
    if (cArr[rVar] >= _lim2Mastered - 2) {
      cArr[rVar] = _lim2Mastered - 2;
    } else {
      if (cArr[rVar] > 4) {
        cArr[rVar] = 4;
      }
      // Original Java: if (this.cArr[this.rVar] == 0) { this.cArr[this.rVar] = 1; this.unknownCount++; }
      // This implies that if cArr[rVar] was 0 (never seen or fully reset), it becomes 1 and increments unknownCount.
      // If it was already 1,2,3,4 from previous misses, it stays there, and unknownCount is not incremented again for the same char.
      // Let's refine this based on whether it *became* 1 due to this miss.
      if (cArr[rVar] == 0) {
        // If truly unseen or reset to 0 before this miss sequence for this char
        cArr[rVar] = 1;
        unknownCount++;
      } else if (cArr[rVar] > 0 && cArr[rVar] <= 4) {
        // It was already in a low state from previous misses, do nothing to cArr or unknownCount
      } else {
        // E.g. was 5, or _lim1 etc. Now set to 1 and count as new unknown.
        cArr[rVar] = 1;
        unknownCount++; // Count it if it wasn't already in the 1-4 range or 0.
      }
    }

    if (sdMode) {
      finished = true;
    }
  }

  void _initSdMode() {
    sdCount = 0;
    stCount = characterCount;
    sArr = _randomizeIntArray(List.from(sArr));
    sdFlag = true;
    sdMode = true;
  }

  int _getLastPlayVar(int charIndex) {
    if (charIndex < 0 || charIndex >= pLast.length || pLast[charIndex] == 0) {
      return 0;
    }
    return (countVar + 1) - pLast[charIndex];
  }

  int _getUrg(int charIndex) {
    if (charIndex < 0 || charIndex >= cArr.length) {
      return -9999; // Should not happen
    }
    return _getLastPlayVar(charIndex) - cArr[charIndex];
  }

  int getProgress() {
    int currentStCount = stCount;
    if (currentStCount > characterCount) {
      currentStCount = characterCount; // Cap stCount at 30, matching Java logic
    }
    return sdCount + currentStCount; // sdCount + min(stCount, 30) = max 60
  }

  int get currentRoundNumberPublicGetter => countVar;
  int get totalCharactersToMaster => characterCount;
  int get successfulSdRounds => sdCount;
  int get standardLearnedCount => stCount;
  bool get isSuddenDeathActive => sdMode;
  bool get isSuddenDeathPending =>
      sdFlag; // Use this to show a message before SD starts

  void acknowledgeSdFlag() {
    sdFlag = false;
  }
}

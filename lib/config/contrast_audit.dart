import 'package:flutter/material.dart';
import 'design_system.dart';
import 'color_system.dart';

/// Contrast audit utility for WCAG compliance validation
///
/// This class provides tools to audit and validate color contrast ratios
/// across the Dasso Reader app to ensure accessibility compliance.
class ContrastAudit {
  // Private constructor to prevent instantiation
  ContrastAudit._();

  // =====================================================
  // WCAG CONTRAST STANDARDS
  // =====================================================

  /// WCAG AA minimum contrast ratio for normal text (4.5:1)
  static const double wcagAANormalText = 4.5;

  /// WCAG AA minimum contrast ratio for large text (3:1)
  static const double wcagAALargeText = 3.0;

  /// WCAG AAA minimum contrast ratio for normal text (7:1)
  static const double wcagAAANormalText = 7.0;

  /// WCAG AAA minimum contrast ratio for large text (4.5:1)
  static const double wcagAAALargeText = 4.5;

  // =====================================================
  // CONTRAST VALIDATION METHODS
  // =====================================================

  /// Validate reading theme contrast
  static ContrastValidationResult validateReadingTheme(
    String backgroundColor,
    String textColor,
  ) {
    final bgColor = Color(int.parse('0x$backgroundColor'));
    final txtColor = Color(int.parse('0x$textColor'));

    final ratio = _calculateContrastRatio(txtColor, bgColor);
    final isValid = ratio >= wcagAANormalText;

    return ContrastValidationResult(
      foreground: txtColor,
      background: bgColor,
      ratio: ratio,
      isValidAA: isValid,
      isValidAAA: ratio >= wcagAAANormalText,
      recommendation: isValid ? null : _getContrastRecommendation(bgColor),
    );
  }

  /// Validate HSK level badge contrast
  static ContrastValidationResult validateHskBadge(int level) {
    final color = ColorSystem.getHskLevelColor(level);
    final backgroundColor = color.withValues(alpha: 0.2);
    final textColor = color.withValues(alpha: 0.8);

    final ratio = _calculateContrastRatio(textColor, backgroundColor);
    final isValid = ratio >= wcagAANormalText;

    return ContrastValidationResult(
      foreground: textColor,
      background: backgroundColor,
      ratio: ratio,
      isValidAA: isValid,
      isValidAAA: ratio >= wcagAAANormalText,
      recommendation:
          isValid ? null : _getContrastRecommendation(backgroundColor),
    );
  }

  /// Validate button contrast
  static ContrastValidationResult validateButton(
    BuildContext context,
    ButtonType buttonType,
  ) {
    final buttonColors = ColorSystem.getButtonColors(context, buttonType);

    final ratio = _calculateContrastRatio(
      buttonColors.foreground,
      buttonColors.background,
    );
    final isValid = ratio >= wcagAANormalText;

    return ContrastValidationResult(
      foreground: buttonColors.foreground,
      background: buttonColors.background,
      ratio: ratio,
      isValidAA: isValid,
      isValidAAA: ratio >= wcagAAANormalText,
      recommendation:
          isValid ? null : _getContrastRecommendation(buttonColors.background),
    );
  }

  /// Validate status message contrast
  static ContrastValidationResult validateStatusMessage(
    BuildContext context,
    StatusType statusType,
  ) {
    final statusColors = ColorSystem.getStatusColors(context, statusType);

    final ratio = _calculateContrastRatio(
      statusColors.foreground,
      statusColors.background,
    );
    final isValid = ratio >= wcagAANormalText;

    return ContrastValidationResult(
      foreground: statusColors.foreground,
      background: statusColors.background,
      ratio: ratio,
      isValidAA: isValid,
      isValidAAA: ratio >= wcagAAANormalText,
      recommendation:
          isValid ? null : _getContrastRecommendation(statusColors.background),
    );
  }

  /// Validate custom color pair
  static ContrastValidationResult validateColorPair(
    Color foreground,
    Color background, {
    bool isLargeText = false,
  }) {
    final ratio = _calculateContrastRatio(foreground, background);
    final minRatio = isLargeText ? wcagAALargeText : wcagAANormalText;
    final isValid = ratio >= minRatio;

    return ContrastValidationResult(
      foreground: foreground,
      background: background,
      ratio: ratio,
      isValidAA: isValid,
      isValidAAA: ratio >= (isLargeText ? wcagAAALargeText : wcagAAANormalText),
      recommendation: isValid ? null : _getContrastRecommendation(background),
    );
  }

  // =====================================================
  // BATCH VALIDATION METHODS
  // =====================================================

  /// Validate all design system colors
  static List<ContrastValidationResult> validateDesignSystemColors() {
    final results = <ContrastValidationResult>[];

    // Validate primary colors
    results.add(validateColorPair(Colors.white, DesignSystem.primaryColor));
    results.add(
      validateColorPair(
        DesignSystem.textColorPrimary,
        DesignSystem.surfaceColor,
      ),
    );
    results.add(
      validateColorPair(
        DesignSystem.darkTextColorPrimary,
        DesignSystem.darkSurfaceColor,
      ),
    );

    // Validate state colors
    results.add(validateColorPair(Colors.white, DesignSystem.successColor));
    results.add(validateColorPair(Colors.white, DesignSystem.warningColor));
    results.add(validateColorPair(Colors.white, DesignSystem.errorColor));

    return results;
  }

  /// Validate all HSK level badges
  static List<ContrastValidationResult> validateAllHskBadges() {
    final results = <ContrastValidationResult>[];

    for (int level = 1; level <= 6; level++) {
      results.add(validateHskBadge(level));
    }

    return results;
  }

  // =====================================================
  // HELPER METHODS
  // =====================================================

  /// Calculate contrast ratio between two colors
  static double _calculateContrastRatio(Color foreground, Color background) {
    final luminance1 = foreground.computeLuminance();
    final luminance2 = background.computeLuminance();

    final lighter = luminance1 > luminance2 ? luminance1 : luminance2;
    final darker = luminance1 > luminance2 ? luminance2 : luminance1;

    return (lighter + 0.05) / (darker + 0.05);
  }

  /// Get contrast improvement recommendation
  static Color _getContrastRecommendation(Color background) {
    return ColorSystem.getContrastingColor(background);
  }

  /// Generate contrast audit report
  static String generateAuditReport(List<ContrastValidationResult> results) {
    final buffer = StringBuffer();
    buffer.writeln('=== CONTRAST AUDIT REPORT ===\n');

    int passed = 0;
    int failed = 0;

    for (final result in results) {
      if (result.isValidAA) {
        passed++;
        buffer.writeln('✅ PASS: ${result.ratio.toStringAsFixed(2)}:1');
      } else {
        failed++;
        buffer.writeln(
          '❌ FAIL: ${result.ratio.toStringAsFixed(2)}:1 (min: $wcagAANormalText:1)',
        );
        buffer.writeln(
          '   Recommendation: Use ${result.recommendation?.toString() ?? 'high contrast color'}',
        );
      }
    }

    buffer.writeln('\n=== SUMMARY ===');
    buffer.writeln('Passed: $passed');
    buffer.writeln('Failed: $failed');
    buffer.writeln('Total: ${results.length}');
    buffer.writeln(
      'Success Rate: ${(passed / results.length * 100).toStringAsFixed(1)}%',
    );

    return buffer.toString();
  }
}

// =====================================================
// RESULT CLASSES
// =====================================================

/// Result of a contrast validation check
class ContrastValidationResult {
  final Color foreground;
  final Color background;
  final double ratio;
  final bool isValidAA;
  final bool isValidAAA;
  final Color? recommendation;

  const ContrastValidationResult({
    required this.foreground,
    required this.background,
    required this.ratio,
    required this.isValidAA,
    required this.isValidAAA,
    this.recommendation,
  });

  /// Get WCAG compliance level
  String get complianceLevel {
    if (isValidAAA) return 'AAA';
    if (isValidAA) return 'AA';
    return 'FAIL';
  }

  @override
  String toString() {
    return 'ContrastValidationResult(ratio: ${ratio.toStringAsFixed(2)}:1, level: $complianceLevel)';
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';
import 'package:dasso_reader/config/app_icons.dart';
import 'package:dasso_reader/config/platform_adaptations.dart';
import 'package:dasso_reader/config/design_system.dart';

import 'package:dasso_reader/config/shared_preference_provider.dart';

// =====================================================
// NAVIGATION ENUMS
// =====================================================

/// Navigation feedback types
enum NavigationFeedbackType {
  selection,
  transition,
  error,
  success,
}

/// Navigation error types
enum NavigationError {
  routeNotFound,
  accessDenied,
  networkError,
  invalidState,
  unknown,
}

/// Navigation transition types
enum NavigationTransitionType {
  fade,
  slide,
  scale,
  sharedAxis,
  fadeThrough,
  platform,
}

// =====================================================
// NAVIGATION CLASSES
// =====================================================

/// Navigation destination definition
class NavigationDestination {
  final String id;
  final IconData icon;
  final IconData? selectedIcon;
  final String Function(BuildContext) labelBuilder;
  final String Function(BuildContext) tooltipBuilder;
  final bool isConditional;
  final bool Function()? shouldShow;

  const NavigationDestination({
    required this.id,
    required this.icon,
    this.selectedIcon,
    required this.labelBuilder,
    required this.tooltipBuilder,
    this.isConditional = false,
    this.shouldShow,
  });

  String getLabel(BuildContext context) => labelBuilder(context);
  String getTooltip(BuildContext context) => tooltipBuilder(context);
  IconData getIcon({bool selected = false}) =>
      selected && selectedIcon != null ? selectedIcon! : icon;
}

/// Navigation state information
class NavigationState {
  final int currentIndex;
  final String currentDestinationId;
  final bool canGoBack;
  final bool canGoForward;
  final List<String> navigationHistory;
  final String? currentPageTitle;

  const NavigationState({
    required this.currentIndex,
    required this.currentDestinationId,
    this.canGoBack = false,
    this.canGoForward = false,
    this.navigationHistory = const [],
    this.currentPageTitle,
  });

  NavigationState copyWith({
    int? currentIndex,
    String? currentDestinationId,
    bool? canGoBack,
    bool? canGoForward,
    List<String>? navigationHistory,
    String? currentPageTitle,
  }) {
    return NavigationState(
      currentIndex: currentIndex ?? this.currentIndex,
      currentDestinationId: currentDestinationId ?? this.currentDestinationId,
      canGoBack: canGoBack ?? this.canGoBack,
      canGoForward: canGoForward ?? this.canGoForward,
      navigationHistory: navigationHistory ?? this.navigationHistory,
      currentPageTitle: currentPageTitle ?? this.currentPageTitle,
    );
  }
}

/// Comprehensive Navigation System for Dasso Reader
///
/// This system provides:
/// - 🧭 Consistent navigation patterns across all platforms
/// - 🎯 Clear and intuitive navigation icons and labels
/// - 🔄 Smooth and consistent transitions
/// - 🛡️ Robust error handling and recovery
/// - ♿ Accessibility-compliant navigation
/// - 📱 Platform-adaptive navigation behavior
class NavigationSystem {
  NavigationSystem._();

  /// Primary navigation destinations for Dasso Reader
  static List<NavigationDestination> get primaryDestinations => [
        NavigationDestination(
          id: 'bookshelf',
          icon: Icons.book_outlined,
          selectedIcon: Icons.book,
          labelBuilder: (context) => L10n.of(context).navBar_bookshelf,
          tooltipBuilder: (context) => L10n.of(context).navBar_bookshelf,
        ),
        NavigationDestination(
          id: 'dictionary',
          icon: Icons.translate_outlined,
          selectedIcon: Icons.translate,
          labelBuilder: (context) => L10n.of(context).navBar_dictionary,
          tooltipBuilder: (context) => L10n.of(context).navBar_dictionary,
        ),
        NavigationDestination(
          id: 'vocabulary',
          icon: Icons.menu_book_outlined,
          selectedIcon: Icons.menu_book,
          labelBuilder: (context) => L10n.of(context).navBar_vocabulary,
          tooltipBuilder: (context) => L10n.of(context).navBar_vocabulary,
        ),
        NavigationDestination(
          id: 'hsk',
          icon: Icons.school_outlined,
          selectedIcon: Icons.school,
          labelBuilder: (context) => L10n.of(context).navBar_hsk,
          tooltipBuilder: (context) => L10n.of(context).navBar_hsk,
        ),
        NavigationDestination(
          id: 'notes',
          icon: Icons.note_outlined,
          selectedIcon: Icons.note,
          labelBuilder: (context) => L10n.of(context).navBar_notes,
          tooltipBuilder: (context) => L10n.of(context).navBar_notes,
          isConditional: true,
          shouldShow: () => Prefs().bottomNavigatorShowNote,
        ),
      ];

  /// Get visible navigation destinations based on current settings
  static List<NavigationDestination> getVisibleDestinations() {
    return primaryDestinations.where((destination) {
      if (!destination.isConditional) return true;
      return destination.shouldShow?.call() ?? true;
    }).toList();
  }

  // =====================================================
  // NAVIGATION FEEDBACK SYSTEM
  // =====================================================

  /// Provides haptic and visual feedback for navigation actions
  static void provideFeedback(NavigationFeedbackType type) {
    switch (type) {
      case NavigationFeedbackType.selection:
        if (PlatformAdaptations.shouldUseHapticFeedback) {
          HapticFeedback.selectionClick();
        }
        break;
      case NavigationFeedbackType.transition:
        if (PlatformAdaptations.shouldUseHapticFeedback) {
          HapticFeedback.lightImpact();
        }
        break;
      case NavigationFeedbackType.error:
        if (PlatformAdaptations.shouldUseHapticFeedback) {
          HapticFeedback.heavyImpact();
        }
        break;
      case NavigationFeedbackType.success:
        if (PlatformAdaptations.shouldUseHapticFeedback) {
          HapticFeedback.mediumImpact();
        }
        break;
    }
  }

  // =====================================================
  // NAVIGATION ERROR HANDLING
  // =====================================================

  /// Handles navigation errors with appropriate user feedback
  static void handleNavigationError(
    BuildContext context,
    NavigationError error, {
    String? customMessage,
    VoidCallback? onRetry,
  }) {
    String message;
    switch (error) {
      case NavigationError.routeNotFound:
        message = 'Page not found. Returning to home.';
        break;
      case NavigationError.accessDenied:
        message = 'Access denied to this section.';
        break;
      case NavigationError.networkError:
        message = 'Network error. Please check your connection.';
        break;
      case NavigationError.invalidState:
        message = 'Invalid navigation state. Resetting to home.';
        break;
      case NavigationError.unknown:
        message = 'An unexpected error occurred.';
        break;
    }

    // Provide error feedback
    provideFeedback(NavigationFeedbackType.error);

    // Show error message using existing toast system
    // Note: Using a simple approach since AnxToast might not be available
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(customMessage ?? message),
        backgroundColor: Theme.of(context).colorScheme.error,
        behavior: SnackBarBehavior.floating,
      ),
    );

    // Handle recovery based on error type
    _handleErrorRecovery(context, error, onRetry);
  }

  /// Handles error recovery strategies
  static void _handleErrorRecovery(
    BuildContext context,
    NavigationError error,
    VoidCallback? onRetry,
  ) {
    switch (error) {
      case NavigationError.routeNotFound:
      case NavigationError.invalidState:
        // Navigate back to safe state (home)
        Navigator.of(context).popUntil((route) => route.isFirst);
        break;
      case NavigationError.accessDenied:
        // Stay on current page, just show message
        break;
      case NavigationError.networkError:
        // Offer retry option
        if (onRetry != null) {
          Future.delayed(const Duration(seconds: 2), onRetry);
        }
        break;
      case NavigationError.unknown:
        // Log error and stay on current page
        break;
    }
  }

  // =====================================================
  // NAVIGATION TRANSITIONS
  // =====================================================

  /// Creates platform-appropriate page transitions
  static PageRouteBuilder<T> createTransition<T>({
    required Widget page,
    required RouteSettings settings,
    NavigationTransitionType type = NavigationTransitionType.platform,
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeOutCubic,
  }) {
    // Use platform-appropriate transition by default
    if (type == NavigationTransitionType.platform) {
      return PageRouteBuilder<T>(
        settings: settings,
        transitionDuration: duration,
        pageBuilder: (context, animation, secondaryAnimation) => page,
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          if (PlatformAdaptations.isIOS) {
            // iOS-style slide transition
            return SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(1.0, 0.0),
                end: Offset.zero,
              ).animate(CurvedAnimation(parent: animation, curve: curve)),
              child: child,
            );
          } else {
            // Material-style fade through transition
            return FadeTransition(
              opacity: CurvedAnimation(parent: animation, curve: curve),
              child: SlideTransition(
                position: Tween<Offset>(
                  begin: const Offset(0.0, 0.05),
                  end: Offset.zero,
                ).animate(CurvedAnimation(parent: animation, curve: curve)),
                child: child,
              ),
            );
          }
        },
      );
    }

    // Custom transition types
    return PageRouteBuilder<T>(
      settings: settings,
      transitionDuration: duration,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        final curvedAnimation =
            CurvedAnimation(parent: animation, curve: curve);

        switch (type) {
          case NavigationTransitionType.fade:
            return FadeTransition(opacity: curvedAnimation, child: child);

          case NavigationTransitionType.slide:
            return SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(1.0, 0.0),
                end: Offset.zero,
              ).animate(curvedAnimation),
              child: child,
            );

          case NavigationTransitionType.scale:
            return ScaleTransition(
              scale:
                  Tween<double>(begin: 0.8, end: 1.0).animate(curvedAnimation),
              child: FadeTransition(opacity: curvedAnimation, child: child),
            );

          case NavigationTransitionType.sharedAxis:
            return FadeTransition(
              opacity: curvedAnimation,
              child: SlideTransition(
                position: Tween<Offset>(
                  begin: const Offset(0.1, 0.0),
                  end: Offset.zero,
                ).animate(curvedAnimation),
                child: child,
              ),
            );

          case NavigationTransitionType.fadeThrough:
            return FadeTransition(
              opacity: curvedAnimation,
              child: SlideTransition(
                position: Tween<Offset>(
                  begin: const Offset(0.0, 0.05),
                  end: Offset.zero,
                ).animate(curvedAnimation),
                child: child,
              ),
            );

          case NavigationTransitionType.platform:
            // This case is handled above
            return child;
        }
      },
    );
  }

  // =====================================================
  // NAVIGATION VISUAL FEEDBACK
  // =====================================================

  /// Creates enhanced navigation tab with visual feedback
  static Widget createNavigationTab({
    required BuildContext context,
    required NavigationDestination destination,
    required bool isSelected,
    required VoidCallback onTap,
    bool isCompact = false,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    final iconSize = isCompact ? AppIcons.sizeS : AppIcons.sizeM;

    return Semantics(
      label: destination.getTooltip(context),
      button: true,
      selected: isSelected,
      child: InkWell(
        onTap: () {
          provideFeedback(NavigationFeedbackType.selection);
          onTap();
        },
        borderRadius: BorderRadius.circular(DesignSystem.radiusM),
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: DesignSystem.spaceS,
            vertical: DesignSystem.spaceXS,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Enhanced icon with selection animation
              AnimatedContainer(
                duration: DesignSystem.durationFast,
                curve: Curves.easeOutCubic,
                padding: EdgeInsets.all(isSelected ? DesignSystem.spaceXS : 0),
                decoration: isSelected
                    ? BoxDecoration(
                        color: colorScheme.primaryContainer
                            .withAlpha(51), // 0.2 * 255
                        borderRadius:
                            BorderRadius.circular(DesignSystem.radiusS),
                      )
                    : null,
                child: Icon(
                  destination.getIcon(selected: isSelected),
                  size: iconSize,
                  color: isSelected
                      ? colorScheme.primary
                      : colorScheme.onSurfaceVariant,
                ),
              ),

              if (!isCompact) ...[
                const SizedBox(height: DesignSystem.spaceXS),
                // Enhanced label with selection animation
                AnimatedDefaultTextStyle(
                  duration: DesignSystem.durationFast,
                  curve: Curves.easeOutCubic,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                    color: isSelected
                        ? colorScheme.primary
                        : colorScheme.onSurfaceVariant,
                    letterSpacing: -0.2,
                  ),
                  child: Text(
                    destination.getLabel(context),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  // =====================================================
  // NAVIGATION BREADCRUMBS
  // =====================================================

  /// Creates navigation breadcrumb widget for location awareness
  static Widget createBreadcrumb({
    required BuildContext context,
    required List<String> breadcrumbs,
    VoidCallback? onHomeTap,
  }) {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: DesignSystem.spaceM,
        vertical: DesignSystem.spaceS,
      ),
      child: Row(
        children: [
          // Home icon
          if (onHomeTap != null)
            Semantics(
              label: 'Home',
              hint: 'Navigate to home page',
              button: true,
              child: InkWell(
                onTap: () {
                  provideFeedback(NavigationFeedbackType.selection);
                  onHomeTap();
                },
                borderRadius: BorderRadius.circular(DesignSystem.radiusS),
                child: Padding(
                  padding: const EdgeInsets.all(DesignSystem.spaceXS),
                  child: Icon(
                    AdaptiveIcons.home,
                    size: AppIcons.sizeS,
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
              ),
            ),

          // Breadcrumb items
          ...breadcrumbs.asMap().entries.map((entry) {
            final index = entry.key;
            final breadcrumb = entry.value;
            final isLast = index == breadcrumbs.length - 1;

            return Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (index > 0 || onHomeTap != null) ...[
                  const SizedBox(width: DesignSystem.spaceXS),
                  ExcludeSemantics(
                    child: Icon(
                      AdaptiveIcons.chevronRight,
                      size: AppIcons.sizeXS,
                      color: DesignSystem.getStateLayerColor(
                        colorScheme.onSurfaceVariant,
                        0.5,
                      ),
                    ),
                  ),
                  const SizedBox(width: DesignSystem.spaceXS),
                ],
                Semantics(
                  label: isLast
                      ? 'Current page: $breadcrumb'
                      : 'Breadcrumb: $breadcrumb',
                  child: Text(
                    breadcrumb,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: isLast ? FontWeight.w600 : FontWeight.w400,
                      color: isLast
                          ? colorScheme.onSurface
                          : colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
              ],
            );
          }),
        ],
      ),
    );
  }
}

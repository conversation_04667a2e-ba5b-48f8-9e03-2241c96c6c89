import 'package:dasso_reader/dao/book.dart' as book_dao;
import 'package:dasso_reader/models/book.dart';
import 'package:dasso_reader/utils/state_management/efficient_data_handling.dart';
import 'package:dasso_reader/utils/state_management/efficient_data_utils.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Efficient book list provider with pagination and caching
class EfficientBookListNotifier
    extends StateNotifier<AsyncValue<PaginatedResult<List<Book>>>> {
  late PaginationManager<List<Book>> _paginationManager;
  late IntelligentCache<String, List<Book>> _searchCache;

  EfficientBookListNotifier() : super(const AsyncValue.loading()) {
    _initialize();
  }

  Future<void> _initialize() async {
    try {
      // Initialize efficient data handling if not already done
      EfficientDataUtils.initialize();

      // Create pagination manager for books
      _paginationManager = EfficientDataUtils.getPaginationManager<List<Book>>(
        key: 'book_list',
        dataType: DataType.books,
        dataLoader: _loadBooksPage,
      );

      // Create search cache
      _searchCache = EfficientDataUtils.getCache<String, List<Book>>(
        name: 'book_search_cache',
        maxSize: 50,
        defaultTtl: const Duration(minutes: 5),
      );

      // Load first page
      final result = await _paginationManager.loadPage(1);
      state = AsyncValue.data(result);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Load a specific page of books
  Future<PaginatedResult<List<Book>>> _loadBooksPage(
    int page,
    int pageSize,
  ) async {
    if (kDebugMode) {
      debugPrint(
        '📚 EfficientBookList: Loading page $page with size $pageSize',
      );
    }

    // Get all books from database
    final allBooks = await book_dao.selectNotDeleteBooks();

    // Group books efficiently
    final groupedBooks = await _groupBooksEfficiently(allBooks);

    // Calculate pagination
    final totalItems = groupedBooks.length;
    final totalPages = (totalItems / pageSize).ceil();
    final startIndex = (page - 1) * pageSize;
    final endIndex = (startIndex + pageSize).clamp(0, totalItems);

    final pageItems = groupedBooks.sublist(
      startIndex.clamp(0, totalItems),
      endIndex,
    );

    return PaginatedResult<List<Book>>(
      items: pageItems,
      currentPage: page,
      totalPages: totalPages,
      totalItems: totalItems,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
      timestamp: DateTime.now(),
    );
  }

  /// Group books efficiently using background processing for large datasets
  Future<List<List<Book>>> _groupBooksEfficiently(List<Book> books) async {
    if (books.length <= 50) {
      // Small dataset - process synchronously
      return _groupBooks(books);
    }

    // Large dataset - use efficient transformation
    return await EfficientDataUtils.transformData<List<Book>>(
      data: books,
      transform: (bookData) {
        final book = bookData as Book;
        return [book]; // This will be properly grouped in the next step
      },
      taskId: 'group_books_${DateTime.now().millisecondsSinceEpoch}',
    ).then((individualBooks) {
      // Now group the individual books
      return _groupBooks(individualBooks.expand((list) => list).toList());
    });
  }

  /// Traditional book grouping logic
  List<List<Book>> _groupBooks(List<Book> books) {
    final groupedBooks = <List<Book>>[];

    for (final book in books) {
      if (book.groupId == 0) {
        groupedBooks.add([book]);
      } else {
        final existingGroupIndex = groupedBooks.indexWhere(
          (group) => group.first.groupId == book.groupId,
        );

        if (existingGroupIndex == -1) {
          groupedBooks.add([book]);
        } else {
          groupedBooks[existingGroupIndex].add(book);
        }
      }
    }

    return groupedBooks;
  }

  /// Load a specific page
  Future<PaginatedResult<List<Book>>> loadPage(int page) async {
    return await _paginationManager.loadPage(page);
  }

  /// Refresh the book list
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    _paginationManager.clearCache();
    _searchCache.clear();

    try {
      final result = await _paginationManager.loadPage(1);
      state = AsyncValue.data(result);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }

    if (kDebugMode) {
      debugPrint('🔄 EfficientBookList: Refreshed');
    }
  }

  /// Search books with caching
  Future<List<List<Book>>> search(String query) async {
    if (query.isEmpty) {
      // Return first page for empty query
      final result = await loadPage(1);
      return result.items;
    }

    // Check cache first
    final cachedResult = _searchCache.get(query);
    if (cachedResult != null) {
      if (kDebugMode) {
        debugPrint('🔍 EfficientBookList: Search cache hit for "$query"');
      }
      return [cachedResult];
    }

    if (kDebugMode) {
      debugPrint('🔍 EfficientBookList: Searching for "$query"');
    }

    // Get all books and filter
    final allBooks = await book_dao.selectNotDeleteBooks();
    final lowerCaseQuery = query.toLowerCase();

    // Filter books efficiently
    final filteredBooks = <Book>[];
    for (final book in allBooks) {
      if (book.title.toLowerCase().contains(lowerCaseQuery) ||
          book.author.toLowerCase().contains(lowerCaseQuery)) {
        filteredBooks.add(book);
      }
    }

    // Cache the filtered results
    _searchCache.put(query, filteredBooks);

    // Group the filtered books
    final groupedBooks = await _groupBooksEfficiently(filteredBooks);

    return groupedBooks;
  }

  /// Get pagination statistics
  Map<String, dynamic> getPaginationStats() {
    return {
      'cachedPages': _paginationManager.cachedPagesCount,
      'estimatedMemoryUsage': _paginationManager.estimatedMemoryUsage,
      'searchCacheStats': _searchCache.getStatistics(),
    };
  }
}

/// Provider instance for efficient book list
final efficientBookListProvider = StateNotifierProvider<
    EfficientBookListNotifier, AsyncValue<PaginatedResult<List<Book>>>>((ref) {
  return EfficientBookListNotifier();
});

/// Optimized provider selectors for specific book list aspects
final efficientBookCountProvider = Provider<int>((ref) {
  return ref.watch(efficientBookListProvider).when(
        data: (paginatedResult) {
          final count = paginatedResult.totalItems;
          if (kDebugMode) {
            debugPrint('📊 EfficientBookCount: $count books total');
          }
          return count;
        },
        loading: () => 0,
        error: (_, __) => 0,
      );
});

final efficientBookGroupCountProvider = Provider<int>((ref) {
  return ref.watch(efficientBookListProvider).when(
        data: (paginatedResult) {
          // For group count, we need to consider all pages
          // This is a simplified version - in practice, you might want to cache this
          final count = paginatedResult.totalItems;
          if (kDebugMode) {
            debugPrint('📊 EfficientBookGroupCount: $count groups total');
          }
          return count;
        },
        loading: () => 0,
        error: (_, __) => 0,
      );
});

final efficientHasBooksProvider = Provider<bool>((ref) {
  return ref.watch(efficientBookListProvider).when(
        data: (paginatedResult) => paginatedResult.totalItems > 0,
        loading: () => false,
        error: (_, __) => false,
      );
});

final efficientIsLoadingProvider = Provider<bool>((ref) {
  return ref.watch(efficientBookListProvider).when(
        data: (_) => false,
        loading: () => true,
        error: (_, __) => false,
      );
});

/// Provider for current page books (for display)
final efficientCurrentPageBooksProvider = Provider<List<List<Book>>>((ref) {
  return ref.watch(efficientBookListProvider).when(
        data: (paginatedResult) => paginatedResult.items,
        loading: () => [],
        error: (_, __) => [],
      );
});

/// Provider for pagination info
final efficientPaginationInfoProvider = Provider<Map<String, dynamic>>((ref) {
  return ref.watch(efficientBookListProvider).when(
        data: (paginatedResult) => {
          'currentPage': paginatedResult.currentPage,
          'totalPages': paginatedResult.totalPages,
          'totalItems': paginatedResult.totalItems,
          'hasNextPage': paginatedResult.hasNextPage,
          'hasPreviousPage': paginatedResult.hasPreviousPage,
          'itemsOnCurrentPage': paginatedResult.items.length,
        },
        loading: () => {
          'currentPage': 0,
          'totalPages': 0,
          'totalItems': 0,
          'hasNextPage': false,
          'hasPreviousPage': false,
          'itemsOnCurrentPage': 0,
        },
        error: (_, __) => {
          'currentPage': 0,
          'totalPages': 0,
          'totalItems': 0,
          'hasNextPage': false,
          'hasPreviousPage': false,
          'itemsOnCurrentPage': 0,
        },
      );
});

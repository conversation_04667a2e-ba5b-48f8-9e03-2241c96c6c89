import 'dart:convert';

import 'package:dasso_reader/models/hsk_character.dart';
import 'package:dasso_reader/models/hsk_character_set.dart';
import 'package:dasso_reader/models/hsk_settings.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:dasso_reader/services/java_practice_adapter.dart'; // Added import
import 'package:dasso_reader/utils/log/common.dart';

part 'hsk_providers.g.dart';

// Available HSK levels provider
@riverpod
Future<List<String>> hskLevels(Ref ref) async {
  // In a real implementation, this might come from an API or a database
  return ['HSK 1', 'HSK 2', 'HSK 3', 'HSK 4', 'HSK 5', 'HSK 6'];
}

// Selected HSK level provider
@riverpod
class CurrentHskLevel extends _$CurrentHskLevel {
  @override
  String build() {
    return 'HSK 1'; // Default level
  }

  void setLevel(String level) {
    state = level;
  }
}

// Character sets for the selected HSK level
@riverpod
Future<List<HskCharacterSet>> hskCharacterSets(Ref ref) async {
  final currentLevel = ref.watch(currentHskLevelProvider);

  try {
    // Extract the level number (e.g., "HSK 1" -> 1)
    final levelNum = int.parse(currentLevel.split(' ')[1]);

    // Load the HSK data from the JSON file
    final jsonString = await rootBundle
        .loadString('assets/hsk-json-all/hsk-level-$levelNum.json');
    final List<dynamic> jsonData = jsonDecode(jsonString) as List<dynamic>;

    // Group characters into sets of 30 for better learning experience
    const charactersPerSet = 30;
    final characterSets = <HskCharacterSet>[];

    // Process all characters from the JSON
    List<HskCharacter> allCharacters = [];
    for (var item in jsonData) {
      // Convert from JSON format to our HskCharacter model
      HskCharacter character = HskCharacter(
        hskLevel: currentLevel,
        characterId: item['id'] as int,
        character: item['hanzi'] as String,
        pinyin: item['pinyin'] as String,
        // Take the first translation as the primary one
        englishTranslation: (item['translations'] as List<dynamic>).isNotEmpty
            ? item['translations'][0] as String
            : '',
        // Remove 'assets/' prefix as AssetSource will add it automatically
        audioAssetPath: 'audio/hsk${levelNum}_opus/${item['id']}.opus',
      );
      allCharacters.add(character);
    }

    // Create character sets with 30 characters each
    for (int i = 0; i < allCharacters.length; i += charactersPerSet) {
      final endIndex = (i + charactersPerSet) > allCharacters.length
          ? allCharacters.length
          : i + charactersPerSet;

      final charactersInSet = allCharacters.sublist(i, endIndex);
      final startId = charactersInSet.first.characterId;
      final endId = charactersInSet.last.characterId;

      characterSets.add(
        HskCharacterSet(
          id: '${currentLevel}_${startId}_$endId',
          hskLevel: currentLevel,
          startId: startId,
          endId: endId,
          characters: charactersInSet,
        ),
      );
    }

    return characterSets;
  } catch (e) {
    throw Exception('Failed to load HSK character sets: $e');
  }
}

// Selected character set provider
@riverpod
class CurrentHskCharacterSet extends _$CurrentHskCharacterSet {
  @override
  Future<HskCharacterSet?> build() async {
    // Initially no set is selected
    return null;
  }

  Future<void> setCharacterSet(HskCharacterSet set) async {
    state = AsyncValue.data(set);
  }
}

// Settings providers
@riverpod
class HskLearnSettingsNotifier extends _$HskLearnSettingsNotifier {
  static const _prefsKey = 'hsk_learn_settings';

  @override
  Future<HskLearnSettings> build() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_prefsKey);

      if (settingsJson != null) {
        try {
          final Map<String, dynamic> map =
              jsonDecode(settingsJson) as Map<String, dynamic>;
          return HskLearnSettings.fromJson(map);
        } catch (e) {
          // If there's an error parsing, return default settings
          return const HskLearnSettings();
        }
      }

      return const HskLearnSettings();
    } catch (e) {
      // If SharedPreferences fails (e.g., in background isolate), return default
      return const HskLearnSettings();
    }
  }

  Future<void> updateSettings(HskLearnSettings settings) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_prefsKey, jsonEncode(settings.toJson()));
    state = AsyncValue.data(settings);
  }

  Future<void> toggleAutoPlaySound() async {
    state.whenData((settings) async {
      final updatedSettings = settings.copyWith(
        autoPlaySound: !settings.autoPlaySound,
      );
      await updateSettings(updatedSettings);
    });
  }

  Future<void> toggleShowEnglishTranslation() async {
    state.whenData((settings) async {
      final updatedSettings = settings.copyWith(
        showEnglishTranslation: !settings.showEnglishTranslation,
      );
      await updateSettings(updatedSettings);
    });
  }

  Future<void> toggleShowPinyinPrompt() async {
    state.whenData((settings) async {
      final updatedSettings = settings.copyWith(
        showPinyinPrompt: !settings.showPinyinPrompt,
      );
      await updateSettings(updatedSettings);
    });
  }

  Future<void> toggleShowPinyinOnButtons() async {
    state.whenData((settings) async {
      final updatedSettings = settings.copyWith(
        showPinyinOnButtons: !settings.showPinyinOnButtons,
      );
      await updateSettings(updatedSettings);
    });
  }

  Future<void> toggleShowTextPrompt() async {
    state.whenData((settings) async {
      final updatedSettings = settings.copyWith(
        showTextPrompt: !settings.showTextPrompt,
      );
      await updateSettings(updatedSettings);
    });
  }

  Future<void> toggleDelayTextPrompt() async {
    state.whenData((settings) async {
      final updatedSettings = settings.copyWith(
        delayTextPrompt: !settings.delayTextPrompt,
      );
      await updateSettings(updatedSettings);
    });
  }
}

@riverpod
class HskPracticeSettingsNotifier extends _$HskPracticeSettingsNotifier {
  static const _prefsKey = 'hsk_practice_settings';

  @override
  Future<HskPracticeSettings> build() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_prefsKey);

      if (settingsJson != null) {
        try {
          final Map<String, dynamic> map =
              jsonDecode(settingsJson) as Map<String, dynamic>;
          return HskPracticeSettings.fromJson(map);
        } catch (e) {
          // If there's an error parsing, return default settings
          return const HskPracticeSettings();
        }
      }

      return const HskPracticeSettings();
    } catch (e) {
      // If SharedPreferences fails (e.g., in background isolate), return default
      return const HskPracticeSettings();
    }
  }

  Future<void> updateSettings(HskPracticeSettings settings) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_prefsKey, jsonEncode(settings.toJson()));
    state = AsyncValue.data(settings);
  }

  Future<void> toggleAutoPlaySound() async {
    state.whenData((settings) async {
      final updatedSettings = settings.copyWith(
        autoPlaySound: !settings.autoPlaySound,
      );
      await updateSettings(updatedSettings);
    });
  }

  Future<void> toggleShowEnglish() async {
    state.whenData((settings) async {
      final updatedSettings = settings.copyWith(
        showEnglish: !settings.showEnglish,
      );
      await updateSettings(updatedSettings);
    });
  }

  Future<void> toggleShowPinyin() async {
    state.whenData((settings) async {
      final updatedSettings = settings.copyWith(
        showPinyin: !settings.showPinyin,
      );
      await updateSettings(updatedSettings);
    });
  }

  Future<void> toggleDelayTextPrompt() async {
    state.whenData((settings) async {
      final updatedSettings = settings.copyWith(
        delayTextPrompt: !settings.delayTextPrompt,
      );
      await updateSettings(updatedSettings);
    });
  }

  Future<void> setTimerDuration(double seconds) async {
    state.whenData((settings) async {
      final updatedSettings = settings.copyWith(
        timerDurationSeconds: seconds,
      );
      await updateSettings(updatedSettings);
    });
  }
}

@riverpod
class HskReviewSettingsNotifier extends _$HskReviewSettingsNotifier {
  static const _prefsKey = 'hsk_review_settings';

  @override
  Future<HskReviewSettings> build() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_prefsKey);

      if (settingsJson != null) {
        try {
          final Map<String, dynamic> map =
              jsonDecode(settingsJson) as Map<String, dynamic>;
          return HskReviewSettings.fromJson(map);
        } catch (e) {
          // If there's an error parsing, return default settings
          return const HskReviewSettings();
        }
      }

      return const HskReviewSettings();
    } catch (e) {
      // If SharedPreferences fails (e.g., in background isolate), return default
      return const HskReviewSettings();
    }
  }

  Future<void> updateSettings(HskReviewSettings settings) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_prefsKey, jsonEncode(settings.toJson()));
    state = AsyncValue.data(settings);
  }

  Future<void> toggleAutoPlaySound() async {
    state.whenData((settings) async {
      final updatedSettings = settings.copyWith(
        autoPlaySound: !settings.autoPlaySound,
      );
      await updateSettings(updatedSettings);
    });
  }

  Future<void> toggleShowPinyin() async {
    state.whenData((settings) async {
      final updatedSettings = settings.copyWith(
        showPinyin: !settings.showPinyin,
      );
      await updateSettings(updatedSettings);
    });
  }

  Future<void> toggleShowEnglish() async {
    state.whenData((settings) async {
      final updatedSettings = settings.copyWith(
        showEnglish: !settings.showEnglish,
      );
      await updateSettings(updatedSettings);
    });
  }
}

// Added this provider for review settings
@riverpod
Future<HskReviewSettings> hskReviewSettings(Ref ref) async {
  final notifier = ref.watch(hskReviewSettingsNotifierProvider.notifier);
  return await notifier.build();
}

// Character progress provider
@riverpod
class CharacterProgress extends _$CharacterProgress {
  static const _prefsKey = 'hsk_character_progress';

  @override
  Future<Map<String, HskCharacter>> build() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final progressJson = prefs.getString(_prefsKey);

      if (progressJson != null) {
        try {
          final Map<String, dynamic> map =
              jsonDecode(progressJson) as Map<String, dynamic>;
          return map.map(
            (key, value) => MapEntry(
              key,
              HskCharacter.fromJson(value as Map<String, dynamic>),
            ),
          );
        } catch (e) {
          // If there's an error parsing, return empty map
          return {};
        }
      }

      return {};
    } catch (e) {
      // If SharedPreferences fails (e.g., in background isolate), return empty map
      return {};
    }
  }

  Future<void> updateCharacter(HskCharacter character) async {
    final key = '${character.hskLevel}_${character.characterId}';

    state.whenData((progress) async {
      final updatedProgress = {...progress, key: character};
      final prefs = await SharedPreferences.getInstance();

      final Map<String, dynamic> jsonMap = updatedProgress.map(
        (key, value) => MapEntry(key, value.toJson()),
      );

      await prefs.setString(_prefsKey, jsonEncode(jsonMap));
      state = AsyncValue.data(updatedProgress);
    });
  }

  Future<void> incrementViewCount(HskCharacter character) async {
    final updatedCharacter = character.copyWith(
      viewCount: character.viewCount + 1,
    );
    await updateCharacter(updatedCharacter);
  }

  Future<void> updateLearnCycleState(
    HskCharacter character,
    int newState,
  ) async {
    final updatedCharacter = character.copyWith(
      learnCycleState: newState,
    );
    await updateCharacter(updatedCharacter);
  }

  Future<void> markAsMastered(HskCharacter character) async {
    final updatedCharacter = character.copyWith(
      isMastered: true,
    );
    await updateCharacter(updatedCharacter);
  }

  Future<void> updatePracticeStreak(
    HskCharacter character,
    bool correct, {
    DateTime? timestamp,
  }) async {
    final now = timestamp ?? DateTime.now();

    if (correct) {
      final updatedCharacter = character.copyWith(
        practiceCorrectStreak: character.practiceCorrectStreak + 1,
        lastPracticedCorrectly: now,
      );
      await updateCharacter(updatedCharacter);
    } else {
      final updatedCharacter = character.copyWith(
        practiceCorrectStreak: 0,
      );
      await updateCharacter(updatedCharacter);
    }
  }
}

// Session progress provider for learning/practice modes
@riverpod
class HskSessionProgress extends _$HskSessionProgress {
  @override
  Map<String, dynamic> build() {
    return {
      'mode': null, // 'learn' or 'practice'
      'currentIndex': 0,
      'correctAnswers': 0,
      'totalQuestions': 0,
      'startTime': null,
      'endTime': null,
    };
  }

  void startLearnSession(int totalQuestions) {
    state = {
      'mode': 'learn',
      'currentIndex': 0,
      'correctAnswers': 0,
      'totalQuestions': totalQuestions,
      'startTime': DateTime.now().millisecondsSinceEpoch,
      'endTime': null,
    };
  }

  void startPracticeSession(int totalQuestions) {
    state = {
      'mode': 'practice',
      'currentIndex': 0,
      'correctAnswers': 0,
      'totalQuestions': totalQuestions,
      'startTime': DateTime.now().millisecondsSinceEpoch,
      'endTime': null,
    };
  }

  void incrementCorrectAnswers() {
    state = {
      ...state,
      'correctAnswers': state['correctAnswers'] + 1,
    };
  }

  void nextQuestion() {
    state = {
      ...state,
      'currentIndex': state['currentIndex'] + 1,
    };
  }

  void endSession() {
    state = {
      ...state,
      'endTime': DateTime.now().millisecondsSinceEpoch,
    };
  }

  int getSessionDurationSeconds() {
    final startTime = state['startTime'] as int?;
    final endTime =
        (state['endTime'] as int?) ?? DateTime.now().millisecondsSinceEpoch;

    if (startTime == null) return 0;

    return ((endTime - startTime) / 1000).round();
  }
}

// Audio player provider for playing character pronunciations
@riverpod
class HskAudioPlayer extends _$HskAudioPlayer {
  @override
  AudioPlayerState build() {
    return AudioPlayerState(isPlaying: false, currentAsset: null);
  }

  void playAudio(String assetPath) {
    state = AudioPlayerState(isPlaying: true, currentAsset: assetPath);

    // Normally, you would use the audioplayers package to play the audio
    // For this implementation, we'll just update the state
    // In a real implementation, you would load and play the asset

    // Simulate audio playback completion after 1 second
    Future.delayed(const Duration(seconds: 1), () {
      state = AudioPlayerState(isPlaying: false, currentAsset: null);
    });
  }
}

class AudioPlayerState {
  final bool isPlaying;
  final String? currentAsset;

  AudioPlayerState({required this.isPlaying, this.currentAsset});
}

// HSK Practice Mode Metrics Provider
final javaPracticeAdapterProvider = StateNotifierProvider.autoDispose<
    JavaPracticeAdapterNotifier, JavaPracticeAdapter?>((ref) {
  return JavaPracticeAdapterNotifier(null);
});

class JavaPracticeAdapterNotifier extends StateNotifier<JavaPracticeAdapter?> {
  JavaPracticeAdapterNotifier(super.adapter);

  void initializeAdapter(List<HskCharacter> characters) {
    if (characters.length == 30) {
      state = JavaPracticeAdapter(hskCharacters: characters);
    } else {
      AnxLog.severe(
        'Error: JavaPracticeAdapter requires 30 characters, received ${characters.length}',
      );
      state = null;
    }
  }

  void _forceUpdate() {
    if (state != null) {
      // Create a new instance of the adapter with the same characters,
      // then manually copy all mutable state fields from the old state to the new one.
      // This ensures Riverpod detects a change because the state object instance is new.
      final newAdapterState =
          JavaPracticeAdapter(hskCharacters: state!.masterHskCharacterList);
      newAdapterState.cArr = List.from(state!.cArr);
      newAdapterState.sArr = List.from(state!.sArr);
      newAdapterState.pLast = List.from(state!.pLast);
      newAdapterState.curRun = List.from(state!.curRun);
      newAdapterState.dArr = List.from(state!.dArr);
      newAdapterState.countVar = state!.countVar;
      newAdapterState.stCount = state!.stCount;
      newAdapterState.sdCount = state!.sdCount;
      newAdapterState.unknownCount = state!.unknownCount;
      newAdapterState.timeDelayMs = state!.timeDelayMs;
      newAdapterState.finished = state!.finished;
      newAdapterState.sdFlag = state!.sdFlag;
      newAdapterState.sdMode = state!.sdMode;
      newAdapterState.missFlag = state!.missFlag;
      newAdapterState.nLoop = state!.nLoop;
      newAdapterState.rVar = state!.rVar;
      newAdapterState.currentLim2 = state!.currentLim2;
      state = newAdapterState;
    }
  }

  void resetAdapter() {
    state = null;
  }

  JavaPracticeRound? getRound() {
    if (state == null) {
      AnxLog.warning('JavaPracticeAdapter not initialized in provider.');
      return null;
    }
    final round = state!.getRound();
    _forceUpdate();
    return round;
  }

  void hit(int characterMasterListIndex) {
    if (state == null) return;
    state!.hit(characterMasterListIndex);
    _forceUpdate();
  }

  void miss(int characterMasterListIndex) {
    if (state == null) return;
    state!.miss(characterMasterListIndex);
    _forceUpdate();
  }

  void acknowledgeSdFlag() {
    if (state == null) return;
    state!.acknowledgeSdFlag();
    _forceUpdate();
  }

  // Expose relevant getters from the adapter
  int get progress => state?.getProgress() ?? 0;
  int get currentRoundNumber => state?.currentRoundNumberPublicGetter ?? 0;
  int get totalCharactersToMaster => state?.totalCharactersToMaster ?? 30;
  int get successfulSdRounds => state?.successfulSdRounds ?? 0;
  int get standardLearnedCount => state?.standardLearnedCount ?? 0;
  bool get isSuddenDeathActive => state?.isSuddenDeathActive ?? false;
  bool get isSuddenDeathPending => state?.isSuddenDeathPending ?? false;
  bool get isFinished => state?.finished ?? true;
  int get timeDelayMs => state?.timeDelayMs ?? 1000;
}

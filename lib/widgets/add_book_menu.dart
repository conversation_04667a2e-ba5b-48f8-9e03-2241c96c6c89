import 'package:flutter/material.dart';

/// A floating menu widget for the add book options.
Widget showAddBookMenu({
  required BuildContext context,
  required VoidCallback onPasteText,
  required VoidCallback onImportFile,
}) {
  final colorScheme = Theme.of(context).colorScheme;
  final size = MediaQuery.of(context).size;

  // Calculate appropriate sizes based on screen dimensions
  final double menuWidth = size.width > 600 ? 500 : size.width * 0.85;
  const double itemPadding = 16.0;
  const double itemSpacing = 16.0;

  return AlertDialog(
    backgroundColor: colorScheme.surface,
    surfaceTintColor: colorScheme.surfaceTint,
    elevation: 8,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(28),
    ),
    title: Text(
      'Add Book',
      style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.bold,
          ),
      textAlign: TextAlign.center,
    ),
    content: SizedBox(
      width: menuWidth,
      child: LayoutBuilder(
        builder: (context, constraints) {
          // Calculate item width based on available space
          final double actualItemWidth =
              (constraints.maxWidth - itemSpacing) / 2;

          return Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildMenuOption(
                context: context,
                icon: Icons.edit_note_rounded,
                label: 'Paste Text',
                width: actualItemWidth,
                onTap: () {
                  Navigator.of(context).pop();
                  onPasteText();
                },
              ),
              const SizedBox(width: itemSpacing),
              _buildMenuOption(
                context: context,
                icon: Icons.file_download_outlined,
                label: 'Import File',
                width: actualItemWidth,
                onTap: () {
                  Navigator.of(context).pop();
                  onImportFile();
                },
              ),
            ],
          );
        },
      ),
    ),
    contentPadding: const EdgeInsets.all(itemPadding),
    // No actions to keep a clean design
  );
}

Widget _buildMenuOption({
  required BuildContext context,
  required IconData icon,
  required String label,
  required double width,
  required VoidCallback onTap,
}) {
  final colorScheme = Theme.of(context).colorScheme;

  return InkWell(
    onTap: onTap,
    borderRadius: BorderRadius.circular(20),
    child: Container(
      width: width,
      height: width,
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.4),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 64,
            height: 64,
            decoration: BoxDecoration(
              color: colorScheme.surface,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: colorScheme.shadow.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
              border: Border.all(
                color: colorScheme.primary.withValues(alpha: 0.2),
                width: 1.5,
              ),
            ),
            child: Icon(
              icon,
              size: 32,
              color: colorScheme.primary,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            label,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: colorScheme.onSurface,
                  fontWeight: FontWeight.w500,
                ),
          ),
        ],
      ),
    ),
  );
}

import 'package:flutter/material.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';
import 'package:dasso_reader/config/app_icons.dart';

AppBar settingsAppBar(String title, BuildContext context) {
  final theme = Theme.of(context);
  return AppBar(
    leading: Icon<PERSON>utton(
      icon: Icon(
        AdaptiveIcons.back,
        size: AppIcons.sizeM, // Use chevron back button with proper size
      ),
      onPressed: () {
        Navigator.pop(context);
      },
    ),
    title: Text(title),
    backgroundColor: theme.appBarTheme.backgroundColor,
    foregroundColor: theme.appBarTheme.foregroundColor,
  );
}

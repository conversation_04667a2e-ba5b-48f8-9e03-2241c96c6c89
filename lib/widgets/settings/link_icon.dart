import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:dasso_reader/config/design_system.dart';

Widget linkIcon({
  required Widget icon,
  required String url,
  required LaunchMode mode,
  double? size, // Made nullable to use DesignSystem default
  String? tooltip, // Add tooltip for accessibility
  String? semanticLabel, // Add semantic label for screen readers
}) {
  // Use DesignSystem constant with manufacturer adjustment
  final adjustedSize = size ?? DesignSystem.getAdjustedIconSize(30.0);
  return IconButton(
    onPressed: () => launchUrl(
      Uri.parse(url),
      mode: mode,
    ),
    tooltip: tooltip ?? 'Open link', // Default tooltip if none provided
    icon: SizedBox(
      width: adjustedSize,
      height: adjustedSize,
      child: semanticLabel != null
          ? Semantics(
              label: semanticLabel,
              button: true,
              child: icon,
            )
          : icon,
    ),
  );
}

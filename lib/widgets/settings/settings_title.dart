import 'package:dasso_reader/main.dart';
import 'package:dasso_reader/widgets/settings/settings_section.dart';

import 'package:dasso_reader/widgets/common/adaptive_components.dart';
import 'package:dasso_reader/config/adaptive_icons.dart';
import 'package:dasso_reader/utils/accessibility/semantic_helpers.dart';
import 'package:flutter/cupertino.dart';

Widget settingsTitle({
  required Icon icon,
  required String title,
  required bool isMobile,
  required int id,
  required int selectedIndex,
  required Function setDetail,
  required Widget subPage,
  required List<String> subtitle,
}) {
  BuildContext context = navigatorKey.currentContext!;

  // Create semantic label with context
  final subtitleText = subtitle.join(' • ');
  final semanticLabel = '$title settings';

  return SemanticHelpers.listItem(
    child: AdaptiveListTile(
      leading: icon,
      title: Text(title),
      trailing: Icon(AdaptiveIcons.chevronRight),
      selected: !isMobile && selectedIndex == id,
      subtitle: Text(subtitleText),
      onTap: () {
        if (!isMobile) {
          setDetail(subPage, id);
          return;
        }
        Navigator.push(
          context,
          CupertinoPageRoute<void>(
            fullscreenDialog: false,
            builder: (context) => subPage,
          ),
        );
      },
    ),
    label: semanticLabel,
    onTap: () {
      if (!isMobile) {
        setDetail(subPage, id);
        return;
      }
      Navigator.push(
        context,
        CupertinoPageRoute<void>(
          fullscreenDialog: false,
          builder: (context) => subPage,
        ),
      );
    },
  );
}

Widget settingsSections({
  required List<AbstractSettingsSection> sections,
}) {
  // return SettingsList(sections: sections);
  return ListView.builder(
    itemCount: sections.length,
    itemBuilder: (context, index) {
      return sections[index];
    },
  );
}

import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/widgets/dictionary/accessible_dictionary_tab.dart';
import 'package:flutter/material.dart';

/// A specialized dictionary component specifically for use in the main dictionary screen
/// This provides separation of concerns from the context menu dictionary
class MainDictionaryTab extends StatefulWidget {
  /// The text to look up in the dictionary
  final String text;

  /// Whether to limit the number of definitions shown
  final bool limitDefinitions;

  const MainDictionaryTab({
    super.key,
    required this.text,
    this.limitDefinitions = false,
  });

  @override
  State<MainDictionaryTab> createState() => _MainDictionaryTabState();
}

class _MainDictionaryTabState extends State<MainDictionaryTab> {
  @override
  Widget build(BuildContext context) {
    AnxLog.info('Building main dictionary tab for text: "${widget.text}"');

    // Use the AccessibleDictionaryTab with main screen specific settings
    return AccessibleDictionaryTab(
      text: widget.text,
      showTabBar: true,
      initialTabIndex: 0,
      limitDefinitions: widget.limitDefinitions,
      // Main screen specific settings
      compactMode: false,
      showStrokeAnimations:
          true, // Explicitly enable stroke animations for main dictionary
    );
  }
}

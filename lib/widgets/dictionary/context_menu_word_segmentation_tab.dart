import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/l10n/generated/L10n.dart';
import 'package:dasso_reader/service/dictionary/chinese_segmentation_service.dart';
import 'package:dasso_reader/service/dictionary/dictionary_service.dart';
import 'package:dasso_reader/models/dictionary/dictionary_entry.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// A specialized word segmentation component for the context menu
/// Displays Chinese word segmentation options in a user-friendly scrollable interface
class ContextMenuWordSegmentationTab extends StatefulWidget {
  /// The selected text to segment
  final String selectedText;

  /// The book ID for retrieving cached segmentation data
  final int bookId;

  /// Selection range information
  final int startOffset;
  final int endOffset;

  const ContextMenuWordSegmentationTab({
    super.key,
    required this.selectedText,
    required this.bookId,
    required this.startOffset,
    required this.endOffset,
  });

  @override
  State<ContextMenuWordSegmentationTab> createState() =>
      _ContextMenuWordSegmentationTabState();
}

class _ContextMenuWordSegmentationTabState
    extends State<ContextMenuWordSegmentationTab> {
  bool _isLoading = true;
  Map<String, dynamic>? _segmentationData;
  List<Map<String, dynamic>> _wordOptions = [];
  final DictionaryService _dictionaryService = DictionaryService();

  @override
  void initState() {
    super.initState();
    _loadSegmentationData();
  }

  /// Load segmentation data from the service
  Future<void> _loadSegmentationData() async {
    try {
      final segmentationService = ChineseSegmentationService();
      await segmentationService.initialize();

      // Try to get stored segmentation data first
      final storedData = segmentationService.getStoredSegmentationData(
        selectedText: widget.selectedText,
        startOffset: widget.startOffset,
        endOffset: widget.endOffset,
        bookId: widget.bookId,
      );

      if (storedData != null) {
        _segmentationData = storedData;
        await _processSegmentationData();
      } else {
        // If no stored data, generate it on the fly
        await _generateSegmentationData();
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      AnxLog.severe('Error loading segmentation data: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Generate segmentation data if not available
  Future<void> _generateSegmentationData() async {
    try {
      final segmentationService = ChineseSegmentationService();

      // Generate comprehensive segmentation data
      await segmentationService.storeSegmentationDataForSelection(
        selectedText: widget.selectedText,
        fullNodeText: widget.selectedText, // Use selected text as context
        startOffset: 0,
        endOffset: widget.selectedText.length,
        selectionRange: [0, widget.selectedText.length],
        bookId: widget.bookId,
      );

      // Retrieve the generated data
      _segmentationData = segmentationService.getStoredSegmentationData(
        selectedText: widget.selectedText,
        startOffset: widget.startOffset,
        endOffset: widget.endOffset,
        bookId: widget.bookId,
      );

      await _processSegmentationData();
    } catch (e) {
      AnxLog.severe('Error generating segmentation data: $e');
    }
  }

  /// Process segmentation data into word options with dictionary entries
  Future<void> _processSegmentationData() async {
    if (_segmentationData == null) return;

    // Initialize dictionary service
    await _dictionaryService.initialize();

    final List<Map<String, dynamic>> options = [];

    // Add segmentation options
    final segmentationOptions =
        _segmentationData!['segmentationOptions'] as List<dynamic>? ?? [];
    for (final option in segmentationOptions) {
      final optionMap = option as Map<String, dynamic>;
      final word = optionMap['word'] as String;

      // Skip if it's the same as the original text or single character
      if (word != widget.selectedText &&
          word.trim().isNotEmpty &&
          word.length > 1) {
        final entry = await _dictionaryService.lookupChinese(word);
        options.add({
          'word': word,
          'type': optionMap['type'] ?? 'segmentation',
          'direction': optionMap['direction'] ?? 'unknown',
          'isSelected': false,
          'entry': entry,
        });
      }
    }

    // Add possible words from boundary detection
    final possibleWords =
        _segmentationData!['possibleWords'] as List<dynamic>? ?? [];
    for (final wordData in possibleWords) {
      final wordMap = wordData as Map<String, dynamic>;
      final word = wordMap['word'] as String;

      // Skip if already added, same as original, or single character
      if (word != widget.selectedText &&
          word.trim().isNotEmpty &&
          word.length > 1 &&
          !options.any((opt) => opt['word'] == word)) {
        final entry = await _dictionaryService.lookupChinese(word);
        options.add({
          'word': word,
          'type': 'boundary',
          'direction': 'context',
          'isSelected': false,
          'isExactMatch': wordMap['isExactMatch'] ?? false,
          'containsSelection': wordMap['containsSelection'] ?? false,
          'entry': entry,
        });
      }
    }

    _wordOptions = options;

    // Set first item as selected if any options exist
    if (_wordOptions.isNotEmpty) {
      _wordOptions[0]['isSelected'] = true;
    }
  }

  /// Handle word selection
  void _onWordSelected(String word) {
    // Copy the selected word to clipboard
    Clipboard.setData(ClipboardData(text: word));

    // Show feedback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${L10n.of(context).notes_page_copied}: "$word"'),
        duration: const Duration(seconds: 2),
      ),
    );

    // Update selection state
    setState(() {
      for (var option in _wordOptions) {
        option['isSelected'] = option['word'] == word;
      }
    });
  }

  /// Check if text is Chinese
  bool _isChinese(String text) {
    return RegExp(r'[\u4e00-\u9fa5]').hasMatch(text);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final readingTextColor =
        Color(int.parse('0x${Prefs().readTheme.textColor}'));

    if (_isLoading) {
      return Container(
        padding: const EdgeInsets.all(DesignSystem.spaceM), // 16.0 equivalent
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
            const SizedBox(height: DesignSystem.spaceS), // 8.0 equivalent
            Text(
              'Analyzing word boundaries...',
              style: theme.textTheme.bodySmall?.copyWith(
                color: readingTextColor.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      );
    }

    // If no Chinese text or no segmentation data
    if (!_isChinese(widget.selectedText) || _wordOptions.isEmpty) {
      return Container(
        padding: const EdgeInsets.symmetric(
          vertical: DesignSystem.spaceS + 4, // 12.0 equivalent (8.0 + 4.0)
          horizontal: DesignSystem.spaceS, // 8.0 equivalent
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.info_outline,
              color: readingTextColor.withValues(alpha: 0.5),
              size: 20,
            ),
            const SizedBox(
              height: DesignSystem.spaceXS + 2,
            ), // 6.0 equivalent (4.0 + 2.0)
            Text(
              'No word segmentation available',
              style: theme.textTheme.bodySmall?.copyWith(
                color: readingTextColor.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: DesignSystem.spaceXS, // 4.0 equivalent
        vertical: DesignSystem.spaceXS / 2, // 2.0 equivalent
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Compact scrollable word options with definitions
          Column(
            mainAxisSize: MainAxisSize.min,
            children:
                _wordOptions.map((option) => _buildWordItem(option)).toList(),
          ),
        ],
      ),
    );
  }

  /// Build a word item with definition
  Widget _buildWordItem(Map<String, dynamic> option) {
    final word = option['word'] as String;
    final entry = option['entry'] as DictionaryEntry?;
    final isSelected = option['isSelected'] as bool;
    final theme = Theme.of(context);
    final readingTextColor =
        Color(int.parse('0x${Prefs().readTheme.textColor}'));

    return Container(
      margin:
          const EdgeInsets.only(bottom: DesignSystem.spaceXS), // 4.0 equivalent
      child: InkWell(
        onTap: () => _onWordSelected(word),
        borderRadius: BorderRadius.circular(
          DesignSystem.spaceXS + 2,
        ), // 6.0 equivalent (4.0 + 2.0)
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: DesignSystem.spaceS, // 8.0 equivalent
            vertical: DesignSystem.spaceXS + 2, // 6.0 equivalent (4.0 + 2.0)
          ),
          decoration: BoxDecoration(
            color: isSelected
                ? theme.colorScheme.primary.withValues(alpha: 0.1)
                : readingTextColor.withValues(alpha: 0.03),
            borderRadius: BorderRadius.circular(
              DesignSystem.spaceXS + 2,
            ), // 6.0 equivalent (4.0 + 2.0)
            border: Border.all(
              color: isSelected
                  ? theme.colorScheme.primary
                  : readingTextColor.withValues(alpha: 0.1),
              width: isSelected ? 1.2 : 0.5,
            ),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Word display - more compact
              Container(
                constraints: const BoxConstraints(minWidth: 50),
                child: Text(
                  word,
                  style: theme.textTheme.titleSmall?.copyWith(
                    color: isSelected
                        ? theme.colorScheme.primary
                        : readingTextColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: DesignSystem.spaceS), // 8.0 equivalent

              // Definition content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (entry != null) ...[
                      // Pinyin and HSK badge inline
                      if (entry.pinyin.isNotEmpty)
                        Row(
                          children: [
                            Text(
                              entry.formattedPinyin(),
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.primary,
                                fontStyle: FontStyle.italic,
                              ),
                            ),
                            // HSK badge inline with pinyin
                            if (entry.hskLevel != null &&
                                entry.hskLevel! > 0) ...[
                              const SizedBox(
                                width: DesignSystem.spaceS,
                              ), // 8.0 equivalent
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: DesignSystem.spaceXS +
                                      2, // 6.0 equivalent (4.0 + 2.0)
                                  vertical: DesignSystem.spaceXS /
                                      2, // 2.0 equivalent
                                ),
                                decoration: BoxDecoration(
                                  color: theme.colorScheme.tertiary
                                      .withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(
                                    DesignSystem.spaceXS,
                                  ), // 4.0 equivalent
                                ),
                                child: Text(
                                  'HSK ${entry.hskLevel!}',
                                  style: theme.textTheme.labelSmall?.copyWith(
                                    color: theme.colorScheme.tertiary,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                      const SizedBox(
                        height: DesignSystem.spaceXS / 2,
                      ), // 2.0 equivalent

                      // Definitions (show first 2) - more compact
                      ...entry.definitions.take(2).map(
                            (definition) => Padding(
                              padding: const EdgeInsets.only(
                                bottom: DesignSystem.spaceXS / 4,
                              ), // 1.0 equivalent
                              child: Text(
                                '• $definition',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color:
                                      readingTextColor.withValues(alpha: 0.8),
                                  fontSize: 11,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ),
                    ] else ...[
                      Text(
                        'No definition available',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: readingTextColor.withValues(alpha: 0.5),
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ],
                ),
              ),

              // Selection indicator
              if (isSelected)
                Icon(
                  Icons.check_circle,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
            ],
          ),
        ),
      ),
    );
  }
}

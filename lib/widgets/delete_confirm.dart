import 'package:flutter/material.dart';
import 'package:dasso_reader/utils/accessibility/semantic_helpers.dart';

class DeleteConfirm extends StatefulWidget {
  const DeleteConfirm({
    super.key,
    required this.delete,
    required this.deleteIcon,
    required this.confirmIcon,
  });

  final Function delete;
  final Widget deleteIcon;
  final Widget confirmIcon;

  @override
  State<DeleteConfirm> createState() => _DeleteConfirmState();
}

class _DeleteConfirmState extends State<DeleteConfirm> {
  bool isDelete = false;

  @override
  Widget build(BuildContext context) {
    return SemanticHelpers.button(
      context: context,
      label: isDelete ? 'Confirm deletion' : 'Delete item',
      hint: isDelete
          ? 'Tap to confirm and permanently delete this item'
          : 'Tap to delete this item. You will be asked to confirm.',
      onTap: () {
        if (isDelete) {
          widget.delete();
          setState(() {
            isDelete = false;
          });
        } else {
          setState(() {
            isDelete = true;
          });
        }
      },
      child: IconButton(
        onPressed: () {
          if (isDelete) {
            widget.delete();
            setState(() {
              isDelete = false;
            });
          } else {
            setState(() {
              isDelete = true;
            });
          }
        },
        icon: isDelete ? widget.confirmIcon : widget.deleteIcon,
      ),
    );
  }
}

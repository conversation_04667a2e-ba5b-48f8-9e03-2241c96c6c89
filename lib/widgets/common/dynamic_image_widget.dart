import 'dart:io';
import 'package:flutter/material.dart';
import 'package:dasso_reader/config/responsive_system.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/color_system.dart';
import 'package:dasso_reader/enums/dynamic_content_types.dart';

/// A comprehensive image widget that adapts to different content types and screen sizes
///
/// This widget automatically handles:
/// - Aspect ratio based on content type
/// - Size constraints based on screen size
/// - Loading states and error handling
/// - Placeholder images
/// - Accessibility features
class DynamicImageWidget extends StatelessWidget {
  const DynamicImageWidget({
    super.key,
    this.imageProvider,
    this.imagePath,
    this.imageUrl,
    this.contentType = ImageContentType.general,
    this.loadingState = LoadingState.initial,
    this.fit,
    this.placeholder,
    this.errorWidget,
    this.width,
    this.height,
    this.aspectRatio,
    this.borderRadius,
    this.semanticsLabel,
    this.onTap,
    this.heroTag,
    this.showLoadingIndicator = true,
    this.backgroundColor,
  });

  /// Image provider (takes precedence over path and URL)
  final ImageProvider? imageProvider;

  /// Local file path
  final String? imagePath;

  /// Network image URL
  final String? imageUrl;

  /// The type of image content
  final ImageContentType contentType;

  /// Current loading state
  final LoadingState loadingState;

  /// How the image should fit within its bounds
  final BoxFit? fit;

  /// Widget to show while loading
  final Widget? placeholder;

  /// Widget to show on error
  final Widget? errorWidget;

  /// Fixed width (overrides adaptive sizing)
  final double? width;

  /// Fixed height (overrides adaptive sizing)
  final double? height;

  /// Fixed aspect ratio (overrides adaptive calculation)
  final double? aspectRatio;

  /// Border radius for the image
  final BorderRadius? borderRadius;

  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Tap callback
  final VoidCallback? onTap;

  /// Hero tag for hero animations
  final String? heroTag;

  /// Whether to show loading indicator
  final bool showLoadingIndicator;

  /// Background color for the container
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    // Get adaptive constraints and aspect ratio
    final constraints = ResponsiveSystem.getAdaptiveImageConstraints(
      context,
      contentType: contentType,
    );

    final effectiveAspectRatio = aspectRatio ??
        ResponsiveSystem.getAdaptiveImageAspectRatio(
          context,
          contentType: contentType,
        );

    final effectiveFit = fit ?? _getDefaultFitForContentType();

    Widget imageWidget = _buildImageContent(context, effectiveFit);

    // Apply size constraints
    if (width != null || height != null) {
      imageWidget = SizedBox(
        width: width,
        height: height,
        child: imageWidget,
      );
    } else {
      imageWidget = ConstrainedBox(
        constraints: constraints,
        child: AspectRatio(
          aspectRatio: effectiveAspectRatio,
          child: imageWidget,
        ),
      );
    }

    // Apply border radius
    if (borderRadius != null) {
      imageWidget = ClipRRect(
        borderRadius: borderRadius!,
        child: imageWidget,
      );
    }

    // Apply hero animation if specified
    if (heroTag != null) {
      imageWidget = Hero(
        tag: heroTag!,
        child: imageWidget,
      );
    }

    // Apply tap gesture
    if (onTap != null) {
      imageWidget = GestureDetector(
        onTap: onTap,
        child: imageWidget,
      );
    }

    // Apply semantics
    if (semanticsLabel != null) {
      imageWidget = Semantics(
        label: semanticsLabel,
        child: imageWidget,
      );
    }

    return imageWidget;
  }

  Widget _buildImageContent(BuildContext context, BoxFit fit) {
    switch (loadingState) {
      case LoadingState.loading:
        return _buildLoadingWidget(context);

      case LoadingState.error:
        return _buildErrorWidget(context);

      case LoadingState.empty:
        return _buildPlaceholderWidget(context);

      case LoadingState.success:
      case LoadingState.initial:
      case LoadingState.refreshing:
        return _buildImage(context, fit);
    }
  }

  Widget _buildImage(BuildContext context, BoxFit fit) {
    ImageProvider? provider;

    if (imageProvider != null) {
      provider = imageProvider!;
    } else if (imagePath != null) {
      final file = File(imagePath!);
      if (file.existsSync()) {
        provider = FileImage(file);
      }
    } else if (imageUrl != null) {
      provider = NetworkImage(imageUrl!);
    }

    if (provider == null) {
      return _buildPlaceholderWidget(context);
    }

    return Container(
      color: backgroundColor,
      child: Image(
        image: provider,
        fit: fit,
        loadingBuilder: showLoadingIndicator
            ? (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return _buildLoadingWidget(context);
              }
            : null,
        errorBuilder: (context, error, stackTrace) {
          return _buildErrorWidget(context);
        },
      ),
    );
  }

  Widget _buildLoadingWidget(BuildContext context) {
    if (placeholder != null) return placeholder!;

    return Container(
      color: backgroundColor ??
          Theme.of(context).colorScheme.surfaceContainerHighest,
      child: Center(
        child: CircularProgressIndicator(
          strokeWidth: 2,
          color: Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }

  Widget _buildErrorWidget(BuildContext context) {
    if (errorWidget != null) return errorWidget!;

    return Container(
      color: backgroundColor ?? Theme.of(context).colorScheme.errorContainer,
      child: Center(
        child: Icon(
          Icons.error_outline,
          color: Theme.of(context).colorScheme.error,
          size: _getIconSizeForContentType(),
        ),
      ),
    );
  }

  Widget _buildPlaceholderWidget(BuildContext context) {
    if (placeholder != null) return placeholder!;

    final (icon, color) = _getPlaceholderIconAndColor(context);

    return Container(
      color: backgroundColor ?? color,
      child: Center(
        child: Icon(
          icon,
          color: ColorSystem.getContrastingColor(color),
          size: _getIconSizeForContentType(),
        ),
      ),
    );
  }

  BoxFit _getDefaultFitForContentType() {
    switch (contentType) {
      case ImageContentType.bookCover:
        return BoxFit.cover;
      case ImageContentType.avatar:
        return BoxFit.cover;
      case ImageContentType.banner:
        return BoxFit.cover;
      case ImageContentType.thumbnail:
        return BoxFit.cover;
      case ImageContentType.icon:
        return BoxFit.contain;
      case ImageContentType.hero:
        return BoxFit.cover;
      case ImageContentType.gallery:
        return BoxFit.contain;
      case ImageContentType.background:
        return BoxFit.cover;
      case ImageContentType.general:
        return BoxFit.contain;
    }
  }

  (IconData, Color) _getPlaceholderIconAndColor(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    switch (contentType) {
      case ImageContentType.bookCover:
        return (Icons.book, colorScheme.primaryContainer);
      case ImageContentType.avatar:
        return (Icons.person, colorScheme.secondaryContainer);
      case ImageContentType.banner:
        return (Icons.image, colorScheme.tertiaryContainer);
      case ImageContentType.thumbnail:
        return (Icons.image_outlined, colorScheme.surfaceContainerHighest);
      case ImageContentType.icon:
        return (Icons.image_not_supported, colorScheme.outline);
      case ImageContentType.hero:
        return (Icons.landscape, colorScheme.primaryContainer);
      case ImageContentType.gallery:
        return (Icons.photo_library, colorScheme.secondaryContainer);
      case ImageContentType.background:
        return (Icons.wallpaper, colorScheme.surfaceContainerHighest);
      case ImageContentType.general:
        return (Icons.image, colorScheme.surfaceContainerHighest);
    }
  }

  double _getIconSizeForContentType() {
    switch (contentType) {
      case ImageContentType.avatar:
      case ImageContentType.icon:
        return DesignSystem
            .widgetIconSizeMedium; // 24.0 -> 28.0 for consistency
      case ImageContentType.thumbnail:
        return DesignSystem.widgetIconSizeLarge; // 32.0
      case ImageContentType.bookCover:
        return DesignSystem.getAdjustedIconSize(
          40.0,
        ); // Use adjusted size for manufacturer consistency
      case ImageContentType.banner:
      case ImageContentType.hero:
      case ImageContentType.gallery:
      case ImageContentType.background:
      case ImageContentType.general:
        return DesignSystem.getAdjustedIconSize(
          48.0,
        ); // Use adjusted size for manufacturer consistency
    }
  }
}

/// A specialized image widget for book covers with fallback colors
class BookCoverImageWidget extends StatelessWidget {
  const BookCoverImageWidget({
    super.key,
    required this.imagePath,
    required this.bookTitle,
    this.width,
    this.height,
    this.borderRadius,
    this.onTap,
    this.heroTag,
  });

  final String imagePath;
  final String bookTitle;
  final double? width;
  final double? height;
  final BorderRadius? borderRadius;
  final VoidCallback? onTap;
  final String? heroTag;

  @override
  Widget build(BuildContext context) {
    final file = File(imagePath);
    final exists = file.existsSync();

    return DynamicImageWidget(
      imagePath: exists ? imagePath : null,
      contentType: ImageContentType.bookCover,
      loadingState: exists ? LoadingState.success : LoadingState.empty,
      width: width,
      height: height,
      borderRadius: borderRadius ?? BorderRadius.circular(DesignSystem.radiusM),
      onTap: onTap,
      heroTag: heroTag,
      placeholder: _buildBookPlaceholder(context),
      semanticsLabel: 'Book cover for $bookTitle',
    );
  }

  Widget _buildBookPlaceholder(BuildContext context) {
    // Generate consistent color based on book title
    final colorIndex = bookTitle.hashCode % Colors.primaries.length;
    final backgroundColor = Colors.primaries[colorIndex].shade200;

    return Container(
      color: backgroundColor,
      child: Center(
        child: Icon(
          Icons.book,
          size: 40,
          color: ColorSystem.getContrastingColor(backgroundColor),
        ),
      ),
    );
  }
}

/// A grid of images with adaptive sizing
class DynamicImageGrid extends StatelessWidget {
  const DynamicImageGrid({
    super.key,
    required this.images,
    this.contentType = ImageContentType.gallery,
    this.onImageTap,
    this.crossAxisCount,
    this.aspectRatio,
    this.spacing,
  });

  final List<String> images;
  final ImageContentType contentType;
  final void Function(int index, String imagePath)? onImageTap;
  final int? crossAxisCount;
  final double? aspectRatio;
  final double? spacing;

  @override
  Widget build(BuildContext context) {
    final effectiveCrossAxisCount =
        crossAxisCount ?? ResponsiveSystem.getAdaptiveColumnCount(context);

    final effectiveAspectRatio = aspectRatio ??
        ResponsiveSystem.getAdaptiveImageAspectRatio(
          context,
          contentType: contentType,
        );

    final effectiveSpacing =
        spacing ?? ResponsiveSystem.getAdaptiveGridSpacing(context);

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: effectiveCrossAxisCount,
        childAspectRatio: effectiveAspectRatio,
        crossAxisSpacing: effectiveSpacing,
        mainAxisSpacing: effectiveSpacing,
      ),
      itemCount: images.length,
      itemBuilder: (context, index) {
        final imagePath = images[index];
        return DynamicImageWidget(
          imagePath: imagePath,
          contentType: contentType,
          borderRadius: BorderRadius.circular(DesignSystem.radiusS),
          onTap: () => onImageTap?.call(index, imagePath),
          heroTag: 'image_$index',
        );
      },
    );
  }
}

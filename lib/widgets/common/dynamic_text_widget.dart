import 'package:flutter/material.dart';
import 'package:dasso_reader/config/responsive_system.dart';
import 'package:dasso_reader/config/app_typography.dart';
import 'package:dasso_reader/enums/dynamic_content_types.dart';

/// A comprehensive text widget that adapts to different content types and screen sizes
///
/// This widget automatically handles:
/// - Text overflow based on content type
/// - Max lines based on screen size and content type
/// - Font scaling for accessibility
/// - Responsive typography
class DynamicTextWidget extends StatelessWidget {
  const DynamicTextWidget(
    this.text, {
    super.key,
    this.style,
    this.contentType = TextContentType.general,
    this.maxLines,
    this.overflow,
    this.textAlign,
    this.softWrap = true,
    this.textScaler,
    this.semanticsLabel,
    this.minFontSize,
    this.maxFontSize,
    this.customBaseLines,
  });

  /// The text to display
  final String text;

  /// The text style to apply
  final TextStyle? style;

  /// The type of content this text represents
  final TextContentType contentType;

  /// Maximum number of lines (overrides adaptive calculation if provided)
  final int? maxLines;

  /// Text overflow behavior (overrides adaptive calculation if provided)
  final TextOverflow? overflow;

  /// Text alignment
  final TextAlign? textAlign;

  /// Whether text should wrap
  final bool softWrap;

  /// Text scaler for accessibility
  final TextScaler? textScaler;

  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Minimum font size for responsive scaling
  final double? minFontSize;

  /// Maximum font size for responsive scaling
  final double? maxFontSize;

  /// Custom base lines for max lines calculation
  final int? customBaseLines;

  @override
  Widget build(BuildContext context) {
    // Get adaptive text properties
    final adaptiveMaxLines = maxLines ??
        ResponsiveSystem.getAdaptiveMaxLines(
          context,
          baseLines: customBaseLines ?? contentType.defaultMaxLines,
          contentType: contentType,
        );

    final adaptiveOverflow = overflow ??
        ResponsiveSystem.getAdaptiveTextOverflow(
          context,
          contentType: contentType,
        );

    // Get responsive text style
    TextStyle effectiveStyle = style ?? _getDefaultStyleForContentType(context);

    if (minFontSize != null || maxFontSize != null) {
      effectiveStyle = AppTypography.responsive(
        context,
        effectiveStyle,
        minFontSize: minFontSize,
        maxFontSize: maxFontSize,
      );
    }

    return Text(
      text,
      style: effectiveStyle,
      maxLines: adaptiveMaxLines,
      overflow: adaptiveOverflow,
      textAlign: textAlign,
      softWrap: softWrap,
      textScaler: textScaler,
      semanticsLabel: semanticsLabel,
    );
  }

  /// Get default text style based on content type
  TextStyle _getDefaultStyleForContentType(BuildContext context) {
    switch (contentType) {
      case TextContentType.title:
        return AppTypography.titleLarge;
      case TextContentType.subtitle:
        return AppTypography.titleMedium;
      case TextContentType.body:
        return AppTypography.bodyLarge;
      case TextContentType.caption:
        return AppTypography.bodySmall;
      case TextContentType.label:
        return AppTypography.labelMedium;
      case TextContentType.userGenerated:
        return AppTypography.bodyMedium;
      case TextContentType.translation:
        return AppTypography.bodyMedium.copyWith(
          fontStyle: FontStyle.italic,
        );
      case TextContentType.error:
        return AppTypography.bodyMedium.copyWith(
          color: Theme.of(context).colorScheme.error,
        );
      case TextContentType.success:
        return AppTypography.bodyMedium.copyWith(
          color: Theme.of(context).colorScheme.primary,
        );
      case TextContentType.warning:
        return AppTypography.bodyMedium.copyWith(
          color: Theme.of(context).colorScheme.tertiary,
        );
      case TextContentType.info:
        return AppTypography.bodyMedium.copyWith(
          color: Theme.of(context).colorScheme.secondary,
        );
      case TextContentType.general:
        return AppTypography.bodyMedium;
    }
  }
}

/// A specialized text widget for user-generated content with enhanced readability
class UserGeneratedTextWidget extends StatelessWidget {
  const UserGeneratedTextWidget(
    this.text, {
    super.key,
    this.style,
    this.maxLines,
    this.showFullTextButton = true,
    this.onShowFullText,
  });

  final String text;
  final TextStyle? style;
  final int? maxLines;
  final bool showFullTextButton;
  final VoidCallback? onShowFullText;

  @override
  Widget build(BuildContext context) {
    final adaptiveMaxLines = maxLines ??
        ResponsiveSystem.getAdaptiveMaxLines(
          context,
          baseLines: 3,
          contentType: TextContentType.userGenerated,
        );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DynamicTextWidget(
          text,
          style: style,
          contentType: TextContentType.userGenerated,
          maxLines: adaptiveMaxLines,
        ),
        if (showFullTextButton && _isTextTruncated(context, adaptiveMaxLines))
          TextButton(
            onPressed: onShowFullText,
            child: const Text('Show more'),
          ),
      ],
    );
  }

  bool _isTextTruncated(BuildContext context, int maxLines) {
    // Simple heuristic - in a real implementation, you might want to measure the text
    final estimatedLines = (text.length / 50).ceil(); // Rough estimate
    return estimatedLines > maxLines;
  }
}

/// A specialized text widget for translation content with language indicators
class TranslationTextWidget extends StatelessWidget {
  const TranslationTextWidget(
    this.text, {
    super.key,
    this.originalText,
    this.sourceLanguage,
    this.targetLanguage,
    this.style,
    this.showLanguageIndicator = true,
  });

  final String text;
  final String? originalText;
  final String? sourceLanguage;
  final String? targetLanguage;
  final TextStyle? style;
  final bool showLanguageIndicator;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (originalText != null) ...[
          DynamicTextWidget(
            originalText!,
            contentType: TextContentType.general,
            style: style?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 8),
          const Divider(height: 1),
          const SizedBox(height: 8),
        ],
        DynamicTextWidget(
          text,
          style: style,
          contentType: TextContentType.translation,
        ),
        if (showLanguageIndicator &&
            sourceLanguage != null &&
            targetLanguage != null)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              '$sourceLanguage → $targetLanguage',
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    color: Theme.of(context).colorScheme.outline,
                  ),
            ),
          ),
      ],
    );
  }
}

/// A text widget that shows different states (loading, error, empty)
class StatefulTextWidget extends StatelessWidget {
  const StatefulTextWidget({
    super.key,
    this.text,
    this.loadingState = LoadingState.initial,
    this.emptyStateType = EmptyStateType.general,
    this.contentType = TextContentType.general,
    this.style,
    this.loadingText = 'Loading...',
    this.emptyText,
    this.errorText = 'Failed to load content',
    this.onRetry,
  });

  final String? text;
  final LoadingState loadingState;
  final EmptyStateType emptyStateType;
  final TextContentType contentType;
  final TextStyle? style;
  final String loadingText;
  final String? emptyText;
  final String errorText;
  final VoidCallback? onRetry;

  @override
  Widget build(BuildContext context) {
    switch (loadingState) {
      case LoadingState.loading:
      case LoadingState.refreshing:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
            const SizedBox(width: 8),
            DynamicTextWidget(
              loadingText,
              contentType: TextContentType.info,
              style: style,
            ),
          ],
        );

      case LoadingState.error:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            DynamicTextWidget(
              errorText,
              contentType: TextContentType.error,
              style: style,
            ),
            if (onRetry != null)
              TextButton(
                onPressed: onRetry,
                child: const Text('Retry'),
              ),
          ],
        );

      case LoadingState.empty:
        return DynamicTextWidget(
          emptyText ?? _getDefaultEmptyText(),
          contentType: TextContentType.info,
          style: style?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        );

      case LoadingState.success:
      case LoadingState.initial:
        if (text == null || text!.isEmpty) {
          return DynamicTextWidget(
            emptyText ?? _getDefaultEmptyText(),
            contentType: TextContentType.info,
            style: style?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          );
        }
        return DynamicTextWidget(
          text!,
          contentType: contentType,
          style: style,
        );
    }
  }

  String _getDefaultEmptyText() {
    switch (emptyStateType) {
      case EmptyStateType.noBooks:
        return 'No books available';
      case EmptyStateType.noSearchResults:
        return 'No search results found';
      case EmptyStateType.noNotes:
        return 'No notes available';
      case EmptyStateType.noBookmarks:
        return 'No bookmarks available';
      case EmptyStateType.noHistory:
        return 'No reading history';
      case EmptyStateType.networkError:
        return 'Network connection error';
      case EmptyStateType.permissionDenied:
        return 'Permission denied';
      case EmptyStateType.notFound:
        return 'Content not found';
      case EmptyStateType.general:
        return 'No content available';
    }
  }
}

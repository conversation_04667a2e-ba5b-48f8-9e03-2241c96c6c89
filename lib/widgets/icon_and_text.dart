import 'package:flutter/material.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/utils/accessibility/semantic_helpers.dart';

class IconAndText extends StatelessWidget {
  final Widget icon;
  final String text;
  final VoidCallback? onTap;
  final double? fontSize;

  const IconAndText({
    super.key,
    required this.icon,
    required this.text,
    this.onTap,
    this.fontSize,
  });

  @override
  Widget build(BuildContext context) {
    Widget content = SizedBox(
      width: DesignSystem.spaceXXL, // 48.0 -> standardized
      height: DesignSystem.spaceXXL +
          DesignSystem.spaceS +
          DesignSystem.spaceXS, // 60.0 -> standardized
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          icon,
          DesignSystem
              .verticalSpaceXS, // Consistent spacing between icon and text
          FittedBox(
            fit: BoxFit.scaleDown,
            child: Container(
              constraints: const BoxConstraints(
                maxWidth: DesignSystem.spaceXXL * 2, // 96.0 -> standardized
              ),
              child: Text(
                text,
                style: TextStyle(fontSize: fontSize),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      ),
    );

    return onTap != null
        ? SemanticHelpers.button(
            context: context,
            label: text,
            hint: 'Tap to ${text.toLowerCase()}',
            onTap: onTap,
            child: ConstrainedBox(
              constraints: DesignSystem.getMinTouchTargetConstraints(),
              child: IconButton(
                onPressed: onTap,
                icon: content,
              ),
            ),
          )
        : content;
  }
}

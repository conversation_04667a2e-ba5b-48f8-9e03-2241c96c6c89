import 'package:dasso_reader/widgets/reading_page/more_settings/more_settings.dart';
import 'package:dasso_reader/config/design_system.dart';
import 'package:flutter/material.dart';

/// Optimized widget title component with const constructor for performance
class WidgetTitle extends StatelessWidget {
  const WidgetTitle({
    super.key,
    required this.title,
    required this.currentSetting,
    this.textColor,
  });

  final String title;
  final ReadingSettings currentSetting;
  final Color? textColor;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: DesignSystem.spaceS),
      child: Row(
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize:
                  DesignSystem.fontSizeXL, // Use DesignSystem constant (18.0)
              fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.bold),
              color: textColor,
            ),
          ),
          const Spacer(),
        ],
      ),
    );
  }
}

/// Legacy function wrapper for backward compatibility
Widget widgetTitle(
  String title,
  ReadingSettings currentSetting, {
  Color? textColor,
}) {
  return WidgetTitle(
    title: title,
    currentSetting: currentSetting,
    textColor: textColor,
  );
}

import 'package:flutter/material.dart';

/// Reusable Mountain Painter for consistent decoration across the app
/// Optimized for performance with shouldRepaint returning false
class MountainPainter extends CustomPainter {
  final Color color;
  final double opacity;

  const MountainPainter({
    this.color = Colors.white,
    this.opacity = 0.2,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color.withValues(alpha: opacity)
      ..style = PaintingStyle.fill;

    final path = Path();

    // Start at bottom left
    path.moveTo(0, size.height);

    // Create mountain silhouette with optimized points
    final mountainPoints = [
      (0.2, 0.4), // First peak
      (0.35, 0.7), // Valley
      (0.5, 0.2), // Highest peak
      (0.7, 0.6), // Valley
      (0.85, 0.3), // Final peak
    ];

    // Draw mountain path
    for (final (x, y) in mountainPoints) {
      path.lineTo(size.width * x, size.height * y);
    }

    // End at bottom right and close path
    path.lineTo(size.width, size.height);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    // Only repaint if color or opacity changes
    if (oldDelegate is MountainPainter) {
      return oldDelegate.color != color || oldDelegate.opacity != opacity;
    }
    return true;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MountainPainter &&
        other.color == color &&
        other.opacity == opacity;
  }

  @override
  int get hashCode => Object.hash(color, opacity);
}

/// Convenient widget wrapper for the mountain decoration
class MountainDecoration extends StatelessWidget {
  final double height;
  final Color color;
  final double opacity;

  const MountainDecoration({
    super.key,
    this.height = 80.0,
    this.color = Colors.white,
    this.opacity = 0.2,
  });

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: Size(double.infinity, height),
      painter: MountainPainter(
        color: color,
        opacity: opacity,
      ),
    );
  }
}

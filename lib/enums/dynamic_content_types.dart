import 'package:flutter/material.dart';

/// Enums for dynamic content handling in the Dasso Reader app
///
/// These enums help categorize different types of content to provide
/// appropriate responsive behavior and styling.

/// Text content types for adaptive text handling
enum TextContentType {
  /// General text content (default)
  general,

  /// User-generated content (comments, notes, reviews)
  /// Should be more visible and allow more space
  userGenerated,

  /// Translation text content
  /// Often needs more space and should show as much as possible
  translation,

  /// Title text content
  /// Should be concise and truncated cleanly
  title,

  /// Subtitle text content
  subtitle,

  /// Body text content for reading
  body,

  /// Caption text content
  caption,

  /// Label text content
  label,

  /// Error message text content
  error,

  /// Success message text content
  success,

  /// Warning message text content
  warning,

  /// Info message text content
  info,
}

/// Image content types for adaptive image handling
enum ImageContentType {
  /// General image content (default)
  general,

  /// Book cover images
  /// Typically taller aspect ratio
  bookCover,

  /// Avatar/profile images
  /// Should be square
  avatar,

  /// Banner images
  /// Wide aspect ratio
  banner,

  /// Thumbnail images
  /// Small, slightly wide aspect ratio
  thumbnail,

  /// Icon images
  /// Small, square
  icon,

  /// Hero images
  /// Large, prominent images
  hero,

  /// Gallery images
  /// Variable aspect ratios
  gallery,

  /// Background images
  /// Should fill available space
  background,
}

/// List content types for adaptive list handling
enum ListContentType {
  /// General list content (default)
  general,

  /// Book list content
  books,

  /// Notes list content
  notes,

  /// Search results list content
  searchResults,

  /// Settings list content
  settings,

  /// Navigation list content
  navigation,

  /// HSK characters list content
  hskCharacters,

  /// Dictionary entries list content
  dictionary,

  /// Reading history list content
  history,

  /// Bookmarks list content
  bookmarks,
}

/// Grid content types for adaptive grid handling
enum GridContentType {
  /// General grid content (default)
  general,

  /// Book grid content
  books,

  /// Theme selection grid
  themes,

  /// Color picker grid
  colors,

  /// Icon selection grid
  icons,

  /// Gallery grid content
  gallery,

  /// Settings grid content
  settings,
}

// ContentDensity is already defined in design_system.dart

/// Loading state types for dynamic content
enum LoadingState {
  /// Initial state - no loading
  initial,

  /// Loading state - show loading indicator
  loading,

  /// Success state - content loaded successfully
  success,

  /// Error state - failed to load content
  error,

  /// Empty state - no content available
  empty,

  /// Refreshing state - refreshing existing content
  refreshing,
}

/// Empty state types for different scenarios
enum EmptyStateType {
  /// General empty state
  general,

  /// No books available
  noBooks,

  /// No search results
  noSearchResults,

  /// No notes available
  noNotes,

  /// No bookmarks available
  noBookmarks,

  /// No reading history
  noHistory,

  /// Network error
  networkError,

  /// Permission denied
  permissionDenied,

  /// Content not found
  notFound,
}

/// Extensions for content type utilities
extension TextContentTypeExtension on TextContentType {
  /// Get the default max lines for this text content type
  int get defaultMaxLines {
    switch (this) {
      case TextContentType.title:
        return 2;
      case TextContentType.subtitle:
        return 1;
      case TextContentType.userGenerated:
        return 5;
      case TextContentType.translation:
        return 6;
      case TextContentType.body:
        return 10;
      case TextContentType.caption:
        return 2;
      case TextContentType.label:
        return 1;
      case TextContentType.error:
      case TextContentType.success:
      case TextContentType.warning:
      case TextContentType.info:
        return 3;
      case TextContentType.general:
        return 2;
    }
  }

  /// Get the default text overflow for this content type
  TextOverflow get defaultOverflow {
    switch (this) {
      case TextContentType.title:
      case TextContentType.label:
        return TextOverflow.ellipsis;
      case TextContentType.translation:
      case TextContentType.userGenerated:
        return TextOverflow.fade;
      case TextContentType.error:
      case TextContentType.success:
      case TextContentType.warning:
      case TextContentType.info:
        return TextOverflow.visible;
      case TextContentType.general:
      case TextContentType.subtitle:
      case TextContentType.body:
      case TextContentType.caption:
        return TextOverflow.ellipsis;
    }
  }
}

extension ImageContentTypeExtension on ImageContentType {
  /// Get the default aspect ratio for this image content type
  double get defaultAspectRatio {
    switch (this) {
      case ImageContentType.bookCover:
        return 0.65; // Taller
      case ImageContentType.avatar:
      case ImageContentType.icon:
        return 1.0; // Square
      case ImageContentType.banner:
        return 2.5; // Wide
      case ImageContentType.thumbnail:
        return 1.2; // Slightly wide
      case ImageContentType.hero:
        return 16 / 9; // Cinematic
      case ImageContentType.general:
      case ImageContentType.gallery:
      case ImageContentType.background:
        return 4 / 3; // Standard
    }
  }
}

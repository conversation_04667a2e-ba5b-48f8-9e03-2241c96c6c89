enum AiPrompts {
  test,
  summaryTheChapter,
  summaryTheBook,
  summaryThePreviousContent,
  grammar,
  exampleSentences,
  characterBreakdown,
  wordUsage,
  synonymsAntonyms,
  characterMnemonics,
}

extension AiPromptsJson on AiPrompts {
  String getPrompt() {
    switch (this) {
      case AiPrompts.test:
        return '''
        Write a concise and friendly self-introduction. Use the language code: {{language_locale}}
        ''';

      case AiPrompts.summaryTheChapter:
        return '''
Summarize the chapter content. Your reply must follow these requirements:
Language: Use the same language as the original chapter content.
Length: 8-10 complete sentences.
Structure: Three paragraphs: Main plot, Core characters, Themes/messages.
Style: Avoid boilerplate phrases like "This chapter describes..."
Perspective: Maintain a literary analysis perspective, not just narration.
Chapter content: {{chapter}}
        ''';

      case AiPrompts.summaryTheBook:
        return '''
Generate a book summary for "{{book}}" by {{author}}
[Requirements]:
Language matches the book title's language
Central conflict (highlight with » symbol)
3 core characters + their motivations (name + critical choice)
Theme keywords (3-5)
Avoid spoiling the final outcome
        ''';

      case AiPrompts.summaryThePreviousContent:
        return '''
I'm revisiting a book I read long ago. Help me quickly recall the previous content to continue reading:
[Requirements]
3-5 sentences
Same language as original previous content
Avoid verbatim repetition; preserve core information

[Previous Content]
{{previous_content}}
        ''';

      case AiPrompts.grammar:
        return '''
Grammar Point: [Identify the key grammar rule in **{{text}}**, e.g., "了 (le) for completed actions"].

Explanation (English): [1-2 sentences explaining the rule in **{{text}}** simply].

Explanation (Arabic): [شرح عربي مختصر للقاعدة في **{{text}}**].

Examples:

Chinese: **{{text}}**
Pinyin: [Pinyin with tone marks, **{{text}}** in bold]
English: [Translation with **{{text}}** in bold]
Arabic: [ترجمة عربية مع **{{text}}** بالخط العريض]

Chinese: [Create a new example sentence using **{{text}}** in bold]
Pinyin: [Pinyin with **{{text}}** in bold]
English/Arabic: [Translations with **{{text}}** in bold]

Pronunciation Guide: Highlight **{{text}}** with pinyin and tones.

Key Notes: [List 2-3 common mistakes or tips about **{{text}}**].
        ''';

      case AiPrompts.exampleSentences:
        return '''
Generate 5 example sentences using "**{{text}}**" in different contexts:

[Requirements]
- Use the same language as the input text
- Show various usage patterns and contexts
- Include pinyin for Chinese characters
- Provide English translations
- Range from simple to intermediate complexity
- Always format the target word/phrase as **{{text}}** in bold throughout your response

Format:
1. Chinese: [Sentence with **{{text}}** in bold]
   Pinyin: [Pinyin with tone marks, **{{text}}** in bold]
   English: [Translation with **{{text}}** in bold]
   Context: [When/where this would be used]

[Continue for 5 examples]

Usage Notes: [Brief explanation of common patterns or collocations with **{{text}}**]
        ''';

      case AiPrompts.characterBreakdown:
        return '''
Analyze the character structure of "**{{text}}**":

[For each character in **{{text}}**, provide:]

Character: **[Character from {{text}}]**
Radical: [Main radical] (meaning: [radical meaning])
Components: [List all components/parts]
Stroke Count: [Number]
Structure Type: [左右结构/上下结构/包围结构/etc.]

Etymology: [Brief origin story of **{{text}}** if known]
Mnemonic: [Memory aid for **{{text}}** connecting components to meaning]

Related Characters: [Characters sharing components with **{{text}}**]
Common Words: [3-5 common words using **{{text}}**]

[If **{{text}}** contains multiple characters, analyze each separately]

Learning Tip: [Strategy for remembering **{{text}}**]
        ''';

      case AiPrompts.wordUsage:
        return '''
Analyze the usage and frequency of "**{{text}}**":

Word/Phrase: **{{text}}**
HSK Level: [HSK 1-6 or Advanced/Native level]
Frequency: [Very Common/Common/Moderate/Rare]
Formality: [Formal/Neutral/Informal/Colloquial]

Usage Contexts:
- Written: [Where **{{text}}** appears in writing]
- Spoken: [When **{{text}}** is used in conversation]
- Regional: [Any regional preferences for **{{text}}**]

Grammar Notes:
- Part of Speech: [Noun/Verb/Adjective/etc.]
- Usage Patterns: [How **{{text}}** is typically used in sentences]
- Collocations: [Common word combinations with **{{text}}**]

Examples by Context:
1. Formal: [Example sentence with **{{text}}** in bold]
2. Casual: [Example sentence with **{{text}}** in bold]
3. Written: [Example sentence with **{{text}}** in bold]

Learning Priority: [High/Medium/Low] - [Explanation why **{{text}}** is important]
Similar Level Words: [3-5 words of similar difficulty to **{{text}}**]
        ''';

      case AiPrompts.synonymsAntonyms:
        return '''
Find synonyms and antonyms for "**{{text}}**":

Target Word: **{{text}}**
Meaning: [Brief definition of **{{text}}**]

SYNONYMS (近义词):
1. [Synonym 1] - [Subtle difference from **{{text}}** in meaning/usage]
2. [Synonym 2] - [Subtle difference from **{{text}}** in meaning/usage]
3. [Synonym 3] - [Subtle difference from **{{text}}** in meaning/usage]
[Continue as appropriate]

ANTONYMS (反义词):
1. [Antonym 1] - [How it contrasts with **{{text}}**]
2. [Antonym 2] - [How it contrasts with **{{text}}**]
[Continue as appropriate]

USAGE COMPARISON:
- **{{text}}**: [When to use **{{text}}**]
- [Key synonym]: [When to use instead of **{{text}}**]
- [Key antonym]: [Contrasting usage to **{{text}}**]

Example Sentences:
- Original: [Sentence with **{{text}}** in bold]
- Synonym: [Same sentence with synonym, showing difference from **{{text}}**]
- Antonym: [Contrasting sentence to **{{text}}**]

Learning Note: [Tips for choosing between **{{text}}** and similar words]
        ''';

      case AiPrompts.characterMnemonics:
        return '''
Create memory aids for "**{{text}}**":

[For each character in **{{text}}**, provide:]

Character: **[Character from {{text}}]**
Meaning: [Primary meaning of this character in **{{text}}**]

VISUAL MNEMONIC:
Story: [Creative story connecting visual elements of **{{text}}** to meaning]
Visual Cues: [What **{{text}}** "looks like"]

COMPONENT BREAKDOWN:
- [Component 1]: [Meaning] → [How it relates to **{{text}}**'s overall meaning]
- [Component 2]: [Meaning] → [How it relates to **{{text}}**'s overall meaning]

MEMORY PALACE:
Location: [Suggested place to "store" **{{text}}**]
Action: [Memorable action involving **{{text}}**'s meaning]

PRONUNCIATION AID:
Sound Association: [English word or phrase that sounds like **{{text}}**]
Tone Memory: [Technique for remembering **{{text}}**'s tone]

PRACTICE PHRASES:
1. [Simple phrase using **{{text}}** in bold]
2. [Memorable sentence with **{{text}}** in bold for context]

Review Trigger: [Quick mental cue to recall **{{text}}**]
        ''';
    }
  }
}

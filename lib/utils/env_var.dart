import 'dart:io';

class EnvVar {
  static bool get isBeian {
    if (!isAppStore) {
      return false;
    }
    return Platform.localeName == 'zh_Hans_CN';
  }

  static const bool isAppStore =
      String.fromEnvironment('isAppStore', defaultValue: 'false') == 'true';

  // Update checking from fork repo is enabled
  static const bool disableUpdates = false;

  static const String sharingSecret =
      String.fromEnvironment('sharingSecret', defaultValue: '');
}

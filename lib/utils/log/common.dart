import 'dart:io';

import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/utils/log/string_to_level.dart';
import 'package:dasso_reader/utils/get_path/log_file.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:logging/logging.dart';

class AnxLog {
  static final log = Logger('AnxReader');
  static late File? logFile;

  Level level;
  DateTime time;
  String message;

  AnxLog(this.level, this.time, this.message);

  Color getColor(BuildContext context) => level == Level.SEVERE
      ? Theme.of(context).colorScheme.error
      : level == Level.WARNING
          ? Theme.of(context).colorScheme.onSurfaceVariant
          : Theme.of(context).colorScheme.outline;

  static AnxLog parse(String log) {
    try {
      final logParts = log.split('^*^');
      final level = stringToLevel(logParts[0]);
      final time = DateTime.parse(logParts[1].trim());
      final message = logParts[2];
      return AnxLog(level, time, message);
    } catch (e) {
      return AnxLog(Level.SEVERE, DateTime.now(), 'Parse log error: $e');
    }
  }

  static Future<void> init() async {
    logFile = await getLogFile();

    Logger.root.level = Level.ALL;
    Logger.root.onRecord.listen((record) {
      if (kDebugMode) {
        String colorCode = '';
        if (record.level == Level.SEVERE) {
          colorCode = '\x1B[31m';
        } else if (record.level == Level.WARNING) {
          colorCode = '\x1B[33m';
        } else if (record.level == Level.INFO) {
          colorCode = '\x1B[34m';
        }
        print(
          '$colorCode${record.level.name}: ${record.time}: ${record.message} \x1B[0m',
        );
        if (record.error != null) {
          print('${record.error} \x1B[0m');
        }
      }
      String error = record.error == null ? '' : ' : ${record.error}';
      logFile!.writeAsStringSync(
        '${'${record.level.name}^*^ ${record.time}^*^ [${record.message}]$error'.replaceAll('\n', ' ')}\n',
        mode: FileMode.append,
      );
    });
    if (Prefs().clearLogWhenStart) {
      clear();
    }
    info('Log file: ${logFile!.path}');
  }

  static void clear() {
    logFile!.writeAsStringSync('');
  }

  static void info(String message, [Object? error, StackTrace? stackTrace]) {
    log.info(message, error, stackTrace);
  }

  static void warning(String message, [Object? error, StackTrace? stackTrace]) {
    log.warning(message, error, stackTrace);
  }

  static void severe(String message, [Object? error, StackTrace? stackTrace]) {
    log.severe(message, error, stackTrace);
  }

  // TTS-specific logging methods for context-aware error handling

  /// Log TTS operation start with context information
  static void ttsOperationStart(
    String context,
    String engine,
    String operation, {
    Map<String, dynamic>? additionalContext,
  }) {
    final message = '[TTS] $context - $engine: Starting $operation';
    if (additionalContext != null && additionalContext.isNotEmpty) {
      info('$message | Context: $additionalContext');
    } else {
      info(message);
    }
  }

  /// Log TTS operation success with performance metrics
  static void ttsOperationSuccess(
    String context,
    String engine,
    String operation,
    Duration duration, {
    Map<String, dynamic>? metrics,
  }) {
    final message =
        '[TTS] $context - $engine: $operation completed in ${duration.inMilliseconds}ms';
    if (metrics != null && metrics.isNotEmpty) {
      info('$message | Metrics: $metrics');
    } else {
      info(message);
    }
  }

  /// Log TTS operation failure with detailed error information
  static void ttsOperationFailure(
    String context,
    String engine,
    String operation,
    String errorType,
    String errorMessage, [
    Object? error,
    StackTrace? stackTrace,
  ]) {
    final message =
        '[TTS] $context - $engine: $operation failed | Error: $errorType - $errorMessage';
    severe(message, error, stackTrace);
  }

  /// Log TTS state changes for debugging
  static void ttsStateChange(
    String context,
    String engine,
    String fromState,
    String toState, {
    String? reason,
  }) {
    final message =
        '[TTS] $context - $engine: State changed from $fromState to $toState';
    if (reason != null) {
      info('$message (Reason: $reason)');
    } else {
      info(message);
    }
  }

  /// Log TTS conflict detection events
  static void ttsConflictDetected(
    String requestingContext,
    String requestedEngine,
    String conflictingContext,
    String conflictingEngine,
  ) {
    final message =
        '[TTS] CONFLICT DETECTED: $requestingContext requesting $requestedEngine but $conflictingContext is using $conflictingEngine';
    warning(message);
  }

  /// Log TTS fallback operations
  static void ttsFallback(
    String context,
    String fromEngine,
    String toEngine,
    String reason,
  ) {
    final message =
        '[TTS] $context: Falling back from $fromEngine to $toEngine (Reason: $reason)';
    info(message);
  }

  /// Log TTS resource coordination events
  static void ttsResourceEvent(
    String context,
    String engine,
    String event, {
    String? details,
  }) {
    final message = '[TTS] $context - $engine: $event';
    if (details != null) {
      info('$message | Details: $details');
    } else {
      info(message);
    }
  }
}

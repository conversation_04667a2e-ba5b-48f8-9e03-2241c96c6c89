import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

void hideStatusBar() {
  SystemChrome.setEnabledSystemUIMode(
    SystemUiMode.manual,
    overlays: [],
  );
}

void showStatusBar() {
  SystemChrome.setEnabledSystemUIMode(
    SystemUiMode.manual,
    overlays: SystemUiOverlay.values,
  );
}

void showStatusBarWithoutResize() {
  SystemChrome.setEnabledSystemUIMode(
    SystemUiMode.edgeToEdge,
  );
}

void onlyStatusBar() {
  SystemChrome.setEnabledSystemUIMode(
    SystemUiMode.manual,
    overlays: [
      SystemUiOverlay.top,
    ],
  );
}

/// Applies StatusBar styling based on background color
/// This ensures StatusBar icons are visible against any background
void applyStatusBarForBackground(Color backgroundColor) {
  final luminance = backgroundColor.computeLuminance();
  final usesDarkIcons = luminance > 0.5;

  final style = SystemUiOverlayStyle(
    statusBarColor: Colors.transparent,
    statusBarIconBrightness: usesDarkIcons ? Brightness.dark : Brightness.light,
    statusBarBrightness: usesDarkIcons ? Brightness.light : Brightness.dark,
    systemNavigationBarColor: Colors.transparent,
    systemNavigationBarIconBrightness:
        usesDarkIcons ? Brightness.dark : Brightness.light,
  );

  SystemChrome.setSystemUIOverlayStyle(style);
}

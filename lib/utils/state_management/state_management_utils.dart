import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'rebuild_optimization.dart';
import 'const_audit.dart';
import 'provider_optimization.dart';
import 'predictability_integration.dart';

/// Comprehensive state management utilities for Dasso Reader
///
/// This system provides a unified interface for all state management optimizations
/// while respecting the existing Riverpod implementation patterns.
class StateManagementUtils {
  static bool _initialized = false;

  /// Initialize the state management optimization system
  static Future<void> initialize() async {
    if (_initialized) return;

    if (kDebugMode) {
      debugPrint('🚀 Initializing Dasso Reader State Management Optimizations');

      // Set up periodic performance monitoring
      _setupPerformanceMonitoring();
    }

    // Initialize predictability system
    try {
      await PredictabilitySystem.instance.initialize();
    } catch (e) {
      if (kDebugMode) {
        debugPrint('⚠️ Could not initialize predictability system: $e');
      }
    }

    _initialized = true;
  }

  /// Set up periodic performance monitoring in debug mode
  static void _setupPerformanceMonitoring() {
    if (!kDebugMode) return;

    // Print performance summaries every 30 seconds in debug mode
    Future.delayed(const Duration(seconds: 30), () {
      _printPerformanceSummary();
      _setupPerformanceMonitoring(); // Recursive call for continuous monitoring
    });
  }

  /// Print comprehensive performance summary
  static void _printPerformanceSummary() {
    if (!kDebugMode) return;

    const separator =
        '============================================================';
    debugPrint('\n$separator');
    debugPrint('📊 DASSO READER STATE MANAGEMENT PERFORMANCE REPORT');
    debugPrint(separator);

    RebuildOptimization.printRebuildSummary();
    ConstAudit.printAuditSummary();
    ProviderPerformanceMonitor.printPerformanceSummary();

    debugPrint('$separator\n');
  }

  /// Clear all performance data
  static void clearPerformanceData() {
    RebuildOptimization.clearRebuildStats();
    ConstAudit.clearAuditResults();
    ProviderPerformanceMonitor.clearStats();
    ProviderOptimization.clearSelectorCache();
  }

  /// Get comprehensive performance report
  static StateManagementReport getPerformanceReport() {
    return StateManagementReport(
      rebuildStats: RebuildOptimization.getRebuildStats(),
      constAuditResults: ConstAudit.getAuditResults(),
      providerStats: ProviderPerformanceMonitor.getStats(),
      timestamp: DateTime.now(),
    );
  }
}

/// Comprehensive performance report
class StateManagementReport {
  const StateManagementReport({
    required this.rebuildStats,
    required this.constAuditResults,
    required this.providerStats,
    required this.timestamp,
  });

  final Map<String, int> rebuildStats;
  final Map<String, ConstAuditResult> constAuditResults;
  final Map<String, ProviderStats> providerStats;
  final DateTime timestamp;

  /// Get total number of rebuilds
  int get totalRebuilds =>
      rebuildStats.values.fold(0, (sum, count) => sum + count);

  /// Get number of widgets that can be optimized
  int get optimizableWidgets =>
      constAuditResults.values.where((result) => result.canBeOptimized).length;

  /// Get average provider rebuild rate
  double get averageProviderRebuildRate {
    if (providerStats.isEmpty) return 0.0;

    final rates = providerStats.values
        .map((stats) => stats.rebuildRate)
        .where((rate) => rate.isFinite);

    if (rates.isEmpty) return 0.0;

    return rates.reduce((a, b) => a + b) / rates.length;
  }

  /// Get performance score (0-100, higher is better)
  double get performanceScore {
    double score = 100.0;

    // Deduct points for excessive rebuilds
    final excessiveRebuilds =
        rebuildStats.values.where((count) => count > 10).length;
    score -= excessiveRebuilds * 5;

    // Deduct points for unoptimized widgets
    score -= optimizableWidgets * 2;

    // Deduct points for high provider rebuild rates
    score -= averageProviderRebuildRate * 20;

    return score.clamp(0.0, 100.0);
  }

  /// Get performance grade
  String get performanceGrade {
    final score = performanceScore;
    if (score >= 90) return 'A+';
    if (score >= 80) return 'A';
    if (score >= 70) return 'B';
    if (score >= 60) return 'C';
    if (score >= 50) return 'D';
    return 'F';
  }

  /// Generate human-readable summary
  String generateSummary() {
    final buffer = StringBuffer();

    buffer.writeln('State Management Performance Report');
    buffer.writeln('Generated: ${timestamp.toIso8601String()}');
    buffer.writeln(
      'Performance Score: ${performanceScore.toStringAsFixed(1)}/100 ($performanceGrade)',
    );
    buffer.writeln('');

    buffer.writeln('Widget Rebuilds:');
    buffer.writeln('  Total rebuilds: $totalRebuilds');
    buffer.writeln('  Widgets tracked: ${rebuildStats.length}');

    if (rebuildStats.isNotEmpty) {
      final topRebuilders = rebuildStats.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));

      buffer.writeln('  Top rebuilders:');
      for (final entry in topRebuilders.take(5)) {
        buffer.writeln('    ${entry.key}: ${entry.value} rebuilds');
      }
    }

    buffer.writeln('');
    buffer.writeln('Const Optimization:');
    buffer.writeln('  Widgets audited: ${constAuditResults.length}');
    buffer.writeln('  Can be optimized: $optimizableWidgets');

    buffer.writeln('');
    buffer.writeln('Provider Performance:');
    buffer.writeln('  Providers tracked: ${providerStats.length}');
    buffer.writeln(
      '  Average rebuild rate: ${(averageProviderRebuildRate * 100).toStringAsFixed(1)}%',
    );

    return buffer.toString();
  }
}

/// Enhanced Consumer widget with built-in optimization
class DassoConsumer<T> extends ConsumerWidget {
  const DassoConsumer({
    super.key,
    required this.provider,
    required this.builder,
    this.child,
    this.debugName,
    this.shouldRebuild,
    this.enableOptimization = true,
  });

  final ProviderListenable<T> provider;
  final Widget Function(BuildContext context, T value, Widget? child) builder;
  final Widget? child;
  final String? debugName;
  final bool Function(T previous, T current)? shouldRebuild;
  final bool enableOptimization;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final name = debugName ?? 'DassoConsumer<$T>';

    if (enableOptimization) {
      RebuildOptimization.logRebuild(name);
      ProviderPerformanceMonitor.recordAccess(name);
    }

    final value = ref.watch(provider);

    if (enableOptimization) {
      ProviderPerformanceMonitor.recordRebuild(name);
    }

    return builder(context, value, child);
  }
}

/// Enhanced AsyncValue consumer with optimization
class DassoAsyncConsumer<T> extends ConsumerWidget {
  const DassoAsyncConsumer({
    super.key,
    required this.provider,
    required this.builder,
    this.child,
    this.debugName,
    this.loading,
    this.error,
    this.enableOptimization = true,
  });

  final ProviderListenable<AsyncValue<T>> provider;
  final Widget Function(BuildContext context, T value, Widget? child) builder;
  final Widget? child;
  final String? debugName;
  final Widget Function(BuildContext context)? loading;
  final Widget Function(
    BuildContext context,
    Object error,
    StackTrace stackTrace,
  )? error;
  final bool enableOptimization;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final name = debugName ?? 'DassoAsyncConsumer<$T>';

    if (enableOptimization) {
      RebuildOptimization.logRebuild(name);
      ProviderPerformanceMonitor.recordAccess(name);
    }

    final asyncValue = ref.watch(provider);

    if (enableOptimization) {
      ProviderPerformanceMonitor.recordRebuild(name);
    }

    return asyncValue.when(
      data: (data) => builder(context, data, child),
      loading: () =>
          loading?.call(context) ?? const CircularProgressIndicator(),
      error: (err, stack) =>
          error?.call(context, err, stack) ?? Text('Error: $err'),
    );
  }
}

/// Optimized StatefulWidget base class
abstract class DassoStatefulWidget extends StatefulWidget {
  const DassoStatefulWidget({super.key});

  @override
  DassoStatefulWidgetState createState();
}

/// Optimized State base class with built-in performance monitoring
abstract class DassoStatefulWidgetState<T extends DassoStatefulWidget>
    extends State<T> with RebuildOptimizationMixin {
  @override
  String get debugName => '${widget.runtimeType}State';

  @override
  Widget buildOptimized(BuildContext context);

  /// Override this to provide const audit name
  String get constAuditName => widget.runtimeType.toString();

  @override
  void initState() {
    super.initState();
    if (kDebugMode) {
      ConstAudit.auditWidget(widget, constAuditName);
    }
  }
}

/// Extension methods for easy optimization
extension StateManagementExtensions on Widget {
  /// Add comprehensive state management optimization
  Widget withStateOptimization({String? debugName}) {
    return kDebugMode
        ? ConstWrapper(debugName: debugName, child: this)
            .withRepaintBoundary(debugName: debugName)
            .withConstAudit(debugName)
        : this;
  }
}

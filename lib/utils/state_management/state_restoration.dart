import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:dasso_reader/utils/log/common.dart';

/// Comprehensive state restoration system for Dasso Reader
///
/// This system provides:
/// - Automatic state preservation across app lifecycle
/// - Navigation state restoration
/// - Form and UI state preservation
/// - Scroll position restoration
/// - Critical user data protection

/// Types of state that can be preserved
enum StateType {
  navigation,
  form,
  scroll,
  ui,
  search,
  filter,
  selection,
  expansion,
  dialog,
  temporary,
}

/// State restoration configuration
class StateRestorationConfig {
  final bool enabled;
  final Duration autoSaveInterval;
  final Set<StateType> enabledTypes;
  final int maxHistoryEntries;
  final bool debugMode;

  const StateRestorationConfig({
    this.enabled = true,
    this.autoSaveInterval = const Duration(seconds: 30),
    this.enabledTypes = const {
      StateType.navigation,
      StateType.form,
      StateType.scroll,
      StateType.ui,
      StateType.search,
    },
    this.maxHistoryEntries = 50,
    this.debugMode = kDebugMode,
  });
}

/// State restoration data container
class StateData {
  final String key;
  final Map<String, dynamic> data;
  final DateTime timestamp;
  final StateType type;

  const StateData({
    required this.key,
    required this.data,
    required this.timestamp,
    required this.type,
  });

  Map<String, dynamic> toJson() => {
        'key': key,
        'data': data,
        'timestamp': timestamp.millisecondsSinceEpoch,
        'type': type.name,
      };

  factory StateData.fromJson(Map<String, dynamic> json) {
    return StateData(
      key: json['key'] as String,
      data: Map<String, dynamic>.from(json['data'] as Map),
      timestamp: DateTime.fromMillisecondsSinceEpoch(json['timestamp'] as int),
      type: StateType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => StateType.temporary,
      ),
    );
  }
}

/// Main state restoration manager
class StateRestorationManager {
  static final StateRestorationManager _instance =
      StateRestorationManager._internal();
  factory StateRestorationManager() => _instance;
  StateRestorationManager._internal();

  static const String _prefsPrefix = 'state_restoration_';
  static const String _dataKey = '${_prefsPrefix}data';

  StateRestorationConfig _config = const StateRestorationConfig();
  final Map<String, StateData> _stateCache = {};
  Timer? _autoSaveTimer;
  bool _initialized = false;

  /// Initialize the state restoration system
  Future<void> initialize({StateRestorationConfig? config}) async {
    if (_initialized) return;

    _config = config ?? const StateRestorationConfig();

    if (_config.enabled) {
      await _loadPersistedState();
      _startAutoSave();

      if (_config.debugMode) {
        AnxLog.info('🔄 State Restoration System initialized');
        AnxLog.info('📊 Loaded ${_stateCache.length} state entries');
      }
    }

    _initialized = true;
  }

  /// Save state data
  Future<void> saveState(
    String key,
    Map<String, dynamic> data,
    StateType type,
  ) async {
    if (!_config.enabled || !_config.enabledTypes.contains(type)) return;

    try {
      final stateData = StateData(
        key: key,
        data: Map<String, dynamic>.from(data),
        timestamp: DateTime.now(),
        type: type,
      );

      _stateCache[key] = stateData;

      if (_config.debugMode) {
        AnxLog.info('💾 Saved state: $key (${type.name})');
      }
    } catch (e) {
      AnxLog.warning('Failed to save state for key $key: $e');
    }
  }

  /// Restore state data
  Map<String, dynamic>? restoreState(String key, {StateType? type}) {
    if (!_config.enabled) return null;

    try {
      final stateData = _stateCache[key];
      if (stateData == null) return null;

      // Check type filter if specified
      if (type != null && stateData.type != type) return null;

      // Check if state is not too old (24 hours)
      final age = DateTime.now().difference(stateData.timestamp);
      if (age.inHours > 24) {
        _stateCache.remove(key);
        return null;
      }

      if (_config.debugMode) {
        AnxLog.info('🔄 Restored state: $key (${stateData.type.name})');
      }

      return Map<String, dynamic>.from(stateData.data);
    } catch (e) {
      AnxLog.warning('Failed to restore state for key $key: $e');
      return null;
    }
  }

  /// Clear state data
  Future<void> clearState(String key) async {
    _stateCache.remove(key);

    if (_config.debugMode) {
      AnxLog.info('🗑️ Cleared state: $key');
    }
  }

  /// Clear all state data
  Future<void> clearAllState() async {
    _stateCache.clear();
    await _persistState();

    if (_config.debugMode) {
      AnxLog.info('🗑️ Cleared all state data');
    }
  }

  /// Get all state keys by type
  List<String> getStateKeys({StateType? type}) {
    if (type == null) {
      return _stateCache.keys.toList();
    }

    return _stateCache.entries
        .where((entry) => entry.value.type == type)
        .map((entry) => entry.key)
        .toList();
  }

  /// Persist state to SharedPreferences
  Future<void> _persistState() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Clean up old entries
      _cleanupOldEntries();

      // Convert state cache to JSON
      final stateJson = _stateCache.map(
        (key, value) => MapEntry(key, value.toJson()),
      );

      await prefs.setString(_dataKey, jsonEncode(stateJson));

      if (_config.debugMode) {
        AnxLog.info('💾 Persisted ${_stateCache.length} state entries');
      }
    } catch (e) {
      AnxLog.severe('Failed to persist state: $e');
    }
  }

  /// Load persisted state from SharedPreferences
  Future<void> _loadPersistedState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final stateJsonString = prefs.getString(_dataKey);

      if (stateJsonString != null) {
        final stateJson = jsonDecode(stateJsonString) as Map<String, dynamic>;

        for (final entry in stateJson.entries) {
          try {
            final stateData =
                StateData.fromJson(entry.value as Map<String, dynamic>);
            _stateCache[entry.key] = stateData;
          } catch (e) {
            AnxLog.warning('Failed to load state entry ${entry.key}: $e');
          }
        }
      }
    } catch (e) {
      AnxLog.warning('Failed to load persisted state: $e');
    }
  }

  /// Clean up old state entries
  void _cleanupOldEntries() {
    final now = DateTime.now();
    final keysToRemove = <String>[];

    for (final entry in _stateCache.entries) {
      final age = now.difference(entry.value.timestamp);
      if (age.inHours > 24 || _stateCache.length > _config.maxHistoryEntries) {
        keysToRemove.add(entry.key);
      }
    }

    for (final key in keysToRemove) {
      _stateCache.remove(key);
    }
  }

  /// Start auto-save timer
  void _startAutoSave() {
    _autoSaveTimer?.cancel();
    _autoSaveTimer = Timer.periodic(_config.autoSaveInterval, (_) {
      _persistState();
    });
  }

  /// Stop auto-save timer
  void _stopAutoSave() {
    _autoSaveTimer?.cancel();
    _autoSaveTimer = null;
  }

  /// Dispose resources
  Future<void> dispose() async {
    _stopAutoSave();
    await _persistState();
    _stateCache.clear();
    _initialized = false;
  }
}

/// Mixin for widgets that need state restoration
mixin StateRestorationMixin<T extends StatefulWidget> on State<T> {
  String get stateKey;
  StateType get stateType => StateType.ui;

  /// Save widget state
  Future<void> saveWidgetState(Map<String, dynamic> state) async {
    await StateRestorationManager().saveState(stateKey, state, stateType);
  }

  /// Restore widget state
  Map<String, dynamic>? restoreWidgetState() {
    return StateRestorationManager().restoreState(stateKey, type: stateType);
  }

  /// Clear widget state
  Future<void> clearWidgetState() async {
    await StateRestorationManager().clearState(stateKey);
  }
}

/// Navigation state restoration utilities
class NavigationStateRestoration {
  static const String _navigationKey = 'navigation_state';

  /// Save navigation state
  static Future<void> saveNavigationState({
    required int currentIndex,
    required String currentRoute,
    List<String>? routeHistory,
    Map<String, dynamic>? additionalData,
  }) async {
    final state = {
      'currentIndex': currentIndex,
      'currentRoute': currentRoute,
      'routeHistory': routeHistory ?? [],
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'additionalData': additionalData ?? {},
    };

    await StateRestorationManager()
        .saveState(_navigationKey, state, StateType.navigation);
  }

  /// Restore navigation state
  static Map<String, dynamic>? restoreNavigationState() {
    return StateRestorationManager()
        .restoreState(_navigationKey, type: StateType.navigation);
  }

  /// Clear navigation state
  static Future<void> clearNavigationState() async {
    await StateRestorationManager().clearState(_navigationKey);
  }
}

/// Form state restoration utilities
class FormStateRestoration {
  /// Save form state
  static Future<void> saveFormState(
    String formKey,
    Map<String, dynamic> formData,
  ) async {
    await StateRestorationManager().saveState(
      'form_$formKey',
      formData,
      StateType.form,
    );
  }

  /// Restore form state
  static Map<String, dynamic>? restoreFormState(String formKey) {
    return StateRestorationManager().restoreState(
      'form_$formKey',
      type: StateType.form,
    );
  }

  /// Save text controller state
  static Future<void> saveTextControllerState(
    String controllerId,
    TextEditingController controller,
  ) async {
    final state = {
      'text': controller.text,
      'selection': {
        'start': controller.selection.start,
        'end': controller.selection.end,
      },
    };

    await StateRestorationManager().saveState(
      'text_controller_$controllerId',
      state,
      StateType.form,
    );
  }

  /// Restore text controller state
  static void restoreTextControllerState(
    String controllerId,
    TextEditingController controller,
  ) {
    final state = StateRestorationManager().restoreState(
      'text_controller_$controllerId',
      type: StateType.form,
    );

    if (state != null) {
      controller.text = state['text'] as String? ?? '';

      final selection = state['selection'] as Map<String, dynamic>?;
      if (selection != null) {
        final start = selection['start'] as int? ?? 0;
        final end = selection['end'] as int? ?? 0;
        controller.selection = TextSelection(
          baseOffset: start.clamp(0, controller.text.length),
          extentOffset: end.clamp(0, controller.text.length),
        );
      }
    }
  }
}

/// Scroll position restoration utilities
class ScrollStateRestoration {
  /// Save scroll position
  static Future<void> saveScrollPosition(
    String scrollKey,
    ScrollController controller,
  ) async {
    if (!controller.hasClients) return;

    final state = {
      'offset': controller.offset,
      'maxScrollExtent': controller.position.maxScrollExtent,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };

    await StateRestorationManager().saveState(
      'scroll_$scrollKey',
      state,
      StateType.scroll,
    );
  }

  /// Restore scroll position
  static void restoreScrollPosition(
    String scrollKey,
    ScrollController controller,
  ) {
    final state = StateRestorationManager().restoreState(
      'scroll_$scrollKey',
      type: StateType.scroll,
    );

    if (state != null && controller.hasClients) {
      final offset = state['offset'] as double? ?? 0.0;
      final maxOffset = controller.position.maxScrollExtent;

      // Ensure offset is within valid range
      final targetOffset = offset.clamp(0.0, maxOffset);

      if (targetOffset > 0) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (controller.hasClients) {
            controller.animateTo(
              targetOffset,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
            );
          }
        });
      }
    }
  }
}

/// Search state restoration utilities
class SearchStateRestoration {
  /// Save search state
  static Future<void> saveSearchState(
    String searchKey, {
    required String query,
    List<String>? recentSearches,
    Map<String, dynamic>? filters,
    Map<String, dynamic>? results,
  }) async {
    final state = {
      'query': query,
      'recentSearches': recentSearches ?? [],
      'filters': filters ?? {},
      'results': results ?? {},
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };

    await StateRestorationManager().saveState(
      'search_$searchKey',
      state,
      StateType.search,
    );
  }

  /// Restore search state
  static Map<String, dynamic>? restoreSearchState(String searchKey) {
    return StateRestorationManager().restoreState(
      'search_$searchKey',
      type: StateType.search,
    );
  }
}

import 'dart:async';
import 'dart:isolate';
import 'package:flutter/foundation.dart';

/// Comprehensive efficient data handling system for Dasso Reader
///
/// This system provides:
/// - Pagination and lazy loading patterns
/// - Memory leak detection and prevention
/// - Intelligent caching strategies
/// - Background processing with isolates
/// - Data transformation optimization

/// Pagination configuration for different data types
enum DataType {
  books,
  vocabulary,
  notes,
  searchResults,
  dictionary,
}

/// Pagination configuration
class PaginationConfig {
  final int pageSize;
  final int prefetchThreshold;
  final bool enablePrefetch;
  final Duration cacheTimeout;

  const PaginationConfig({
    required this.pageSize,
    this.prefetchThreshold = 5,
    this.enablePrefetch = true,
    this.cacheTimeout = const Duration(minutes: 10),
  });

  /// Default configurations for different data types
  static const Map<DataType, PaginationConfig> defaults = {
    DataType.books: PaginationConfig(
      pageSize: 20,
      prefetchThreshold: 5,
      enablePrefetch: true,
      cacheTimeout: Duration(minutes: 15),
    ),
    DataType.vocabulary: PaginationConfig(
      pageSize: 50,
      prefetchThreshold: 10,
      enablePrefetch: true,
      cacheTimeout: Duration(minutes: 5),
    ),
    DataType.notes: PaginationConfig(
      pageSize: 30,
      prefetchThreshold: 8,
      enablePrefetch: true,
      cacheTimeout: Duration(minutes: 10),
    ),
    DataType.searchResults: PaginationConfig(
      pageSize: 25,
      prefetchThreshold: 5,
      enablePrefetch: false,
      cacheTimeout: Duration(minutes: 2),
    ),
    DataType.dictionary: PaginationConfig(
      pageSize: 100,
      prefetchThreshold: 20,
      enablePrefetch: true,
      cacheTimeout: Duration(minutes: 30),
    ),
  };

  static PaginationConfig forDataType(DataType type) {
    return defaults[type] ?? defaults[DataType.books]!;
  }
}

/// Paginated data result
class PaginatedResult<T> {
  final List<T> items;
  final int currentPage;
  final int totalPages;
  final int totalItems;
  final bool hasNextPage;
  final bool hasPreviousPage;
  final DateTime timestamp;

  const PaginatedResult({
    required this.items,
    required this.currentPage,
    required this.totalPages,
    required this.totalItems,
    required this.hasNextPage,
    required this.hasPreviousPage,
    required this.timestamp,
  });

  bool get isEmpty => items.isEmpty;
  bool get isNotEmpty => items.isNotEmpty;

  /// Check if data is stale based on cache timeout
  bool isStale(Duration timeout) {
    return DateTime.now().difference(timestamp) > timeout;
  }

  PaginatedResult<T> copyWith({
    List<T>? items,
    int? currentPage,
    int? totalPages,
    int? totalItems,
    bool? hasNextPage,
    bool? hasPreviousPage,
    DateTime? timestamp,
  }) {
    return PaginatedResult<T>(
      items: items ?? this.items,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      totalItems: totalItems ?? this.totalItems,
      hasNextPage: hasNextPage ?? this.hasNextPage,
      hasPreviousPage: hasPreviousPage ?? this.hasPreviousPage,
      timestamp: timestamp ?? this.timestamp,
    );
  }
}

/// Pagination manager for efficient data loading
class PaginationManager<T> {
  final String _cacheKey;
  final PaginationConfig _config;
  final Future<PaginatedResult<T>> Function(int page, int pageSize) _dataLoader;

  final Map<int, PaginatedResult<T>> _pageCache = {};
  final Set<int> _loadingPages = {};
  Timer? _cacheCleanupTimer;

  PaginationManager({
    required String cacheKey,
    required PaginationConfig config,
    required Future<PaginatedResult<T>> Function(int page, int pageSize)
        dataLoader,
  })  : _cacheKey = cacheKey,
        _config = config,
        _dataLoader = dataLoader {
    _startCacheCleanup();
  }

  /// Load a specific page
  Future<PaginatedResult<T>> loadPage(int page) async {
    // Check cache first
    if (_pageCache.containsKey(page)) {
      final cached = _pageCache[page]!;
      if (!cached.isStale(_config.cacheTimeout)) {
        if (kDebugMode) {
          debugPrint(
            '📄 PaginationManager[$_cacheKey]: Cache hit for page $page',
          );
        }
        return cached;
      } else {
        _pageCache.remove(page);
      }
    }

    // Prevent duplicate loading
    if (_loadingPages.contains(page)) {
      // Wait for ongoing load
      while (_loadingPages.contains(page)) {
        await Future<void>.delayed(const Duration(milliseconds: 50));
      }
      return _pageCache[page] ?? await _loadPageData(page);
    }

    return await _loadPageData(page);
  }

  Future<PaginatedResult<T>> _loadPageData(int page) async {
    _loadingPages.add(page);

    try {
      if (kDebugMode) {
        debugPrint('📄 PaginationManager[$_cacheKey]: Loading page $page');
      }

      final result = await _dataLoader(page, _config.pageSize);
      _pageCache[page] = result;

      // Prefetch next page if enabled and conditions are met
      if (_config.enablePrefetch &&
          result.hasNextPage &&
          !_pageCache.containsKey(page + 1) &&
          !_loadingPages.contains(page + 1)) {
        _prefetchPage(page + 1);
      }

      if (kDebugMode) {
        debugPrint(
          '📄 PaginationManager[$_cacheKey]: Loaded page $page with ${result.items.length} items',
        );
      }

      return result;
    } finally {
      _loadingPages.remove(page);
    }
  }

  /// Prefetch a page in the background
  void _prefetchPage(int page) {
    if (kDebugMode) {
      debugPrint('📄 PaginationManager[$_cacheKey]: Prefetching page $page');
    }

    loadPage(page).catchError((Object error) {
      if (kDebugMode) {
        debugPrint(
          '📄 PaginationManager[$_cacheKey]: Prefetch failed for page $page: $error',
        );
      }
      // Return empty result for error case
      return PaginatedResult<T>(
        items: [],
        currentPage: page,
        totalPages: 0,
        totalItems: 0,
        hasNextPage: false,
        hasPreviousPage: false,
        timestamp: DateTime.now(),
      );
    });
  }

  /// Get cached pages count
  int get cachedPagesCount => _pageCache.length;

  /// Get memory usage estimate (rough)
  int get estimatedMemoryUsage {
    return _pageCache.values.fold<int>(
      0,
      (sum, page) => sum + (page.items.length * 1024), // Rough estimate
    );
  }

  /// Clear cache
  void clearCache() {
    _pageCache.clear();
    if (kDebugMode) {
      debugPrint('📄 PaginationManager[$_cacheKey]: Cache cleared');
    }
  }

  /// Start periodic cache cleanup
  void _startCacheCleanup() {
    _cacheCleanupTimer = Timer.periodic(const Duration(minutes: 5), (_) {
      _cleanupStaleCache();
    });
  }

  /// Clean up stale cache entries
  void _cleanupStaleCache() {
    final stalePages = <int>[];

    for (final entry in _pageCache.entries) {
      if (entry.value.isStale(_config.cacheTimeout)) {
        stalePages.add(entry.key);
      }
    }

    for (final page in stalePages) {
      _pageCache.remove(page);
    }

    if (kDebugMode && stalePages.isNotEmpty) {
      debugPrint(
        '📄 PaginationManager[$_cacheKey]: Cleaned up ${stalePages.length} stale pages',
      );
    }
  }

  /// Dispose resources
  void dispose() {
    _cacheCleanupTimer?.cancel();
    _pageCache.clear();
    _loadingPages.clear();

    if (kDebugMode) {
      debugPrint('📄 PaginationManager[$_cacheKey]: Disposed');
    }
  }
}

/// Memory leak detection and prevention system
class MemoryLeakDetector {
  static final MemoryLeakDetector _instance = MemoryLeakDetector._internal();
  factory MemoryLeakDetector() => _instance;
  MemoryLeakDetector._internal();

  final Map<String, _ResourceTracker> _trackers = {};
  Timer? _monitoringTimer;
  bool _isMonitoring = false;

  /// Start memory leak monitoring
  void startMonitoring({Duration interval = const Duration(seconds: 30)}) {
    if (_isMonitoring) return;

    _isMonitoring = true;
    _monitoringTimer = Timer.periodic(interval, (_) => _checkForLeaks());

    if (kDebugMode) {
      debugPrint('🔍 MemoryLeakDetector: Started monitoring');
    }
  }

  /// Stop memory leak monitoring
  void stopMonitoring() {
    _isMonitoring = false;
    _monitoringTimer?.cancel();
    _monitoringTimer = null;

    if (kDebugMode) {
      debugPrint('🔍 MemoryLeakDetector: Stopped monitoring');
    }
  }

  /// Track a resource (controller, stream, etc.)
  void trackResource(String id, String type, {Map<String, dynamic>? metadata}) {
    _trackers[id] = _ResourceTracker(
      id: id,
      type: type,
      createdAt: DateTime.now(),
      metadata: metadata ?? {},
    );

    if (kDebugMode) {
      debugPrint('🔍 MemoryLeakDetector: Tracking $type resource: $id');
    }
  }

  /// Untrack a resource (when properly disposed)
  void untrackResource(String id) {
    final tracker = _trackers.remove(id);
    if (tracker != null && kDebugMode) {
      final lifetime = DateTime.now().difference(tracker.createdAt);
      debugPrint(
        '🔍 MemoryLeakDetector: Untracked ${tracker.type} resource: $id (lifetime: ${lifetime.inSeconds}s)',
      );
    }
  }

  /// Check for potential memory leaks
  void _checkForLeaks() {
    final now = DateTime.now();
    final potentialLeaks = <_ResourceTracker>[];

    for (final tracker in _trackers.values) {
      final age = now.difference(tracker.createdAt);

      // Consider resources older than 10 minutes as potential leaks
      if (age.inMinutes > 10) {
        potentialLeaks.add(tracker);
      }
    }

    if (potentialLeaks.isNotEmpty && kDebugMode) {
      debugPrint(
        '⚠️ MemoryLeakDetector: Found ${potentialLeaks.length} potential leaks:',
      );
      for (final leak in potentialLeaks) {
        final age = now.difference(leak.createdAt);
        debugPrint(
          '  - ${leak.type} (${leak.id}): ${age.inMinutes} minutes old',
        );
      }
    }
  }

  /// Get current tracking statistics
  Map<String, dynamic> getStatistics() {
    final stats = <String, int>{};
    for (final tracker in _trackers.values) {
      stats[tracker.type] = (stats[tracker.type] ?? 0) + 1;
    }

    return {
      'totalTracked': _trackers.length,
      'byType': stats,
      'isMonitoring': _isMonitoring,
    };
  }

  /// Clear all tracking (for testing)
  void clearTracking() {
    _trackers.clear();
    if (kDebugMode) {
      debugPrint('🔍 MemoryLeakDetector: Cleared all tracking');
    }
  }
}

class _ResourceTracker {
  final String id;
  final String type;
  final DateTime createdAt;
  final Map<String, dynamic> metadata;

  _ResourceTracker({
    required this.id,
    required this.type,
    required this.createdAt,
    required this.metadata,
  });
}

/// Intelligent caching system with size limits and LRU eviction
class IntelligentCache<K, V> {
  final String _name;
  final int _maxSize;
  final Duration _defaultTtl;
  final Map<K, _CacheEntry<V>> _cache = {};
  final List<K> _accessOrder = [];
  Timer? _cleanupTimer;

  IntelligentCache({
    required String name,
    int maxSize = 100,
    Duration defaultTtl = const Duration(minutes: 30),
  })  : _name = name,
        _maxSize = maxSize,
        _defaultTtl = defaultTtl {
    _startPeriodicCleanup();
  }

  /// Get value from cache
  V? get(K key) {
    final entry = _cache[key];
    if (entry == null) return null;

    if (entry.isExpired) {
      _cache.remove(key);
      _accessOrder.remove(key);
      return null;
    }

    // Update access order (LRU)
    _accessOrder.remove(key);
    _accessOrder.add(key);

    if (kDebugMode) {
      debugPrint('💾 IntelligentCache[$_name]: Cache hit for key: $key');
    }

    return entry.value;
  }

  /// Put value in cache
  void put(K key, V value, {Duration? ttl}) {
    final entry = _CacheEntry<V>(
      value: value,
      expiresAt: DateTime.now().add(ttl ?? _defaultTtl),
    );

    // Remove existing entry if present
    if (_cache.containsKey(key)) {
      _accessOrder.remove(key);
    }

    _cache[key] = entry;
    _accessOrder.add(key);

    // Evict oldest entries if cache is full
    while (_cache.length > _maxSize) {
      final oldestKey = _accessOrder.removeAt(0);
      _cache.remove(oldestKey);

      if (kDebugMode) {
        debugPrint('💾 IntelligentCache[$_name]: Evicted key: $oldestKey');
      }
    }

    if (kDebugMode) {
      debugPrint('💾 IntelligentCache[$_name]: Cached key: $key');
    }
  }

  /// Check if key exists and is not expired
  bool containsKey(K key) {
    final entry = _cache[key];
    if (entry == null) return false;

    if (entry.isExpired) {
      _cache.remove(key);
      _accessOrder.remove(key);
      return false;
    }

    return true;
  }

  /// Remove specific key
  void remove(K key) {
    _cache.remove(key);
    _accessOrder.remove(key);

    if (kDebugMode) {
      debugPrint('💾 IntelligentCache[$_name]: Removed key: $key');
    }
  }

  /// Clear all cache
  void clear() {
    _cache.clear();
    _accessOrder.clear();

    if (kDebugMode) {
      debugPrint('💾 IntelligentCache[$_name]: Cleared all cache');
    }
  }

  /// Get cache statistics
  Map<String, dynamic> getStatistics() {
    int expiredCount = 0;

    for (final entry in _cache.values) {
      if (entry.isExpired) expiredCount++;
    }

    return {
      'name': _name,
      'size': _cache.length,
      'maxSize': _maxSize,
      'expiredEntries': expiredCount,
      'hitRate': 0.0, // Would need to track hits/misses for accurate rate
    };
  }

  /// Start periodic cleanup of expired entries
  void _startPeriodicCleanup() {
    _cleanupTimer = Timer.periodic(const Duration(minutes: 5), (_) {
      _cleanupExpired();
    });
  }

  /// Clean up expired entries
  void _cleanupExpired() {
    final expiredKeys = <K>[];

    for (final entry in _cache.entries) {
      if (entry.value.isExpired) {
        expiredKeys.add(entry.key);
      }
    }

    for (final key in expiredKeys) {
      _cache.remove(key);
      _accessOrder.remove(key);
    }

    if (kDebugMode && expiredKeys.isNotEmpty) {
      debugPrint(
        '💾 IntelligentCache[$_name]: Cleaned up ${expiredKeys.length} expired entries',
      );
    }
  }

  /// Dispose cache and cleanup timer
  void dispose() {
    _cleanupTimer?.cancel();
    _cache.clear();
    _accessOrder.clear();

    if (kDebugMode) {
      debugPrint('💾 IntelligentCache[$_name]: Disposed');
    }
  }
}

class _CacheEntry<V> {
  final V value;
  final DateTime expiresAt;

  _CacheEntry({
    required this.value,
    required this.expiresAt,
  });

  bool get isExpired => DateTime.now().isAfter(expiresAt);
}

/// Background processing manager using isolates for heavy operations
class BackgroundProcessor {
  static final BackgroundProcessor _instance = BackgroundProcessor._internal();
  factory BackgroundProcessor() => _instance;
  BackgroundProcessor._internal();

  final Map<String, Isolate> _activeIsolates = {};
  final Map<String, ReceivePort> _receivePorts = {};

  /// Process data transformation in background isolate
  Future<T> processDataTransformation<T>({
    required String taskId,
    required dynamic data,
    required String Function(dynamic) transformFunction,
    Duration timeout = const Duration(minutes: 5),
  }) async {
    if (_activeIsolates.containsKey(taskId)) {
      throw StateError('Task $taskId is already running');
    }

    final receivePort = ReceivePort();
    final completer = Completer<T>();

    // Set up timeout
    Timer? timeoutTimer = Timer(timeout, () {
      if (!completer.isCompleted) {
        _cleanupTask(taskId);
        completer
            .completeError(TimeoutException('Task $taskId timed out', timeout));
      }
    });

    try {
      // Create isolate for background processing
      final isolate = await Isolate.spawn(
        _isolateEntryPoint,
        _IsolateMessage(
          sendPort: receivePort.sendPort,
          data: data,
          transformFunction: transformFunction,
        ),
      );

      _activeIsolates[taskId] = isolate;
      _receivePorts[taskId] = receivePort;

      // Listen for result
      receivePort.listen((message) {
        timeoutTimer.cancel();
        _cleanupTask(taskId);

        if (message is _IsolateError) {
          completer.completeError(Exception(message.error));
        } else {
          completer.complete(message as T);
        }
      });

      if (kDebugMode) {
        debugPrint('🔄 BackgroundProcessor: Started task $taskId');
      }

      return await completer.future;
    } catch (error) {
      timeoutTimer.cancel();
      _cleanupTask(taskId);
      rethrow;
    }
  }

  /// Process large list transformation in chunks
  Future<List<T>> processListTransformation<T>({
    required String taskId,
    required List<dynamic> data,
    required T Function(dynamic) itemTransform,
    int chunkSize = 100,
  }) async {
    if (data.isEmpty) return [];

    final chunks = <List<dynamic>>[];
    for (int i = 0; i < data.length; i += chunkSize) {
      final end = (i + chunkSize < data.length) ? i + chunkSize : data.length;
      chunks.add(data.sublist(i, end));
    }

    final results = <T>[];

    for (int i = 0; i < chunks.length; i++) {
      final chunkTaskId = '${taskId}_chunk_$i';

      try {
        final chunkResult = await processDataTransformation<List<T>>(
          taskId: chunkTaskId,
          data: chunks[i],
          transformFunction: (chunk) {
            return (chunk as List<dynamic>)
                .map((item) => itemTransform(item))
                .toList()
                .toString(); // Convert to string for isolate communication
          },
        );

        results.addAll(chunkResult);

        if (kDebugMode) {
          debugPrint(
            '🔄 BackgroundProcessor: Processed chunk ${i + 1}/${chunks.length} for task $taskId',
          );
        }
      } catch (error) {
        if (kDebugMode) {
          debugPrint(
            '🔄 BackgroundProcessor: Error processing chunk ${i + 1} for task $taskId: $error',
          );
        }
        rethrow;
      }
    }

    return results;
  }

  /// Cancel a running task
  void cancelTask(String taskId) {
    _cleanupTask(taskId);
    if (kDebugMode) {
      debugPrint('🔄 BackgroundProcessor: Cancelled task $taskId');
    }
  }

  /// Get active tasks count
  int get activeTasksCount => _activeIsolates.length;

  /// Get active task IDs
  List<String> get activeTaskIds => _activeIsolates.keys.toList();

  /// Cleanup task resources
  void _cleanupTask(String taskId) {
    _activeIsolates[taskId]?.kill();
    _activeIsolates.remove(taskId);
    _receivePorts[taskId]?.close();
    _receivePorts.remove(taskId);
  }

  /// Cleanup all tasks
  void dispose() {
    for (final taskId in _activeIsolates.keys.toList()) {
      _cleanupTask(taskId);
    }

    if (kDebugMode) {
      debugPrint('🔄 BackgroundProcessor: Disposed all tasks');
    }
  }

  /// Isolate entry point
  static void _isolateEntryPoint(_IsolateMessage message) {
    try {
      // This is a simplified version - in practice, you'd need to handle
      // the actual transformation logic based on the function type
      final result = message.transformFunction(message.data);
      message.sendPort.send(result);
    } catch (error) {
      message.sendPort.send(_IsolateError(error.toString()));
    }
  }
}

class _IsolateMessage {
  final SendPort sendPort;
  final dynamic data;
  final String Function(dynamic) transformFunction;

  _IsolateMessage({
    required this.sendPort,
    required this.data,
    required this.transformFunction,
  });
}

class _IsolateError {
  final String error;
  _IsolateError(this.error);
}

/// Data transformation optimizer to prevent UI jank
class DataTransformationOptimizer {
  static const int _maxSyncItems = 50;
  static const Duration _frameTime = Duration(milliseconds: 16); // 60 FPS

  /// Transform data with automatic async handling for large datasets
  static Future<List<T>> optimizedTransform<T>({
    required List<dynamic> data,
    required T Function(dynamic) transform,
    String? taskId,
  }) async {
    if (data.length <= _maxSyncItems) {
      // Small dataset - process synchronously
      return data.map(transform).toList();
    }

    // Large dataset - process in background
    taskId ??= 'transform_${DateTime.now().millisecondsSinceEpoch}';

    return await BackgroundProcessor().processListTransformation<T>(
      taskId: taskId,
      data: data,
      itemTransform: transform,
    );
  }

  /// Transform data in chunks with frame-aware processing
  static Future<List<T>> frameAwareTransform<T>({
    required List<dynamic> data,
    required T Function(dynamic) transform,
    int chunkSize = 10,
  }) async {
    final results = <T>[];
    final stopwatch = Stopwatch()..start();

    for (int i = 0; i < data.length; i += chunkSize) {
      final end = (i + chunkSize < data.length) ? i + chunkSize : data.length;
      final chunk = data.sublist(i, end);

      // Process chunk
      results.addAll(chunk.map(transform));

      // Check if we're approaching frame time limit
      if (stopwatch.elapsed > _frameTime) {
        // Yield to allow UI updates
        await Future<void>.delayed(Duration.zero);
        stopwatch.reset();
      }
    }

    return results;
  }

  /// Debounced transformation for frequently changing data
  static Timer? _debounceTimer;
  static Future<List<T>> debouncedTransform<T>({
    required List<dynamic> data,
    required T Function(dynamic) transform,
    Duration delay = const Duration(milliseconds: 300),
  }) async {
    final completer = Completer<List<T>>();

    _debounceTimer?.cancel();
    _debounceTimer = Timer(delay, () async {
      try {
        final result = await optimizedTransform<T>(
          data: data,
          transform: transform,
        );
        completer.complete(result);
      } catch (error) {
        completer.completeError(error);
      }
    });

    return completer.future;
  }
}

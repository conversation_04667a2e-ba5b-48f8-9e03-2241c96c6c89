import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'efficient_data_handling.dart';

/// Lazy loading list widget with pagination support
class LazyLoadingListView<T> extends ConsumerStatefulWidget {
  final PaginationManager<T> paginationManager;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final Widget Function(BuildContext context)? loadingBuilder;
  final Widget Function(BuildContext context, String error)? errorBuilder;
  final Widget Function(BuildContext context)? emptyBuilder;
  final EdgeInsets? padding;
  final ScrollController? scrollController;
  final bool shrinkWrap;
  final ScrollPhysics? physics;

  const LazyLoadingListView({
    super.key,
    required this.paginationManager,
    required this.itemBuilder,
    this.loadingBuilder,
    this.errorBuilder,
    this.emptyBuilder,
    this.padding,
    this.scrollController,
    this.shrinkWrap = false,
    this.physics,
  });

  @override
  ConsumerState<LazyLoadingListView<T>> createState() =>
      _LazyLoadingListViewState<T>();
}

class _LazyLoadingListViewState<T>
    extends ConsumerState<LazyLoadingListView<T>> {
  late ScrollController _scrollController;
  final List<T> _allItems = [];
  int _currentPage = 1;
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  bool _hasMoreData = true;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.scrollController ?? ScrollController();
    _scrollController.addListener(_onScroll);
    _loadInitialData();
  }

  @override
  void dispose() {
    if (widget.scrollController == null) {
      _scrollController.dispose();
    } else {
      _scrollController.removeListener(_onScroll);
    }
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreData();
    }
  }

  Future<void> _loadInitialData() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      final result = await widget.paginationManager.loadPage(1);
      setState(() {
        _allItems.clear();
        _allItems.addAll(result.items);
        _currentPage = 1;
        _hasMoreData = result.hasNextPage;
        _isLoading = false;
      });
    } catch (error) {
      setState(() {
        _hasError = true;
        _errorMessage = error.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _loadMoreData() async {
    if (_isLoading || !_hasMoreData) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final nextPage = _currentPage + 1;
      final result = await widget.paginationManager.loadPage(nextPage);

      setState(() {
        _allItems.addAll(result.items);
        _currentPage = nextPage;
        _hasMoreData = result.hasNextPage;
        _isLoading = false;
      });
    } catch (error) {
      setState(() {
        _isLoading = false;
      });

      if (kDebugMode) {
        debugPrint('Error loading more data: $error');
      }
    }
  }

  Future<void> refresh() async {
    await _loadInitialData();
  }

  @override
  Widget build(BuildContext context) {
    if (_hasError && _allItems.isEmpty) {
      return widget.errorBuilder?.call(context, _errorMessage) ??
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, size: 48, color: Colors.red),
                const SizedBox(height: 16),
                Text('Error: $_errorMessage'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _loadInitialData,
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
    }

    if (_allItems.isEmpty && _isLoading) {
      return widget.loadingBuilder?.call(context) ??
          const Center(child: CircularProgressIndicator());
    }

    if (_allItems.isEmpty) {
      return widget.emptyBuilder?.call(context) ??
          const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.inbox_outlined, size: 48, color: Colors.grey),
                SizedBox(height: 16),
                Text('No items found'),
              ],
            ),
          );
    }

    return RefreshIndicator(
      onRefresh: refresh,
      child: ListView.builder(
        controller: _scrollController,
        padding: widget.padding,
        shrinkWrap: widget.shrinkWrap,
        physics: widget.physics,
        itemCount: _allItems.length + (_hasMoreData ? 1 : 0),
        itemBuilder: (context, index) {
          if (index < _allItems.length) {
            return widget.itemBuilder(context, _allItems[index], index);
          } else {
            // Loading indicator for more items
            return Container(
              padding: const EdgeInsets.all(16),
              alignment: Alignment.center,
              child: _isLoading
                  ? const CircularProgressIndicator()
                  : const SizedBox.shrink(),
            );
          }
        },
      ),
    );
  }
}

/// Lazy loading grid widget with pagination support
class LazyLoadingGridView<T> extends ConsumerStatefulWidget {
  final PaginationManager<T> paginationManager;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final int crossAxisCount;
  final double mainAxisSpacing;
  final double crossAxisSpacing;
  final double childAspectRatio;
  final Widget Function(BuildContext context)? loadingBuilder;
  final Widget Function(BuildContext context, String error)? errorBuilder;
  final Widget Function(BuildContext context)? emptyBuilder;
  final EdgeInsets? padding;
  final ScrollController? scrollController;
  final bool shrinkWrap;
  final ScrollPhysics? physics;

  const LazyLoadingGridView({
    super.key,
    required this.paginationManager,
    required this.itemBuilder,
    this.crossAxisCount = 2,
    this.mainAxisSpacing = 8.0,
    this.crossAxisSpacing = 8.0,
    this.childAspectRatio = 1.0,
    this.loadingBuilder,
    this.errorBuilder,
    this.emptyBuilder,
    this.padding,
    this.scrollController,
    this.shrinkWrap = false,
    this.physics,
  });

  @override
  ConsumerState<LazyLoadingGridView<T>> createState() =>
      _LazyLoadingGridViewState<T>();
}

class _LazyLoadingGridViewState<T>
    extends ConsumerState<LazyLoadingGridView<T>> {
  late ScrollController _scrollController;
  final List<T> _allItems = [];
  int _currentPage = 1;
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  bool _hasMoreData = true;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.scrollController ?? ScrollController();
    _scrollController.addListener(_onScroll);
    _loadInitialData();
  }

  @override
  void dispose() {
    if (widget.scrollController == null) {
      _scrollController.dispose();
    } else {
      _scrollController.removeListener(_onScroll);
    }
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreData();
    }
  }

  Future<void> _loadInitialData() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      final result = await widget.paginationManager.loadPage(1);
      setState(() {
        _allItems.clear();
        _allItems.addAll(result.items);
        _currentPage = 1;
        _hasMoreData = result.hasNextPage;
        _isLoading = false;
      });
    } catch (error) {
      setState(() {
        _hasError = true;
        _errorMessage = error.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _loadMoreData() async {
    if (_isLoading || !_hasMoreData) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final nextPage = _currentPage + 1;
      final result = await widget.paginationManager.loadPage(nextPage);

      setState(() {
        _allItems.addAll(result.items);
        _currentPage = nextPage;
        _hasMoreData = result.hasNextPage;
        _isLoading = false;
      });
    } catch (error) {
      setState(() {
        _isLoading = false;
      });

      if (kDebugMode) {
        debugPrint('Error loading more data: $error');
      }
    }
  }

  Future<void> refresh() async {
    await _loadInitialData();
  }

  @override
  Widget build(BuildContext context) {
    if (_hasError && _allItems.isEmpty) {
      return widget.errorBuilder?.call(context, _errorMessage) ??
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, size: 48, color: Colors.red),
                const SizedBox(height: 16),
                Text('Error: $_errorMessage'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _loadInitialData,
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
    }

    if (_allItems.isEmpty && _isLoading) {
      return widget.loadingBuilder?.call(context) ??
          const Center(child: CircularProgressIndicator());
    }

    if (_allItems.isEmpty) {
      return widget.emptyBuilder?.call(context) ??
          const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.inbox_outlined, size: 48, color: Colors.grey),
                SizedBox(height: 16),
                Text('No items found'),
              ],
            ),
          );
    }

    return RefreshIndicator(
      onRefresh: refresh,
      child: GridView.builder(
        controller: _scrollController,
        padding: widget.padding,
        shrinkWrap: widget.shrinkWrap,
        physics: widget.physics,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: widget.crossAxisCount,
          mainAxisSpacing: widget.mainAxisSpacing,
          crossAxisSpacing: widget.crossAxisSpacing,
          childAspectRatio: widget.childAspectRatio,
        ),
        itemCount:
            _allItems.length + (_hasMoreData ? widget.crossAxisCount : 0),
        itemBuilder: (context, index) {
          if (index < _allItems.length) {
            return widget.itemBuilder(context, _allItems[index], index);
          } else {
            // Loading indicator for more items
            return Container(
              alignment: Alignment.center,
              child: _isLoading
                  ? const CircularProgressIndicator()
                  : const SizedBox.shrink(),
            );
          }
        },
      ),
    );
  }
}

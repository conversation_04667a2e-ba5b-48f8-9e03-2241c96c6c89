import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dasso_reader/utils/state_management/state_restoration.dart';
import 'package:dasso_reader/utils/log/common.dart';

/// Centralized app state manager that integrates all state management systems
///
/// This manager coordinates:
/// - State restoration system
/// - SharedPreferences persistence
/// - App lifecycle management
/// - Navigation state preservation
/// - Critical user data protection

class AppStateManager {
  static final AppStateManager _instance = AppStateManager._internal();
  factory AppStateManager() => _instance;
  AppStateManager._internal();

  bool _initialized = false;
  Timer? _periodicSaveTimer;

  /// Initialize the app state management system
  Future<void> initialize() async {
    if (_initialized) return;

    try {
      // Initialize state restoration system
      await StateRestorationManager().initialize(
        config: const StateRestorationConfig(
          enabled: true,
          autoSaveInterval: Duration(seconds: 30),
          enabledTypes: {
            StateType.navigation,
            StateType.form,
            StateType.scroll,
            StateType.ui,
            StateType.search,
          },
          maxHistoryEntries: 100,
          debugMode: kDebugMode,
        ),
      );

      // Start periodic state saving
      _startPeriodicSave();

      _initialized = true;

      if (kDebugMode) {
        AnxLog.info('🚀 App State Manager initialized successfully');
      }
    } catch (e) {
      AnxLog.severe('Failed to initialize App State Manager: $e');
    }
  }

  /// Handle app lifecycle state changes
  Future<void> handleAppLifecycleChange(AppLifecycleState state) async {
    if (!_initialized) return;

    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.hidden:
        await _saveAllCriticalState();
        break;
      case AppLifecycleState.resumed:
        await _restoreAppState();
        break;
      case AppLifecycleState.detached:
        await _saveAllCriticalState();
        await dispose();
        break;
      case AppLifecycleState.inactive:
        // No action needed for inactive state
        break;
    }
  }

  /// Save all critical app state
  Future<void> _saveAllCriticalState() async {
    try {
      // Save current timestamp for state restoration
      await StateRestorationManager().saveState(
        'app_lifecycle',
        {
          'lastSaveTime': DateTime.now().millisecondsSinceEpoch,
          'appVersion': '1.0.0', // Could be dynamic
        },
        StateType.temporary,
      );

      if (kDebugMode) {
        AnxLog.info('💾 Saved all critical app state');
      }
    } catch (e) {
      AnxLog.warning('Failed to save critical app state: $e');
    }
  }

  /// Restore app state on resume
  Future<void> _restoreAppState() async {
    try {
      final appState = StateRestorationManager().restoreState(
        'app_lifecycle',
        type: StateType.temporary,
      );

      if (appState != null) {
        final lastSaveTime = appState['lastSaveTime'] as int?;
        if (lastSaveTime != null) {
          final timeDiff = DateTime.now().millisecondsSinceEpoch - lastSaveTime;
          final hoursDiff = timeDiff / (1000 * 60 * 60);

          if (kDebugMode) {
            AnxLog.info(
              '🔄 App was backgrounded for ${hoursDiff.toStringAsFixed(1)} hours',
            );
          }

          // If app was backgrounded for more than 24 hours, clear temporary state
          if (hoursDiff > 24) {
            await _clearOldTemporaryState();
          }
        }
      }
    } catch (e) {
      AnxLog.warning('Failed to restore app state: $e');
    }
  }

  /// Clear old temporary state
  Future<void> _clearOldTemporaryState() async {
    try {
      final tempKeys =
          StateRestorationManager().getStateKeys(type: StateType.temporary);
      for (final key in tempKeys) {
        await StateRestorationManager().clearState(key);
      }

      if (kDebugMode) {
        AnxLog.info('🗑️ Cleared old temporary state');
      }
    } catch (e) {
      AnxLog.warning('Failed to clear old temporary state: $e');
    }
  }

  /// Start periodic state saving
  void _startPeriodicSave() {
    _periodicSaveTimer?.cancel();
    _periodicSaveTimer = Timer.periodic(const Duration(minutes: 5), (_) {
      _saveAllCriticalState();
    });
  }

  /// Stop periodic state saving
  void _stopPeriodicSave() {
    _periodicSaveTimer?.cancel();
    _periodicSaveTimer = null;
  }

  /// Save navigation state
  Future<void> saveNavigationState({
    required int currentIndex,
    required String currentRoute,
    List<String>? routeHistory,
    Map<String, dynamic>? additionalData,
  }) async {
    await NavigationStateRestoration.saveNavigationState(
      currentIndex: currentIndex,
      currentRoute: currentRoute,
      routeHistory: routeHistory,
      additionalData: additionalData,
    );
  }

  /// Restore navigation state
  Map<String, dynamic>? restoreNavigationState() {
    return NavigationStateRestoration.restoreNavigationState();
  }

  /// Save form state
  Future<void> saveFormState(
    String formKey,
    Map<String, dynamic> formData,
  ) async {
    await FormStateRestoration.saveFormState(formKey, formData);
  }

  /// Restore form state
  Map<String, dynamic>? restoreFormState(String formKey) {
    return FormStateRestoration.restoreFormState(formKey);
  }

  /// Save text controller state
  Future<void> saveTextControllerState(
    String controllerId,
    TextEditingController controller,
  ) async {
    await FormStateRestoration.saveTextControllerState(
      controllerId,
      controller,
    );
  }

  /// Restore text controller state
  void restoreTextControllerState(
    String controllerId,
    TextEditingController controller,
  ) {
    FormStateRestoration.restoreTextControllerState(controllerId, controller);
  }

  /// Save scroll position
  Future<void> saveScrollPosition(
    String scrollKey,
    ScrollController controller,
  ) async {
    await ScrollStateRestoration.saveScrollPosition(scrollKey, controller);
  }

  /// Restore scroll position
  void restoreScrollPosition(String scrollKey, ScrollController controller) {
    ScrollStateRestoration.restoreScrollPosition(scrollKey, controller);
  }

  /// Save search state
  Future<void> saveSearchState(
    String searchKey, {
    required String query,
    List<String>? recentSearches,
    Map<String, dynamic>? filters,
    Map<String, dynamic>? results,
  }) async {
    await SearchStateRestoration.saveSearchState(
      searchKey,
      query: query,
      recentSearches: recentSearches,
      filters: filters,
      results: results,
    );
  }

  /// Restore search state
  Map<String, dynamic>? restoreSearchState(String searchKey) {
    return SearchStateRestoration.restoreSearchState(searchKey);
  }

  /// Get state restoration statistics
  Map<String, dynamic> getStateStatistics() {
    final allKeys = StateRestorationManager().getStateKeys();
    final stats = <String, int>{};

    for (final type in StateType.values) {
      final keysForType = StateRestorationManager().getStateKeys(type: type);
      stats[type.name] = keysForType.length;
    }

    return {
      'totalStates': allKeys.length,
      'byType': stats,
      'initialized': _initialized,
    };
  }

  /// Clear all state data
  Future<void> clearAllState() async {
    await StateRestorationManager().clearAllState();

    if (kDebugMode) {
      AnxLog.info('🗑️ Cleared all app state data');
    }
  }

  /// Dispose resources
  Future<void> dispose() async {
    _stopPeriodicSave();
    await _saveAllCriticalState();
    await StateRestorationManager().dispose();
    _initialized = false;

    if (kDebugMode) {
      AnxLog.info('🔄 App State Manager disposed');
    }
  }
}

/// Provider for app state manager
final appStateManagerProvider = Provider<AppStateManager>((ref) {
  return AppStateManager();
});

/// Mixin for widgets that need app state management
mixin AppStateMixin<T extends StatefulWidget> on State<T> {
  AppStateManager get appStateManager => AppStateManager();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _restoreWidgetState();
    });
  }

  @override
  void dispose() {
    _saveWidgetState();
    super.dispose();
  }

  /// Override this to define widget-specific state key
  String get stateKey => runtimeType.toString();

  /// Override this to save widget-specific state
  Map<String, dynamic> getWidgetState() => {};

  /// Override this to restore widget-specific state
  void setWidgetState(Map<String, dynamic> state) {}

  /// Save widget state
  void _saveWidgetState() {
    final state = getWidgetState();
    if (state.isNotEmpty) {
      appStateManager.saveFormState(stateKey, state);
    }
  }

  /// Restore widget state
  void _restoreWidgetState() {
    final state = appStateManager.restoreFormState(stateKey);
    if (state != null) {
      setWidgetState(state);
    }
  }
}

import 'package:flutter/foundation.dart';

/// Performance logging configuration utility
///
/// This class provides easy control over performance logging verbosity
/// for different development scenarios.
class PerformanceLoggingConfig {
  /// Current logging mode
  static PerformanceLoggingMode _currentMode = PerformanceLoggingMode.quiet;

  /// Get current logging mode
  static PerformanceLoggingMode get currentMode => _currentMode;

  /// Set logging mode
  static void setMode(PerformanceLoggingMode mode) {
    _currentMode = mode;
  }

  /// Check if jank logging should be enabled
  static bool get enableJankLogging {
    switch (_currentMode) {
      case PerformanceLoggingMode.quiet:
        return false;
      case PerformanceLoggingMode.minimal:
        return false;
      case PerformanceLoggingMode.verbose:
        return !kReleaseMode;
      case PerformanceLoggingMode.debug:
        return !kReleaseMode;
    }
  }

  /// Check if detailed performance logging should be enabled
  static bool get enableDetailedLogging {
    switch (_currentMode) {
      case PerformanceLoggingMode.quiet:
        return false;
      case PerformanceLoggingMode.minimal:
        return false;
      case PerformanceLoggingMode.verbose:
        return !kReleaseMode;
      case PerformanceLoggingMode.debug:
        return !kReleaseMode;
    }
  }

  /// Check if optimization summary logging should be enabled
  static bool get enableOptimizationLogging {
    switch (_currentMode) {
      case PerformanceLoggingMode.quiet:
        return false;
      case PerformanceLoggingMode.minimal:
        return false;
      case PerformanceLoggingMode.verbose:
        return !kReleaseMode;
      case PerformanceLoggingMode.debug:
        return !kReleaseMode;
    }
  }

  /// Check if initialization logging should be enabled
  static bool get enableInitializationLogging {
    switch (_currentMode) {
      case PerformanceLoggingMode.quiet:
        return false;
      case PerformanceLoggingMode.minimal:
        return !kReleaseMode; // Show basic initialization
      case PerformanceLoggingMode.verbose:
        return !kReleaseMode;
      case PerformanceLoggingMode.debug:
        return !kReleaseMode;
    }
  }

  /// Get configuration description
  static String get modeDescription {
    switch (_currentMode) {
      case PerformanceLoggingMode.quiet:
        return 'Quiet - No performance logging (recommended for development)';
      case PerformanceLoggingMode.minimal:
        return 'Minimal - Basic initialization logging only';
      case PerformanceLoggingMode.verbose:
        return 'Verbose - Detailed performance monitoring';
      case PerformanceLoggingMode.debug:
        return 'Debug - Full performance debugging output';
    }
  }
}

/// Performance logging modes
enum PerformanceLoggingMode {
  /// No performance logging - cleanest development experience
  quiet,

  /// Basic initialization logging only
  minimal,

  /// Detailed performance monitoring
  verbose,

  /// Full debugging output including jank detection
  debug,
}

/// Extension methods for easy mode switching
extension PerformanceLoggingModeExtension on PerformanceLoggingMode {
  /// Quick method to set this mode as current
  void activate() {
    PerformanceLoggingConfig.setMode(this);
  }
}

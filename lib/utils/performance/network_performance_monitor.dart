import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:dasso_reader/utils/log/common.dart';

/// Network performance monitoring and optimization system
class NetworkPerformanceMonitor {
  bool _isInitialized = false;
  bool _isMonitoring = false;
  NetworkConfig _config = NetworkConfig.defaultConfig();

  final List<NetworkRequestMetrics> _requestHistory = [];
  Duration _averageResponseTime = Duration.zero;
  int _totalRequests = 0;
  int _failedRequests = 0;

  Future<void> initialize(NetworkConfig config) async {
    if (_isInitialized) return;
    _config = config;
    _isInitialized = true;
    if (kDebugMode) {
      AnxLog.info('🌐 NetworkPerformanceMonitor: Initialized');
    }
  }

  void startMonitoring() {
    if (!_isInitialized || _isMonitoring) return;
    _isMonitoring = true;
    if (kDebugMode) {
      AnxLog.info('🌐 NetworkPerformanceMonitor: Started monitoring');
    }
  }

  void stopMonitoring() {
    if (!_isMonitoring) return;
    _isMonitoring = false;
    if (kDebugMode) {
      AnxLog.info('🌐 NetworkPerformanceMonitor: Stopped monitoring');
    }
  }

  NetworkMetrics getMetrics() {
    return NetworkMetrics(
      averageResponseTime: _averageResponseTime,
      totalRequests: _totalRequests,
      failedRequests: _failedRequests,
      successRate: _totalRequests > 0
          ? ((_totalRequests - _failedRequests) / _totalRequests * 100)
          : 100.0,
      performanceScore: _calculatePerformanceScore(),
    );
  }

  void trackRequest(NetworkRequestMetrics metrics) {
    if (!_isMonitoring) return;

    _requestHistory.add(metrics);
    _totalRequests++;

    if (!metrics.isSuccess) {
      _failedRequests++;
    }

    _updateAverageResponseTime();
    _maintainHistory();
  }

  void _updateAverageResponseTime() {
    if (_requestHistory.isEmpty) return;

    final totalTime = _requestHistory.fold<Duration>(
      Duration.zero,
      (sum, request) => sum + request.responseTime,
    );

    _averageResponseTime = Duration(
      microseconds: totalTime.inMicroseconds ~/ _requestHistory.length,
    );
  }

  void _maintainHistory() {
    while (_requestHistory.length > _config.maxRequestHistory) {
      _requestHistory.removeAt(0);
    }
  }

  double _calculatePerformanceScore() {
    if (_totalRequests == 0) return 100.0;

    final successRate =
        (_totalRequests - _failedRequests) / _totalRequests * 100;
    final responseTimeScore = _averageResponseTime.inMilliseconds < 1000
        ? 100.0
        : _averageResponseTime.inMilliseconds < 3000
            ? 80.0
            : 60.0;

    return (successRate * 0.6 + responseTimeScore * 0.4).clamp(0.0, 100.0);
  }

  void dispose() {
    stopMonitoring();
    _requestHistory.clear();
    _isInitialized = false;
    if (kDebugMode) {
      AnxLog.info('🌐 NetworkPerformanceMonitor: Disposed');
    }
  }
}

class NetworkConfig {
  const NetworkConfig({
    required this.maxRequestHistory,
    required this.enableRequestLogging,
  });

  final int maxRequestHistory;
  final bool enableRequestLogging;

  factory NetworkConfig.defaultConfig() {
    return const NetworkConfig(
      maxRequestHistory: 100,
      enableRequestLogging: kDebugMode,
    );
  }
}

class NetworkMetrics {
  const NetworkMetrics({
    required this.averageResponseTime,
    required this.totalRequests,
    required this.failedRequests,
    required this.successRate,
    required this.performanceScore,
  });

  final Duration averageResponseTime;
  final int totalRequests;
  final int failedRequests;
  final double successRate;
  final double performanceScore;
}

class NetworkRequestMetrics {
  const NetworkRequestMetrics({
    required this.url,
    required this.method,
    required this.responseTime,
    required this.statusCode,
    required this.requestSize,
    required this.responseSize,
    required this.timestamp,
  });

  final String url;
  final String method;
  final Duration responseTime;
  final int statusCode;
  final int requestSize;
  final int responseSize;
  final DateTime timestamp;

  bool get isSuccess => statusCode >= 200 && statusCode < 300;
}

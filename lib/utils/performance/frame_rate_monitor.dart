import 'dart:async';
import 'dart:collection';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/utils/performance/performance_logging_config.dart';

/// Frame rate monitoring and jank detection system
///
/// This system provides:
/// - Real-time FPS tracking with 60/120 FPS support
/// - Jank detection with configurable thresholds
/// - Frame time analysis and optimization suggestions
/// - GPU rendering optimization monitoring
class FrameRateMonitor {
  bool _isInitialized = false;
  bool _isMonitoring = false;
  FrameRateConfig _config = FrameRateConfig.defaultConfig();

  // Frame timing data
  final Queue<Duration> _frameTimes = Queue<Duration>();
  final Queue<DateTime> _frameTimestamps = Queue<DateTime>();
  DateTime? _lastFrameTime;

  // Performance metrics
  double _currentFPS = 0.0;
  int _jankCount = 0;
  int _totalFrames = 0;
  Duration _totalFrameTime = Duration.zero;

  // Jank detection
  final List<JankEvent> _jankEvents = [];

  // Frame callback registration
  int? _frameCallbackId;

  /// Initialize the frame rate monitor
  Future<void> initialize(FrameRateConfig config) async {
    if (_isInitialized) return;

    _config = config;
    _isInitialized = true;

    // Log in both debug and profile modes
    AnxLog.info(
      '🎯 FrameRateMonitor: Initialized with target ${_config.targetFPS} FPS',
    );
  }

  /// Start frame rate monitoring
  void startMonitoring() {
    if (!_isInitialized || _isMonitoring) return;

    _isMonitoring = true;
    _resetMetrics();
    _startFrameCallback();

    // Log in both debug and profile modes
    AnxLog.info('🎯 FrameRateMonitor: Started monitoring');
  }

  /// Stop frame rate monitoring
  void stopMonitoring() {
    if (!_isMonitoring) return;

    _isMonitoring = false;
    _stopFrameCallback();

    if (kDebugMode) {
      AnxLog.info('🎯 FrameRateMonitor: Stopped monitoring');
    }
  }

  /// Get current frame rate metrics
  FrameRateMetrics getMetrics() {
    return FrameRateMetrics(
      currentFPS: _currentFPS,
      averageFPS: _calculateAverageFPS(),
      targetFPS: _config.targetFPS,
      jankCount: _jankCount,
      totalFrames: _totalFrames,
      jankEvents: List.from(_jankEvents),
      performanceScore: _calculatePerformanceScore(),
    );
  }

  /// Get current FPS
  double get currentFPS => _currentFPS;

  /// Check if currently experiencing jank
  bool get isJanky =>
      _frameTimes.isNotEmpty &&
      _frameTimes.last.inMicroseconds > _config.jankThresholdMicroseconds;

  /// Start frame timing callback
  void _startFrameCallback() {
    _frameCallbackId =
        SchedulerBinding.instance.scheduleFrameCallback(_onFrame);
  }

  /// Stop frame timing callback
  void _stopFrameCallback() {
    if (_frameCallbackId != null) {
      SchedulerBinding.instance.cancelFrameCallbackWithId(_frameCallbackId!);
      _frameCallbackId = null;
    }
  }

  /// Handle frame callback
  void _onFrame(Duration timestamp) {
    if (!_isMonitoring) return;

    final now = DateTime.now();

    if (_lastFrameTime != null) {
      final frameTime = now.difference(_lastFrameTime!);
      _processFrame(frameTime, now);
    }

    _lastFrameTime = now;
    _totalFrames++;

    // Schedule next frame callback
    if (_isMonitoring) {
      _frameCallbackId =
          SchedulerBinding.instance.scheduleFrameCallback(_onFrame);
    }
  }

  /// Process individual frame timing
  void _processFrame(Duration frameTime, DateTime timestamp) {
    // Add to frame time history
    _frameTimes.add(frameTime);
    _frameTimestamps.add(timestamp);
    _totalFrameTime += frameTime;

    // Maintain sliding window
    while (_frameTimes.length > _config.frameHistorySize) {
      _totalFrameTime -= _frameTimes.removeFirst();
      _frameTimestamps.removeFirst();
    }

    // Calculate current FPS
    _currentFPS = _calculateCurrentFPS();

    // Check for jank
    _checkForJank(frameTime, timestamp);

    // Log performance issues in debug and profile modes
    if (!kReleaseMode && _config.enableDebugLogging) {
      _logPerformanceIssues(frameTime);
    }
  }

  /// Calculate current FPS based on recent frames
  double _calculateCurrentFPS() {
    if (_frameTimes.isEmpty) return 0.0;

    // Use a sliding window of recent frames for more stable FPS calculation
    final windowSize = math.min(_frameTimes.length, 30); // Last 30 frames
    final skipCount = _frameTimes.length - windowSize;
    final recentFrames = _frameTimes.skip(skipCount);

    final totalTime = recentFrames.fold<Duration>(
      Duration.zero,
      (sum, frameTime) => sum + frameTime,
    );

    if (totalTime.inMicroseconds == 0) return 0.0;

    // Calculate FPS: frames per second = (frame_count * 1_000_000) / total_microseconds
    final fps = (windowSize * 1000000) / totalTime.inMicroseconds;

    // Clamp to reasonable values to prevent calculation errors
    return fps.clamp(0.0, 240.0); // Max 240 FPS to catch calculation errors
  }

  /// Calculate average FPS over entire monitoring period
  double _calculateAverageFPS() {
    if (_totalFrames == 0 || _totalFrameTime.inMicroseconds == 0) return 0.0;

    // Calculate average FPS with protection against extreme values
    final avgFPS = (_totalFrames * 1000000) / _totalFrameTime.inMicroseconds;

    // Clamp to reasonable values to prevent calculation errors
    return avgFPS.clamp(0.0, 240.0); // Max 240 FPS to catch calculation errors
  }

  /// Check for jank and record events with enhanced detection
  void _checkForJank(Duration frameTime, DateTime timestamp) {
    final frameMicroseconds = frameTime.inMicroseconds;

    if (frameMicroseconds > _config.jankThresholdMicroseconds) {
      _jankCount++;

      final severity = _getJankSeverity(frameMicroseconds);
      final jankEvent = JankEvent(
        timestamp: timestamp,
        frameTime: frameTime,
        severity: severity,
        expectedFrameTime:
            Duration(microseconds: (1000000 / _config.targetFPS).round()),
      );

      _jankEvents.add(jankEvent);

      // Maintain jank event history
      while (_jankEvents.length > _config.maxJankEvents) {
        _jankEvents.removeAt(0);
      }

      // Enhanced logging with context and suggestions
      if (!kReleaseMode && PerformanceLoggingConfig.enableJankLogging) {
        _logJankWithContext(frameTime, severity, timestamp);
      }
    }
  }

  /// Enhanced jank logging with context and optimization suggestions
  void _logJankWithContext(
    Duration frameTime,
    JankSeverity severity,
    DateTime timestamp,
  ) {
    final ms = frameTime.inMilliseconds;
    final fps = (1000 / ms).toStringAsFixed(1);

    // Detect jank patterns
    final recentJankCount = _jankEvents
        .where((event) => timestamp.difference(event.timestamp).inSeconds < 5)
        .length;

    String context = '';
    if (recentJankCount > 5) {
      context = ' [PATTERN: $recentJankCount janks in 5s]';
    }

    // Provide optimization hints based on severity
    String hint = '';
    switch (severity) {
      case JankSeverity.severe:
        if (ms > 100) {
          hint =
              ' 💡 Check for heavy computations, large widget builds, or I/O operations';
        }
        break;
      case JankSeverity.moderate:
        if (ms > 33) {
          hint = ' 💡 Consider async operations or widget optimization';
        }
        break;
      case JankSeverity.mild:
        if (recentJankCount > 3) {
          hint = ' 💡 Frequent mild jank - check for inefficient rebuilds';
        }
        break;
    }

    // Enhanced logging with stack trace context for debugging
    final stackTrace = StackTrace.current;
    final stackLines = stackTrace.toString().split('\n');
    final relevantStack = stackLines
        .where(
          (line) =>
              line.contains('dasso_reader') &&
              !line.contains('frame_rate_monitor'),
        )
        .take(3)
        .join(' -> ');

    AnxLog.warning(
      '🐌 Jank detected: ${ms}ms ($fps FPS, ${severity.name})$context$hint',
    );

    if (relevantStack.isNotEmpty && !kReleaseMode) {
      AnxLog.info('📍 Jank location: $relevantStack');
    }

    // Track jank sources for pattern analysis
    _trackJankSource(ms, relevantStack);
  }

  /// Track jank sources to identify common culprits
  final Map<String, int> _jankSources = {};

  void _trackJankSource(int frameTimeMs, String stackTrace) {
    if (stackTrace.isEmpty) return;

    // Extract the most relevant part of the stack trace
    final parts = stackTrace.split(' -> ');
    if (parts.isNotEmpty) {
      final source = parts.first.trim();
      _jankSources[source] = (_jankSources[source] ?? 0) + 1;

      // Log frequent jank sources
      if (_jankSources[source]! > 5 && !kReleaseMode) {
        AnxLog.warning(
          '🔥 Frequent jank source detected: $source (${_jankSources[source]} times)',
        );
      }
    }
  }

  /// Get jank source analysis
  Map<String, int> getJankSourceAnalysis() {
    return Map.from(_jankSources);
  }

  /// Determine jank severity based on frame time
  JankSeverity _getJankSeverity(int frameMicroseconds) {
    final expectedMicroseconds = (1000000 / _config.targetFPS).round();
    final multiplier = frameMicroseconds / expectedMicroseconds;

    if (multiplier >= 4.0) return JankSeverity.severe;
    if (multiplier >= 2.0) return JankSeverity.moderate;
    return JankSeverity.mild;
  }

  /// Calculate performance score (0-100)
  double _calculatePerformanceScore() {
    if (_totalFrames == 0) return 100.0;

    final averageFPS = _calculateAverageFPS();
    final fpsScore = (averageFPS / _config.targetFPS * 100).clamp(0.0, 100.0);

    // Penalize for jank events
    final jankPenalty = (_jankCount / _totalFrames * 100 * 2).clamp(0.0, 50.0);

    return (fpsScore - jankPenalty).clamp(0.0, 100.0);
  }

  /// Log performance issues in debug mode
  void _logPerformanceIssues(Duration frameTime) {
    final expectedFrameTime =
        Duration(microseconds: (1000000 / _config.targetFPS).round());

    if (frameTime > expectedFrameTime * 1.5) {
      final fps = 1000000 / frameTime.inMicroseconds;
      AnxLog.info(
        '⚠️ Frame performance: ${frameTime.inMilliseconds}ms (${fps.toStringAsFixed(1)} FPS)',
      );
    }
  }

  /// Reset all metrics
  void _resetMetrics() {
    _frameTimes.clear();
    _frameTimestamps.clear();
    _jankEvents.clear();
    _currentFPS = 0.0;
    _jankCount = 0;
    _totalFrames = 0;
    _totalFrameTime = Duration.zero;
    _lastFrameTime = null;
  }

  /// Dispose of resources
  void dispose() {
    stopMonitoring();
    _resetMetrics();
    _isInitialized = false;

    if (kDebugMode) {
      AnxLog.info('🎯 FrameRateMonitor: Disposed');
    }
  }
}

/// Frame rate monitoring configuration
class FrameRateConfig {
  const FrameRateConfig({
    required this.targetFPS,
    required this.jankThresholdMicroseconds,
    required this.frameHistorySize,
    required this.maxJankEvents,
    required this.enableDebugLogging,
    required this.enableJankLogging,
  });

  final double targetFPS;
  final int jankThresholdMicroseconds;
  final int frameHistorySize;
  final int maxJankEvents;
  final bool enableDebugLogging;
  final bool enableJankLogging;

  factory FrameRateConfig.defaultConfig() {
    return const FrameRateConfig(
      targetFPS: 60.0,
      jankThresholdMicroseconds: 16670, // 16.67ms for 60 FPS
      frameHistorySize: 120, // 2 seconds at 60 FPS
      maxJankEvents: 100,
      enableDebugLogging: false, // Disabled for cleaner development experience
      enableJankLogging: false, // Disabled for cleaner development experience
    );
  }

  /// Development config with verbose logging for performance debugging
  factory FrameRateConfig.verboseConfig() {
    return const FrameRateConfig(
      targetFPS: 60.0,
      jankThresholdMicroseconds: 16670, // 16.67ms for 60 FPS
      frameHistorySize: 120, // 2 seconds at 60 FPS
      maxJankEvents: 100,
      enableDebugLogging: true, // Enable for performance debugging
      enableJankLogging: true, // Enable for performance debugging
    );
  }

  factory FrameRateConfig.highRefreshRate() {
    return const FrameRateConfig(
      targetFPS: 120.0,
      jankThresholdMicroseconds: 8330, // 8.33ms for 120 FPS
      frameHistorySize: 240, // 2 seconds at 120 FPS
      maxJankEvents: 100,
      enableDebugLogging: false, // Disabled for cleaner development experience
      enableJankLogging: false, // Disabled for cleaner development experience
    );
  }
}

/// Frame rate metrics data
class FrameRateMetrics {
  const FrameRateMetrics({
    required this.currentFPS,
    required this.averageFPS,
    required this.targetFPS,
    required this.jankCount,
    required this.totalFrames,
    required this.jankEvents,
    required this.performanceScore,
  });

  final double currentFPS;
  final double averageFPS;
  final double targetFPS;
  final int jankCount;
  final int totalFrames;
  final List<JankEvent> jankEvents;
  final double performanceScore;

  /// Get jank rate as percentage
  double get jankRate =>
      totalFrames > 0 ? (jankCount / totalFrames * 100) : 0.0;

  /// Check if performance is acceptable
  bool get isPerformanceAcceptable => performanceScore >= 70.0;
}

/// Jank event data
class JankEvent {
  const JankEvent({
    required this.timestamp,
    required this.frameTime,
    required this.severity,
    required this.expectedFrameTime,
  });

  final DateTime timestamp;
  final Duration frameTime;
  final JankSeverity severity;
  final Duration expectedFrameTime;

  /// Get frame time multiplier compared to expected
  double get frameTimeMultiplier =>
      frameTime.inMicroseconds / expectedFrameTime.inMicroseconds;
}

/// Jank severity levels
enum JankSeverity {
  mild,
  moderate,
  severe,
}

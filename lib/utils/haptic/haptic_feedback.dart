import 'package:flutter/services.dart';

/// A utility class for providing haptic feedback throughout the app
class AnxHapticFeedback {
  /// Private constructor to prevent instantiation
  AnxHapticFeedback._();

  /// No initialization needed for Flutter's built-in HapticFeedback
  static Future<void> init() async {
    // Nothing to initialize
  }

  /// Trigger a light impact feedback (for UI interactions)
  static void lightImpact() {
    HapticFeedback.lightImpact();
  }

  /// Trigger a medium impact feedback (for confirmations)
  static void mediumImpact() {
    HapticFeedback.mediumImpact();
  }

  /// Trigger a heavy impact feedback (for errors or important events)
  static void heavyImpact() {
    HapticFeedback.heavyImpact();
  }

  /// Trigger a selection click feedback (for selection changes)
  static void selectionClick() {
    HapticFeedback.selectionClick();
  }

  /// Trigger a vibration feedback
  static void vibrate() {
    HapticFeedback.vibrate();
  }
}

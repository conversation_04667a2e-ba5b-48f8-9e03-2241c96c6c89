import 'dart:ui';

import 'package:dasso_reader/utils/log/common.dart';
import 'package:flutter/material.dart';

class AnxError {
  static Future<void> init() async {
    AnxLog.info('AnxError init');
    FlutterError.onError = (details) {
      FlutterError.presentError(details);
      AnxLog.severe(details.exceptionAsString(), details.stack);
    };
    PlatformDispatcher.instance.onError = (error, stack) {
      AnxLog.severe(error.toString(), stack);
      return false;
    };
  }
}

// TTS-specific error types and handling

/// TTS context classification for error handling
enum TtsContext {
  /// Dictionary word/phrase pronunciation
  dictionaryPronunciation,

  /// Continuous chapter/book reading
  continuousReading,
}

/// Available TTS engine types
enum TtsEngineType {
  /// System TTS using flutter_tts package
  systemTts,

  /// Microsoft Edge TTS using WebSocket API
  edgeTts,

  /// Google Translate TTS using HTTP API
  googleTranslateTts,
}

/// Detailed error classification for TTS operations
enum TtsErrorType {
  // Network-related errors
  networkConnection,
  networkTimeout,
  networkUnauthorized,
  networkServiceUnavailable,

  // Platform-specific errors
  platformEngineUnavailable,
  platformVoiceUnavailable,
  platformPermissionDenied,
  platformConfigurationInvalid,
  platformLanguageUnsupported,

  // Audio-related errors
  audioPlayerInitialization,
  audioPlaybackFailed,
  audioFormatUnsupported,
  audioDeviceUnavailable,
  audioSessionConflict,

  // State management errors
  stateInvalidTransition,
  stateConcurrentOperation,
  stateResourceBusy,
  stateEngineNotInitialized,

  // Content-related errors
  contentEmpty,
  contentTooLong,
  contentInvalidFormat,
  contentUnsupportedLanguage,

  // Configuration errors
  configurationInvalid,
  configurationMissing,
  configurationConflict,

  // Resource conflicts (specific to dual-TTS architecture)
  resourceEngineInUse,
  resourceContextConflict,
  resourceUnavailable,

  // Unknown/Generic errors
  unknown,
}

/// Error severity levels for appropriate user feedback and logging
enum TtsErrorSeverity {
  /// Low severity - subtle notification, operation can continue with degraded functionality
  low,

  /// Medium severity - user notification with retry option, affects current operation
  medium,

  /// High severity - prominent user feedback, significant functionality impact
  high,

  /// Critical severity - system-level failure, requires immediate attention
  critical,
}

/// TTS error class that integrates with existing AnxError system
class TtsError {
  final TtsContext context;
  final TtsEngineType failedEngine;
  final TtsErrorType type;
  final TtsErrorSeverity severity;
  final String message;
  final String? technicalDetails;
  final Object? originalError;
  final StackTrace? stackTrace;
  final DateTime timestamp;
  final Map<String, dynamic>? additionalContext;

  TtsError({
    required this.context,
    required this.failedEngine,
    required this.type,
    required this.message,
    TtsErrorSeverity? severity,
    this.technicalDetails,
    this.originalError,
    this.stackTrace,
    DateTime? timestamp,
    this.additionalContext,
  })  : severity = severity ?? TtsErrorSeverity.medium,
        timestamp = timestamp ?? DateTime.now();

  /// Log this error using the existing AnxLog system
  void log() {
    AnxLog.ttsOperationFailure(
      _contextToString(context),
      _engineToString(failedEngine),
      'operation',
      type.name,
      message,
      originalError,
      stackTrace,
    );
  }

  /// Get user-friendly error message based on context and error type
  String getUserFriendlyMessage() {
    // This will be enhanced with L10n integration
    switch (type) {
      case TtsErrorType.networkConnection:
        return context == TtsContext.dictionaryPronunciation
            ? 'Unable to connect for pronunciation. Check your internet connection.'
            : 'Unable to connect to reading service. Check your internet connection.';

      case TtsErrorType.platformEngineUnavailable:
        return context == TtsContext.dictionaryPronunciation
            ? 'Pronunciation engine is not available on this device.'
            : 'Text-to-speech engine is not available on this device.';

      case TtsErrorType.resourceEngineInUse:
        return context == TtsContext.dictionaryPronunciation
            ? 'Pronunciation is temporarily unavailable. Please try again.'
            : 'Reading service is busy. Please try again.';

      default:
        return 'An error occurred with text-to-speech. Please try again.';
    }
  }

  /// Check if this error should trigger a retry attempt
  bool get shouldRetry {
    switch (type) {
      case TtsErrorType.networkTimeout:
      case TtsErrorType.networkConnection:
      case TtsErrorType.stateResourceBusy:
      case TtsErrorType.audioSessionConflict:
        return true;

      case TtsErrorType.platformPermissionDenied:
      case TtsErrorType.platformEngineUnavailable:
      case TtsErrorType.contentInvalidFormat:
      case TtsErrorType.configurationInvalid:
        return false;

      default:
        return true;
    }
  }

  /// Get suggested retry delay for this error type
  Duration? get retryDelay {
    if (!shouldRetry) return null;

    switch (type) {
      case TtsErrorType.networkTimeout:
        return const Duration(seconds: 3);
      case TtsErrorType.networkConnection:
        return const Duration(seconds: 5);
      case TtsErrorType.stateResourceBusy:
        return const Duration(milliseconds: 500);
      case TtsErrorType.audioSessionConflict:
        return const Duration(seconds: 1);
      default:
        return const Duration(seconds: 2);
    }
  }

  /// Check if this error should trigger a fallback to another engine
  bool get shouldFallback {
    // Don't fallback for configuration or permission errors
    if (type == TtsErrorType.platformPermissionDenied ||
        type == TtsErrorType.configurationInvalid ||
        type == TtsErrorType.configurationMissing) {
      return false;
    }

    // Don't fallback for resource conflicts (need coordination instead)
    if (type == TtsErrorType.resourceEngineInUse ||
        type == TtsErrorType.resourceContextConflict) {
      return false;
    }

    return true;
  }

  /// Get safe fallback engines for this error context
  List<TtsEngineType> get safeFallbackEngines {
    if (!shouldFallback) return [];

    switch (context) {
      case TtsContext.dictionaryPronunciation:
        // Dictionary NEVER falls back to Edge TTS to avoid conflicts
        return [TtsEngineType.googleTranslateTts];
      case TtsContext.continuousReading:
        // Continuous reading can fall back to System TTS if dictionary isn't using it
        return [TtsEngineType.systemTts];
    }
  }

  String _contextToString(TtsContext context) {
    switch (context) {
      case TtsContext.dictionaryPronunciation:
        return 'Dictionary';
      case TtsContext.continuousReading:
        return 'Reading';
    }
  }

  String _engineToString(TtsEngineType engine) {
    switch (engine) {
      case TtsEngineType.systemTts:
        return 'SystemTTS';
      case TtsEngineType.edgeTts:
        return 'EdgeTTS';
      case TtsEngineType.googleTranslateTts:
        return 'GoogleTTS';
    }
  }

  @override
  String toString() {
    return 'TtsError(context: ${context.name}, engine: ${failedEngine.name}, type: ${type.name}, message: $message)';
  }
}

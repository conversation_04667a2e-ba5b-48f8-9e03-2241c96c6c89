import 'package:flutter/material.dart';

/// Semantic helper utilities for consistent accessibility implementation
///
/// This class provides helper methods and mixins for adding semantic
/// information to widgets throughout the Dasso Reader app.
class SemanticHelpers {
  // Private constructor to prevent instantiation
  SemanticHelpers._();

  // =====================================================
  // SEMANTIC WRAPPER METHODS
  // =====================================================

  /// Wrap widget with button semantics
  static Widget button({
    required BuildContext context,
    required Widget child,
    required String label,
    VoidCallback? onTap,
    String? hint,
    bool enabled = true,
  }) {
    return Semantics(
      label: label,
      hint: hint,
      button: true,
      enabled: enabled,
      onTap: onTap,
      child: child,
    );
  }

  /// Wrap widget with header semantics
  static Widget header({
    required Widget child,
    required String label,
    int level = 1,
  }) {
    return Semantics(
      label: label,
      header: true,
      child: child,
    );
  }

  /// Wrap widget with image semantics
  static Widget image({
    required Widget child,
    required String label,
    bool isDecorative = false,
  }) {
    if (isDecorative) {
      return ExcludeSemantics(child: child);
    }

    return Semantics(
      label: label,
      image: true,
      child: child,
    );
  }

  /// Wrap widget with list semantics
  static Widget list({
    required Widget child,
    required String label,
    int? itemCount,
  }) {
    String fullLabel = label;
    if (itemCount != null) {
      fullLabel = '$label with $itemCount items';
    }

    return Semantics(
      label: fullLabel,
      child: child,
    );
  }

  /// Wrap widget with list item semantics
  static Widget listItem({
    required Widget child,
    required String label,
    int? index,
    int? total,
    VoidCallback? onTap,
  }) {
    String fullLabel = label;
    if (index != null && total != null) {
      fullLabel = '$label, item ${index + 1} of $total';
    }

    return Semantics(
      label: fullLabel,
      onTap: onTap,
      child: child,
    );
  }

  /// Wrap widget with text field semantics
  static Widget textField({
    required Widget child,
    required String label,
    String? value,
    String? hint,
    bool isRequired = false,
    bool hasError = false,
    String? errorMessage,
  }) {
    String fullLabel = label;

    if (isRequired) {
      fullLabel = '$fullLabel, required';
    }

    if (value != null && value.isNotEmpty) {
      fullLabel = '$fullLabel, current value: $value';
    }

    if (hint != null) {
      fullLabel = '$fullLabel. $hint';
    }

    if (hasError && errorMessage != null) {
      fullLabel = '$fullLabel. Error: $errorMessage';
    }

    return Semantics(
      label: fullLabel,
      textField: true,
      child: child,
    );
  }

  /// Wrap widget with link semantics
  static Widget link({
    required Widget child,
    required String label,
    VoidCallback? onTap,
    String? hint,
  }) {
    return Semantics(
      label: label,
      hint: hint,
      link: true,
      onTap: onTap,
      child: child,
    );
  }

  /// Wrap widget with slider semantics
  static Widget slider({
    required Widget child,
    required String label,
    required double value,
    required double min,
    required double max,
    String? unit,
  }) {
    String fullLabel = '$label: $value';
    if (unit != null) {
      fullLabel = '$fullLabel $unit';
    }
    fullLabel = '$fullLabel, range from $min to $max';

    return Semantics(
      label: fullLabel,
      slider: true,
      child: child,
    );
  }

  /// Wrap widget with checkbox semantics
  static Widget checkbox({
    required Widget child,
    required String label,
    required bool value,
    VoidCallback? onTap,
  }) {
    final state = value ? 'checked' : 'unchecked';
    final fullLabel = '$label, $state';

    return Semantics(
      label: fullLabel,
      checked: value,
      onTap: onTap,
      child: child,
    );
  }

  /// Wrap widget with tab semantics
  static Widget tab({
    required Widget child,
    required String label,
    required bool isSelected,
    int? index,
    int? total,
    VoidCallback? onTap,
  }) {
    String fullLabel = label;

    if (isSelected) {
      fullLabel = '$fullLabel, selected';
    }

    if (index != null && total != null) {
      fullLabel = '$fullLabel, tab ${index + 1} of $total';
    }

    return Semantics(
      label: fullLabel,
      selected: isSelected,
      onTap: onTap,
      child: child,
    );
  }

  // =====================================================
  // CONTENT-SPECIFIC SEMANTIC HELPERS
  // =====================================================

  /// Book-specific semantic wrapper
  static Widget book({
    required Widget child,
    required String title,
    required String author,
    double? progress,
    VoidCallback? onTap,
    VoidCallback? onLongPress,
  }) {
    String label = 'Book: $title by $author';

    if (progress != null) {
      label = '$label, ${(progress * 100).toStringAsFixed(0)}% complete';
    }

    String hint = 'Tap to open';
    if (onLongPress != null) {
      hint = '$hint, long press for options';
    }

    return Semantics(
      label: label,
      hint: hint,
      button: true,
      onTap: onTap,
      onLongPress: onLongPress,
      child: child,
    );
  }

  /// HSK-specific semantic wrapper
  static Widget hskLevel({
    required Widget child,
    required int level,
    bool isDecorative = false,
  }) {
    if (isDecorative) {
      return ExcludeSemantics(child: child);
    }

    return Semantics(
      label: 'HSK Level $level',
      child: child,
    );
  }

  /// Dictionary entry semantic wrapper
  static Widget dictionaryEntry({
    required Widget child,
    required String word,
    String? pinyin,
    String? definition,
    int? hskLevel,
  }) {
    String label = 'Chinese word: $word';

    if (pinyin != null) {
      label = '$label, pronunciation: $pinyin';
    }

    if (definition != null) {
      label = '$label, meaning: $definition';
    }

    if (hskLevel != null) {
      label = '$label, HSK Level $hskLevel';
    }

    return Semantics(
      label: label,
      header: true,
      child: child,
    );
  }

  /// Navigation item semantic wrapper
  static Widget navigationItem({
    required Widget child,
    required String destination,
    required bool isSelected,
    int? index,
    int? total,
    VoidCallback? onTap,
  }) {
    String label = destination;

    if (isSelected) {
      label = '$label, selected';
    }

    if (index != null && total != null) {
      label = '$label, tab ${index + 1} of $total';
    }

    return Semantics(
      label: label,
      selected: isSelected,
      onTap: onTap,
      child: child,
    );
  }

  // =====================================================
  // DECORATIVE ELEMENT HELPERS
  // =====================================================

  /// Exclude decorative elements from screen readers
  static Widget decorative(Widget child) {
    return ExcludeSemantics(child: child);
  }

  /// Mark dividers as decorative
  static Widget divider(Widget child) {
    return ExcludeSemantics(child: child);
  }

  /// Mark background images as decorative
  static Widget backgroundImage(Widget child) {
    return ExcludeSemantics(child: child);
  }

  /// Mark purely visual elements as decorative
  static Widget visual(Widget child) {
    return ExcludeSemantics(child: child);
  }

  // =====================================================
  // SEMANTIC GROUPING HELPERS
  // =====================================================

  /// Group related semantic elements
  static Widget group({
    required Widget child,
    required String label,
    String? hint,
  }) {
    return Semantics(
      label: label,
      hint: hint,
      child: child,
    );
  }

  /// Merge semantics for complex widgets
  static Widget merge({
    required Widget child,
    String? label,
    String? hint,
  }) {
    return MergeSemantics(
      child: Semantics(
        label: label,
        hint: hint,
        child: child,
      ),
    );
  }

  /// Create semantic container for related content
  static Widget container({
    required Widget child,
    required String label,
    String? contentType,
  }) {
    String fullLabel = label;

    if (contentType != null) {
      // Add content type context
      fullLabel = '$contentType $label';
    }

    return Semantics(
      label: fullLabel,
      container: true,
      child: child,
    );
  }
}

/// Mixin for adding accessibility features to custom widgets
mixin CustomWidgetAccessibility on Widget {
  /// Add semantic information to custom widget
  Widget withSemantics({
    required BuildContext context,
    required String label,
    String? hint,
    String? role,
    VoidCallback? onTap,
    bool excludeSemantics = false,
  }) {
    if (excludeSemantics) {
      return ExcludeSemantics(child: this);
    }

    return Semantics(
      label: label,
      hint: hint,
      button: role == 'button',
      link: role == 'link',
      header: role == 'header',
      image: role == 'image',
      textField: role == 'textField',
      onTap: onTap,
      child: this,
    );
  }

  /// Add tooltip for accessibility
  Widget withTooltip(String message) {
    return Tooltip(
      message: message,
      child: this,
    );
  }

  /// Exclude from semantics (for decorative elements)
  Widget asDecorative() {
    return ExcludeSemantics(child: this);
  }
}

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Motion preference levels
enum MotionPreference {
  full, // All animations and motion
  reduced, // Reduced motion and animations
  minimal, // Essential motion only
  none, // No motion or animations
}

/// Accessibility safety system for preventing seizures and motion sensitivity issues
///
/// This system provides safety features including:
/// - Seizure prevention through flash rate monitoring
/// - Motion reduction for vestibular disorders
/// - Animation safety validation
/// - User preference controls for motion sensitivity
class AccessibilitySafety {
  // Private constructor to prevent instantiation
  AccessibilitySafety._();

  // =====================================================
  // SAFETY CONSTANTS
  // =====================================================

  /// Maximum safe flash rate (3 Hz) to prevent seizures
  static const double maxSafeFlashRate = 3.0;

  /// Maximum safe animation duration for rapid changes
  static const Duration maxSafeAnimationDuration = Duration(milliseconds: 333);

  /// Minimum animation duration for motion sensitivity
  static const Duration minMotionSafeAnimationDuration =
      Duration(milliseconds: 500);

  // =====================================================
  // MOTION PREFERENCES
  // =====================================================

  /// Get user's motion preference (synchronous with fallback)
  static MotionPreference getMotionPreference() {
    // For now, return default value. In a real implementation,
    // this would use a cached preference value
    return MotionPreference.full;
  }

  /// Set user's motion preference
  static Future<void> setMotionPreference(MotionPreference preference) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('accessibility_motion_preference', preference.name);
  }

  /// Check if motion is reduced based on system and user preferences
  static bool isMotionReduced(BuildContext context) {
    // Check system preference first
    final systemReduceMotion = MediaQuery.of(context).disableAnimations;
    if (systemReduceMotion) return true;

    // Check user preference
    final userPreference = getMotionPreference();
    return userPreference == MotionPreference.reduced ||
        userPreference == MotionPreference.minimal ||
        userPreference == MotionPreference.none;
  }

  /// Check if animations should be disabled
  static bool shouldDisableAnimations(BuildContext context) {
    final systemReduceMotion = MediaQuery.of(context).disableAnimations;
    if (systemReduceMotion) return true;

    final userPreference = getMotionPreference();
    return userPreference == MotionPreference.none;
  }

  // =====================================================
  // SAFE ANIMATION HELPERS
  // =====================================================

  /// Get safe animation duration based on motion preferences
  static Duration getSafeAnimationDuration({
    required BuildContext context,
    required Duration defaultDuration,
    bool isEssential = false,
  }) {
    if (shouldDisableAnimations(context) && !isEssential) {
      return Duration.zero;
    }

    if (isMotionReduced(context)) {
      // Use longer duration for motion sensitivity
      final result = Duration(
        milliseconds: (defaultDuration.inMilliseconds * 1.5).round(),
      );

      // Manual clamp since Duration doesn't have clamp method
      const maxDuration = Duration(seconds: 2);
      if (result < minMotionSafeAnimationDuration) {
        return minMotionSafeAnimationDuration;
      } else if (result > maxDuration) {
        return maxDuration;
      }
      return result;
    }

    return defaultDuration;
  }

  /// Create safe animation controller
  static AnimationController createSafeAnimationController({
    required TickerProvider vsync,
    required BuildContext context,
    required Duration duration,
    bool isEssential = false,
  }) {
    final safeDuration = getSafeAnimationDuration(
      context: context,
      defaultDuration: duration,
      isEssential: isEssential,
    );

    return AnimationController(
      duration: safeDuration,
      vsync: vsync,
    );
  }

  /// Create safe fade transition
  static Widget createSafeFadeTransition({
    required BuildContext context,
    required Animation<double> animation,
    required Widget child,
    bool isEssential = false,
  }) {
    if (shouldDisableAnimations(context) && !isEssential) {
      return child;
    }

    return FadeTransition(
      opacity: animation,
      child: child,
    );
  }

  /// Create safe slide transition
  static Widget createSafeSlideTransition({
    required BuildContext context,
    required Animation<Offset> animation,
    required Widget child,
    bool isEssential = false,
  }) {
    if (shouldDisableAnimations(context) && !isEssential) {
      return child;
    }

    return SlideTransition(
      position: animation,
      child: child,
    );
  }

  /// Create safe scale transition
  static Widget createSafeScaleTransition({
    required BuildContext context,
    required Animation<double> animation,
    required Widget child,
    bool isEssential = false,
  }) {
    if (shouldDisableAnimations(context) && !isEssential) {
      return child;
    }

    return ScaleTransition(
      scale: animation,
      child: child,
    );
  }

  // =====================================================
  // SEIZURE PREVENTION
  // =====================================================

  /// Validate animation for seizure safety
  static bool isAnimationSafe({
    required Duration duration,
    required int frameCount,
    bool hasFlashing = false,
  }) {
    if (hasFlashing) {
      // Calculate flash rate
      final flashRate = frameCount / duration.inSeconds;
      return flashRate <= maxSafeFlashRate;
    }

    // For non-flashing animations, check duration
    return duration >= maxSafeAnimationDuration;
  }

  /// Create seizure-safe blinking animation
  static AnimationController createSafeBlinkingController({
    required TickerProvider vsync,
    required BuildContext context,
  }) {
    // Use safe duration to prevent rapid flashing
    final safeDuration = Duration(
      milliseconds: (1000 / maxSafeFlashRate).round(),
    );

    return AnimationController(
      duration: safeDuration,
      vsync: vsync,
    );
  }

  /// Validate color changes for seizure safety
  static bool areColorChangesSafe({
    required List<Color> colors,
    required Duration transitionDuration,
  }) {
    // Check for high contrast changes
    for (int i = 0; i < colors.length - 1; i++) {
      final color1 = colors[i];
      final color2 = colors[i + 1];

      // Calculate luminance difference
      final luminanceDiff =
          (color1.computeLuminance() - color2.computeLuminance()).abs();

      // If high contrast change, ensure safe timing
      if (luminanceDiff > 0.5) {
        final changeRate = 1000 / transitionDuration.inMilliseconds;
        if (changeRate > maxSafeFlashRate) {
          return false;
        }
      }
    }

    return true;
  }

  // =====================================================
  // MOTION SENSITIVITY HELPERS
  // =====================================================

  /// Create motion-sensitive scroll physics
  static ScrollPhysics createMotionSafeScrollPhysics(BuildContext context) {
    if (isMotionReduced(context)) {
      // Use clamping physics to reduce bouncing
      return const ClampingScrollPhysics();
    }

    return const BouncingScrollPhysics();
  }

  /// Create motion-sensitive page transition
  static PageTransitionsBuilder createMotionSafePageTransition(
    BuildContext context,
  ) {
    if (shouldDisableAnimations(context)) {
      return const NoAnimationPageTransitionsBuilder();
    }

    if (isMotionReduced(context)) {
      return const FadeUpwardsPageTransitionsBuilder();
    }

    return const CupertinoPageTransitionsBuilder();
  }

  // =====================================================
  // ACCESSIBILITY SETTINGS
  // =====================================================

  /// Get accessibility safety settings
  static AccessibilitySafetySettings getSettings() {
    return AccessibilitySafetySettings(
      motionPreference: getMotionPreference(),
      reduceFlashing: true,
      extendTimeouts: false,
      enhanceContrast: false,
    );
  }

  /// Update accessibility safety settings
  static Future<void> updateSettings(
    AccessibilitySafetySettings settings,
  ) async {
    await setMotionPreference(settings.motionPreference);
    // Additional settings would be saved to SharedPreferences here
  }
}

/// No animation page transitions builder for accessibility
class NoAnimationPageTransitionsBuilder extends PageTransitionsBuilder {
  const NoAnimationPageTransitionsBuilder();

  @override
  Widget buildTransitions<T extends Object?>(
    PageRoute<T> route,
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    return child;
  }
}

/// Accessibility safety settings
class AccessibilitySafetySettings {
  const AccessibilitySafetySettings({
    required this.motionPreference,
    required this.reduceFlashing,
    required this.extendTimeouts,
    required this.enhanceContrast,
  });

  final MotionPreference motionPreference;
  final bool reduceFlashing;
  final bool extendTimeouts;
  final bool enhanceContrast;

  AccessibilitySafetySettings copyWith({
    MotionPreference? motionPreference,
    bool? reduceFlashing,
    bool? extendTimeouts,
    bool? enhanceContrast,
  }) {
    return AccessibilitySafetySettings(
      motionPreference: motionPreference ?? this.motionPreference,
      reduceFlashing: reduceFlashing ?? this.reduceFlashing,
      extendTimeouts: extendTimeouts ?? this.extendTimeouts,
      enhanceContrast: enhanceContrast ?? this.enhanceContrast,
    );
  }
}

/// Mixin for widgets that need motion safety
mixin MotionSafetyMixin<T extends StatefulWidget>
    on State<T>, TickerProviderStateMixin<T> {
  /// Create motion-safe animation controller
  AnimationController createSafeController({
    required Duration duration,
    bool isEssential = false,
  }) {
    return AccessibilitySafety.createSafeAnimationController(
      vsync: this,
      context: context,
      duration: duration,
      isEssential: isEssential,
    );
  }

  /// Check if motion is reduced
  bool get isMotionReduced => AccessibilitySafety.isMotionReduced(context);

  /// Check if animations should be disabled
  bool get shouldDisableAnimations =>
      AccessibilitySafety.shouldDisableAnimations(context);
}

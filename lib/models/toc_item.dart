class TocItem {
  final String id;
  final String href;
  final String label;
  final List<TocItem> subitems;

  TocItem({
    required this.id,
    required this.href,
    required this.label,
    required this.subitems,
  });

  factory TocItem.fromJson(Map<String, dynamic> json) {
    return TocItem(
      id: (json['id'] ?? '').toString(),
      href: json['href'] as String? ?? '',
      label: json['label'] as String? ?? '',
      subitems: json['subitems'] == null
          ? []
          : (json['subitems'] as List<dynamic>)
              .map((i) => TocItem.fromJson(i as Map<String, dynamic>))
              .toList(),
    );
  }
}

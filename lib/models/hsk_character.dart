import 'package:freezed_annotation/freezed_annotation.dart';

part 'hsk_character.freezed.dart';
part 'hsk_character.g.dart';

@freezed
abstract class HskCharacter with _$HskCharacter {
  const factory HskCharacter({
    required String hskLevel,
    required int characterId,
    required String character,
    required String pinyin,
    required String englishTranslation,
    required String audioAssetPath,
    @Default(false) bool isMastered,
    @Default(0) int viewCount,
    @Default(0) int learnCycleState,
    @Default(0) int practiceCorrectStreak,
    DateTime? lastPracticedCorrectly,
  }) = _HskCharacter;

  factory HskCharacter.fromJson(Map<String, dynamic> json) =>
      _$HskCharacterFromJson(json);
}

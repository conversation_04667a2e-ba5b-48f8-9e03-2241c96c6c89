import 'package:freezed_annotation/freezed_annotation.dart';

part 'hsk_settings.freezed.dart';
part 'hsk_settings.g.dart';

@freezed
abstract class HskLearnSettings with _$HskLearnSettings {
  const factory HskLearnSettings({
    @Default(true) bool autoPlaySound,
    @Default(true) bool showEnglishTranslation,
    @Default(true) bool showPinyinPrompt,
    @Default(true) bool showPinyinOnButtons,
    @Default(true) bool showTextPrompt,
    @Default(false) bool delayTextPrompt,
  }) = _HskLearnSettings;

  factory HskLearnSettings.fromJson(Map<String, dynamic> json) =>
      _$HskLearnSettingsFromJson(json);
}

@freezed
abstract class HskPracticeSettings with _$HskPracticeSettings {
  const factory HskPracticeSettings({
    @Default(true) bool autoPlaySound,
    @Default(true) bool showEnglish,
    @Default(true) bool showPinyin,
    @Default(false) bool delayTextPrompt,
    @Default(37.5) double timerDurationSeconds,
  }) = _HskPracticeSettings;

  factory HskPracticeSettings.fromJson(Map<String, dynamic> json) =>
      _$HskPracticeSettingsFromJson(json);
}

@freezed
abstract class HskReviewSettings with _$HskReviewSettings {
  const factory HskReviewSettings({
    @Default(false) bool autoPlaySound,
    @Default(true) bool showPinyin,
    @Default(true) bool showEnglish,
  }) = _HskReviewSettings;

  factory HskReviewSettings.fromJson(Map<String, dynamic> json) =>
      _$HskReviewSettingsFromJson(json);
}

import 'dart:convert';

class BookStyle {
  double fontSize;
  String fontFamily;
  double fontWeight;
  double lineHeight;
  double letterSpacing;
  double wordSpacing;
  double paragraphSpacing;
  double sideMargin;
  double topMargin;
  double bottomMargin;
  double indent;
  int maxColumnCount;

  BookStyle({
    this.fontSize = 1.4,
    this.fontFamily = 'Arial',
    this.fontWeight = 400,
    this.lineHeight = 1.8,
    this.letterSpacing = 0.0,
    this.wordSpacing = 0.0,
    this.paragraphSpacing = 1.0,
    this.sideMargin = 6.0,
    this.topMargin = 90.0,
    this.bottomMargin = 50.0,
    this.indent = 0,
    this.maxColumnCount = 0,
  });

  BookStyle copyWith({
    double? fontSize,
    String? fontFamily,
    double? fontWeight,
    double? lineHeight,
    double? letterSpacing,
    double? wordSpacing,
    double? paragraphSpacing,
    double? sideMargin,
    double? topMargin,
    double? bottomMargin,
    double? indent,
    int? maxColumnCount,
  }) {
    return BookStyle(
      fontSize: fontSize ?? this.fontSize,
      fontFamily: fontFamily ?? this.fontFamily,
      fontWeight: fontWeight ?? this.fontWeight,
      lineHeight: lineHeight ?? this.lineHeight,
      letterSpacing: letterSpacing ?? this.letterSpacing,
      wordSpacing: wordSpacing ?? this.wordSpacing,
      paragraphSpacing: paragraphSpacing ?? this.paragraphSpacing,
      sideMargin: sideMargin ?? this.sideMargin,
      topMargin: topMargin ?? this.topMargin,
      bottomMargin: bottomMargin ?? this.bottomMargin,
      indent: indent ?? this.indent,
      maxColumnCount: maxColumnCount ?? this.maxColumnCount,
    );
  }

  Map<String, Object> toMap() {
    return {
      'fontSize': fontSize,
      'fontFamily': fontFamily,
      'fontWeight': fontWeight,
      'lineHeight': lineHeight,
      'letterSpacing': letterSpacing,
      'wordSpacing': wordSpacing,
      'paragraphSpacing': paragraphSpacing,
      'sideMargin': sideMargin,
      'topMargin': topMargin,
      'bottomMargin': bottomMargin,
      'indent': indent,
      'maxColumnCount': maxColumnCount,
    };
  }

  String toJson() {
    return '''
    {
      "fontSize": $fontSize,
      "fontFamily": "$fontFamily",
      "fontWeight": $fontWeight,
      "lineHeight": $lineHeight,
      "letterSpacing": $letterSpacing,
      "wordSpacing": $wordSpacing,
      "paragraphSpacing": $paragraphSpacing,
      "sideMargin": $sideMargin,
      "topMargin": $topMargin,
      "bottomMargin": $bottomMargin,
      "indent": $indent,
      "maxColumnCount": $maxColumnCount
    }
    ''';
  }

  factory BookStyle.fromJson(String json) {
    final Map<String, dynamic> data = jsonDecode(json) as Map<String, dynamic>;

    // Safe type conversion helper functions
    double parseDoubleValue(dynamic value, double defaultValue) {
      if (value == null) return defaultValue;
      if (value is double) return value;
      if (value is int) return value.toDouble();
      if (value is String) return double.tryParse(value) ?? defaultValue;
      return defaultValue;
    }

    int parseIntValue(dynamic value, int defaultValue) {
      if (value == null) return defaultValue;
      if (value is int) return value;
      if (value is double) return value.toInt();
      if (value is String) return int.tryParse(value) ?? defaultValue;
      return defaultValue;
    }

    String parseStringValue(dynamic value, String defaultValue) {
      if (value == null) return defaultValue;
      if (value is String) return value;
      return value.toString();
    }

    double fontsSize = parseDoubleValue(data['fontSize'], 1.4);
    double paragraphSpacing = parseDoubleValue(data['paragraphSpacing'], 1.5);
    final double fontWeight = parseDoubleValue(data['fontWeight'], 400.0);

    // Validate and clamp values
    if (fontsSize > 3 || fontsSize < 0.5) {
      fontsSize = 1.4;
    }
    if (paragraphSpacing > 3 || paragraphSpacing < 0) {
      paragraphSpacing = 1.5;
    }

    return BookStyle(
      fontSize: fontsSize,
      fontFamily: parseStringValue(data['fontFamily'], ''),
      fontWeight: fontWeight,
      lineHeight: parseDoubleValue(data['lineHeight'], 1.5),
      letterSpacing: parseDoubleValue(data['letterSpacing'], 0.0),
      wordSpacing: parseDoubleValue(data['wordSpacing'], 0.0),
      paragraphSpacing: paragraphSpacing,
      sideMargin: parseDoubleValue(data['sideMargin'], 0.0),
      topMargin: parseDoubleValue(data['topMargin'], 0.0),
      bottomMargin: parseDoubleValue(data['bottomMargin'], 0.0),
      indent: parseDoubleValue(data['indent'], 0.0),
      maxColumnCount: parseIntValue(data['maxColumnCount'], 0),
    );
  }
}

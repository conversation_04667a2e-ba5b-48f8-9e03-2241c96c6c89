import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:dasso_reader/models/hsk_character.dart';

part 'hsk_character_set.freezed.dart';
part 'hsk_character_set.g.dart';

@freezed
abstract class HskCharacterSet with _$HskCharacterSet {
  const factory HskCharacterSet({
    required String id,
    required String hskLevel,
    required int startId,
    required int endId,
    required List<HskCharacter> characters,
    @Default(0) int totalViewTime, // in seconds
  }) = _HskCharacterSet;

  factory HskCharacterSet.fromJson(Map<String, dynamic> json) =>
      _$HskCharacterSetFromJson(json);
}

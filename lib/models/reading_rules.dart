import 'dart:convert';

import 'package:dasso_reader/enums/convert_chinese_mode.dart';

class ReadingRules {
  late ConvertChineseMode convertChineseMode;
  late bool bionicReading;

  ReadingRules({required this.convertChineseMode, required this.bionicReading});

  ReadingRules.fromJson(String json) {
    final Map<String, dynamic> data = jsonDecode(json) as Map<String, dynamic>;
    convertChineseMode =
        getConvertChineseMode(data['convertChineseMode'] as String? ?? 'none');
    bionicReading = data['bionicReading'] as bool? ?? false;
  }

  String toJson() {
    return '''
    {
      "convertChineseMode": "${convertChineseMode.name}",
      "bionicReading": $bionicReading
    }
    ''';
  }

  ReadingRules copyWith({
    ConvertChineseMode? convertChineseMode,
    bool? bionicReading,
  }) {
    return ReadingRules(
      convertChineseMode: convertChineseMode ?? this.convertChineseMode,
      bionicReading: bionicReading ?? this.bionicReading,
    );
  }
}

import 'package:flutter/material.dart';

/// Model class representing a dictionary entry for a Chinese word or character
class DictionaryEntry {
  /// Traditional Chinese form of the word
  final String traditional;

  /// Simplified Chinese form of the word
  final String simplified;

  /// Pinyin pronunciation with tone numbers (e.g., "xue2xi2")
  final String pinyin;

  /// List of English definitions
  final List<String> definitions;

  /// HSK level (1-6) if available, null otherwise
  final int? hskLevel;

  /// Word frequency rank (lower is more common)
  final int? frequency;

  const DictionaryEntry({
    required this.traditional,
    required this.simplified,
    required this.pinyin,
    required this.definitions,
    this.hskLevel,
    this.frequency,
  });

  /// Factory constructor to create a DictionaryEntry from a database map
  factory DictionaryEntry.fromMap(Map<String, dynamic> map) {
    return DictionaryEntry(
      traditional: map['traditional'] as String,
      simplified: map['simplified'] as String,
      pinyin: map['pinyin'] as String,
      definitions: (map['definitions'] as String)
          .split('/')
          .where((def) => def.isNotEmpty)
          .toList(),
      hskLevel: map['hsk_level'] as int?,
      frequency: map['frequency'] as int?,
    );
  }

  /// Convert pinyin with tone numbers to pinyin with tone marks
  String formattedPinyin() {
    // Early return for empty pinyin
    if (pinyin.isEmpty) return '';

    // Map for tone marks on vowels
    final Map<String, String> toneMarks = {
      'a1': 'ā',
      'a2': 'á',
      'a3': 'ǎ',
      'a4': 'à',
      'e1': 'ē',
      'e2': 'é',
      'e3': 'ě',
      'e4': 'è',
      'i1': 'ī',
      'i2': 'í',
      'i3': 'ǐ',
      'i4': 'ì',
      'o1': 'ō',
      'o2': 'ó',
      'o3': 'ǒ',
      'o4': 'ò',
      'u1': 'ū',
      'u2': 'ú',
      'u3': 'ǔ',
      'u4': 'ù',
      'v1': 'ǖ',
      'v2': 'ǘ',
      'v3': 'ǚ',
      'v4': 'ǜ',
      'v': 'ü',
    };

    // Normalize the input: replace u: with v, ensure spaces between syllables
    String normalizedPinyin = pinyin
        .replaceAll('u:', 'v')
        .replaceAll('U:', 'v')
        .toLowerCase()
        // Add space before digits if missing (e.g., "zhong1guo2" -> "zhong1 guo2")
        .replaceAllMapped(RegExp(r'([a-z])(\d)([a-z])'), (match) {
      return '${match.group(1)}${match.group(2)} ${match.group(3)}';
    });

    // Split into syllables, handling both space-separated and non-space-separated formats
    List<String> syllables = [];

    // First try to split by spaces
    List<String> spaceSplit = normalizedPinyin.split(' ');

    // Process each potentially multi-syllable chunk
    for (String chunk in spaceSplit) {
      if (chunk.isEmpty) continue;

      // Check if this chunk might contain multiple syllables without spaces
      if (RegExp(r'\d.*\d').hasMatch(chunk)) {
        // Split by digit followed by letter (e.g., "zhong1guo2" -> ["zhong1", "guo2"])
        List<String> subSyllables = [];
        RegExp(r'([a-z]+\d)').allMatches(chunk).forEach((match) {
          subSyllables.add(match.group(1)!);
        });
        syllables.addAll(subSyllables);
      } else {
        syllables.add(chunk);
      }
    }

    String result = '';

    // Process each syllable
    for (final syllable in syllables) {
      if (syllable.isEmpty) continue;

      String processedSyllable = syllable;
      String? tone;

      // Extract tone number
      final match = RegExp(r'([a-z]+)([1-5]?)').firstMatch(syllable);
      if (match != null) {
        processedSyllable = match.group(1)!;
        tone = match.group(2);

        // Neutral tone (5 or empty) doesn't get a tone mark
        if (tone == '5' || tone == '') {
          tone = null;
        }
      }

      if (tone != null) {
        // Apply tone mark according to pinyin rules
        // Rule: For syllables with a, e, o - they get the tone mark
        // For syllables with combinations, priority is a > o > e > i > u > ü
        String vowelToMark = '';

        // Check for vowel combinations with priority rules
        if (processedSyllable.contains('a')) {
          vowelToMark = 'a';
        } else if (processedSyllable.contains('o')) {
          vowelToMark = 'o';
        } else if (processedSyllable.contains('e')) {
          vowelToMark = 'e';
        } else if (processedSyllable.contains('i')) {
          vowelToMark = 'i';
        } else if (processedSyllable.contains('u')) {
          vowelToMark = 'u';
        } else if (processedSyllable.contains('v')) {
          vowelToMark = 'v';
        }

        if (vowelToMark.isNotEmpty) {
          final replacement = toneMarks['$vowelToMark$tone'] ?? vowelToMark;
          processedSyllable =
              processedSyllable.replaceFirst(vowelToMark, replacement);
        }
      }

      // Convert 'v' back to 'ü'
      processedSyllable = processedSyllable.replaceAll('v', 'ü');

      // Preserve original capitalization
      bool shouldCapitalize = false;
      if (syllables.indexOf(syllable) == 0 &&
          pinyin.isNotEmpty &&
          pinyin[0].toUpperCase() == pinyin[0]) {
        shouldCapitalize = true;
      }

      if (shouldCapitalize && processedSyllable.isNotEmpty) {
        processedSyllable =
            processedSyllable[0].toUpperCase() + processedSyllable.substring(1);
      }

      result += '$processedSyllable ';
    }

    return result.trim();
  }

  /// Get the first character of the simplified form
  String get firstCharacter => simplified.characters.first;

  /// Check if this entry is a single character
  bool get isSingleCharacter => simplified.characters.length == 1;

  /// Map representation for database storage
  Map<String, dynamic> toMap() {
    return {
      'traditional': traditional,
      'simplified': simplified,
      'pinyin': pinyin,
      'definitions': definitions.join('/'),
      'hsk_level': hskLevel,
      'frequency': frequency,
    };
  }
}

/// Model class for detailed information about a single Chinese character
class CharacterInfo {
  /// The character
  final String character;

  /// Radical component
  final String radical;

  /// Component breakdown
  final List<String> components;

  const CharacterInfo({
    required this.character,
    required this.radical,
    required this.components,
  });

  /// Factory constructor to create CharacterInfo from a database map
  factory CharacterInfo.fromMap(Map<String, dynamic> map) {
    return CharacterInfo(
      character: map['character'] as String,
      radical: map['radical'] as String,
      components: (map['components'] as String).split(''),
    );
  }

  /// Map representation for database storage
  Map<String, dynamic> toMap() {
    return {
      'character': character,
      'radical': radical,
      'components': components.join(''),
    };
  }
}

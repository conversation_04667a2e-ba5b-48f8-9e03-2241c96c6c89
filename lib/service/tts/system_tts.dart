import 'dart:async';
import 'dart:io' show Platform;

import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/page/reading_page.dart';
import 'package:dasso_reader/service/tts/base_tts.dart';
import 'package:dasso_reader/service/tts/tts_factory.dart';
import 'package:dasso_reader/utils/error/common.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:flutter/material.dart';
import 'package:flutter_tts/flutter_tts.dart';

class SystemTts extends BaseTts {
  static final SystemTts _instance = SystemTts._internal();

  factory SystemTts() {
    return _instance;
  }

  SystemTts._internal();

  final FlutterTts flutterTts = FlutterTts();

  String? _currentVoiceText;
  static String? _prevVoiceText;

  bool restarting = false;

  late Function getHereFunction;
  late Function getNextTextFunction;
  late Function getPrevTextFunction;

  @override
  final ValueNotifier<TtsStateEnum> ttsStateNotifier =
      ValueNotifier<TtsStateEnum>(TtsStateEnum.stopped);

  @override
  void updateTtsState(TtsStateEnum newState) {
    ttsStateNotifier.value = newState;
  }

  @override
  TtsContext get context => TtsContext.continuousReading;

  @override
  TtsEngineType get engineType => TtsEngineType.systemTts;

  bool get isIOS => Platform.isIOS;
  bool get isAndroid => Platform.isAndroid;
  bool get isWindows => Platform.isWindows;
  bool get isWeb => false; // Always false as we don't support web

  @override
  double get volume => Prefs().ttsVolume;

  @override
  set volume(double volume) {
    Prefs().ttsVolume = volume;
    restart();
  }

  @override
  double get pitch => Prefs().ttsPitch;

  @override
  set pitch(double pitch) {
    Prefs().ttsPitch = pitch;
    restart();
  }

  @override
  double get rate => Prefs().ttsRate;

  @override
  set rate(double rate) {
    Prefs().ttsRate = rate;
    restart();
  }

  @override
  bool get isPlaying => ttsStateNotifier.value == TtsStateEnum.playing;

  @override
  String? get currentVoiceText => _currentVoiceText;

  @override
  Future<void> init(
    Function getCurrentText,
    Function getNextText,
    Function getPrevText,
  ) async {
    getHereFunction = getCurrentText;
    getNextTextFunction = getNextText;
    getPrevTextFunction = getPrevText;

    await setAwaitOptions();

    if (isAndroid) {
      await getDefaultEngine();
      await getDefaultVoice();
    }

    flutterTts.setStartHandler(() async {
      updateTtsState(TtsStateEnum.playing);
      if (!isAndroid) {
        return;
      }
      _prevVoiceText = _currentVoiceText;
      _currentVoiceText = await epubPlayerKey.currentState!.ttsPrepare();

      if (_currentVoiceText?.isNotEmpty ?? false) {
        flutterTts.speak(_currentVoiceText!);
      }
    });

    flutterTts.setCompletionHandler(() async {
      if (!isAndroid) {
        return;
      }
      updateTtsState(TtsStateEnum.playing);
      if (_currentVoiceText?.isEmpty ?? true) {
        _currentVoiceText = await getNextText() as String?;
        await speak();
      } else {
        await getNextText();
      }
    });
  }

  Future<void> setAwaitOptions() async {
    await flutterTts.awaitSpeakCompletion(true);
    if (isAndroid) {
      await flutterTts.awaitSynthCompletion(true);
      await flutterTts.setQueueMode(1);
    }
  }

  Future<void> getDefaultEngine() async {
    var engine = await flutterTts.getDefaultEngine;
    if (engine != null) {}
  }

  Future<void> getDefaultVoice() async {
    var voice = await flutterTts.getDefaultVoice;
    if (voice != null) {}
  }

  @override
  Future<void> speak({String? content}) async {
    // Request access through TtsFactory conflict manager
    final factory = TtsFactory();
    final accessGranted = await factory.requestAccess(context, engineType);

    if (!accessGranted) {
      AnxLog.warning('SystemTts speak access denied due to conflict');

      final error = TtsError(
        context: context,
        failedEngine: engineType,
        type: TtsErrorType.resourceEngineInUse,
        message: 'Cannot start continuous reading - TTS resource is busy',
        severity: TtsErrorSeverity.medium,
      );

      handleError(error);
      return;
    }

    try {
      await setAwaitOptions();
      if (content != null) {
        _currentVoiceText = content;
      }
      _currentVoiceText ??= await getHereFunction() as String?;
      await flutterTts.setVolume(volume);
      await flutterTts.setSpeechRate(rate);
      await flutterTts.setPitch(pitch);

      await flutterTts.speak(_currentVoiceText!);

      if (!isAndroid && ttsStateNotifier.value == TtsStateEnum.playing) {
        _currentVoiceText = await getNextTextFunction() as String?;
        speak();
      }
    } catch (e) {
      // Release access on error
      await factory.releaseAccess(context, engineType);

      final error = TtsError(
        context: context,
        failedEngine: engineType,
        type: TtsErrorType.platformEngineUnavailable,
        message: 'System TTS failed to speak',
        severity: TtsErrorSeverity.high,
        originalError: e,
        technicalDetails: 'Text: $_currentVoiceText',
      );

      handleError(error);
      rethrow;
    }
  }

  @override
  Future<dynamic> stop() async {
    updateTtsState(TtsStateEnum.stopped);
    final result = await flutterTts.stop();
    _currentVoiceText = null;

    // Release access through TtsFactory conflict manager
    final factory = TtsFactory();
    await factory.releaseAccess(context, engineType);

    return result;
  }

  @override
  Future<void> pause() async {
    final result = await flutterTts.stop();
    if (result == 1) {
      updateTtsState(TtsStateEnum.paused);
    }
  }

  @override
  Future<void> resume() async {
    if (isAndroid) {
      speak(content: _prevVoiceText);
      return;
    }
    speak(content: _currentVoiceText);
  }

  @override
  Future<void> prev() async {
    if (restarting) {
      return;
    }
    restarting = true;
    await stop();
    _currentVoiceText = await getPrevTextFunction() as String?;
    speak();
    restarting = false;
  }

  @override
  Future<void> next() async {
    if (restarting) {
      return;
    }
    restarting = true;
    await stop();
    _currentVoiceText = await getNextTextFunction() as String?;
    speak();
    restarting = false;
  }

  @override
  Future<void> restart() async {
    if (restarting) {
      return;
    }
    restarting = true;
    await stop();
    speak();
    restarting = false;
  }

  @override
  Future<void> dispose() async {
    await flutterTts.stop();

    // Release access through TtsFactory conflict manager
    final factory = TtsFactory();
    await factory.releaseAccess(context, engineType);
  }
}

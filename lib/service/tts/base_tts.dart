import 'dart:async';
import 'package:flutter/material.dart';
import 'package:dasso_reader/utils/error/common.dart';

enum TtsStateEnum { playing, stopped, paused, continued }

abstract class BaseTts {
  double get volume;
  set volume(double volume);

  double get pitch;
  set pitch(double pitch);

  double get rate;
  set rate(double rate);

  ValueNotifier<TtsStateEnum> get ttsStateNotifier;
  void updateTtsState(TtsStateEnum newState);

  /// Get the TTS context for this instance (Dictionary or Continuous Reading)
  TtsContext get context;

  /// Get the engine type for this instance
  TtsEngineType get engineType;

  Future<void> init(
    Function getCurrentText,
    Function getNextText,
    Function getPrevText,
  );

  Future<void> speak({String? content});

  Future<dynamic> stop();

  Future<void> pause();

  Future<void> resume();

  Future<void> prev();

  Future<void> next();

  Future<void> restart();

  Future<void> dispose();

  bool get isPlaying;

  String? get currentVoiceText;

  /// Handle TTS errors in a context-aware manner
  void handleError(TtsError error) {
    error.log();
    // Subclasses can override for specific error handling
  }
}

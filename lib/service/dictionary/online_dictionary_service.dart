import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:just_audio/just_audio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:dasso_reader/models/dictionary/dictionary_entry.dart';
import 'package:dasso_reader/service/tts/tts_factory.dart';
import 'package:dasso_reader/utils/error/common.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:async';
import 'dart:io' show Platform;

/// Service for handling online dictionary lookups and pronunciation
class OnlineDictionaryService {
  static final OnlineDictionaryService _instance =
      OnlineDictionaryService._internal();
  factory OnlineDictionaryService() => _instance;

  OnlineDictionaryService._internal();

  final AudioPlayer _audioPlayer = AudioPlayer();

  // Flutter TTS instance for dictionary pronunciation (separate from reading interface TTS)
  final FlutterTts _dictionaryTts = FlutterTts();
  bool _ttsInitialized = false;

  // HTTP client
  final http.Client _httpClient = http.Client();

  // Cache for dictionary lookups to reduce API calls
  final Map<String, DictionaryEntry> _dictionaryCache = {};
  final Map<String, CharacterInfo> _characterInfoCache = {};

  // Persistent cache keys
  static const String _dictionaryCacheKey = 'dictionary_cache';
  static const String _characterInfoCacheKey = 'character_info_cache';

  // Flag to track if we're in offline mode
  bool _isOfflineMode = false;

  /// Initialize the service and load cached data
  Future<void> initialize() async {
    await _loadCachedData();
    await _initializeDictionaryTts();
  }

  /// Initialize Flutter TTS for dictionary pronunciation
  Future<void> _initializeDictionaryTts() async {
    if (_ttsInitialized) return;

    try {
      // Configure TTS for Chinese Simplified
      await _dictionaryTts.setLanguage('zh-CN');

      // Set default speech parameters for dictionary pronunciation
      await _dictionaryTts.setVolume(1.0);
      await _dictionaryTts
          .setSpeechRate(0.5); // Slower rate for pronunciation clarity
      await _dictionaryTts.setPitch(1.0);

      // Platform-specific configurations
      if (Platform.isAndroid) {
        // Android-specific TTS configuration
        try {
          await _dictionaryTts.awaitSpeakCompletion(true);
          await _dictionaryTts.awaitSynthCompletion(true);
          AnxLog.info('Android TTS configuration applied');
        } catch (e) {
          AnxLog.info('Android TTS configuration failed: $e');
        }
      } else if (Platform.isIOS) {
        // iOS-specific configuration
        try {
          await _dictionaryTts.setSharedInstance(true);
          AnxLog.info('iOS TTS configuration applied');
        } catch (e) {
          AnxLog.info('iOS TTS configuration failed: $e');
        }
      }

      _ttsInitialized = true;
      AnxLog.info('Dictionary TTS initialized successfully');
    } catch (e) {
      AnxLog.severe('Error initializing dictionary TTS: $e');
      // TTS initialization failure is not critical, continue without it
    }
  }

  /// Load cached dictionary and character info from SharedPreferences
  Future<void> _loadCachedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Load dictionary cache
      final dictionaryJson = prefs.getString(_dictionaryCacheKey);
      if (dictionaryJson != null) {
        final data = json.decode(dictionaryJson) as Map<String, dynamic>;
        data.forEach((key, value) {
          if (value is Map<String, dynamic>) {
            _dictionaryCache[key] = DictionaryEntry.fromMap(value);
          }
        });
        AnxLog.info(
          'Loaded ${_dictionaryCache.length} dictionary entries from cache',
        );
      }

      // Load character info cache
      final characterInfoJson = prefs.getString(_characterInfoCacheKey);
      if (characterInfoJson != null) {
        final data = json.decode(characterInfoJson) as Map<String, dynamic>;
        data.forEach((key, value) {
          if (value is Map<String, dynamic>) {
            _characterInfoCache[key] = CharacterInfo.fromMap(value);
          }
        });
        AnxLog.info(
          'Loaded ${_characterInfoCache.length} character info entries from cache',
        );
      }
    } catch (e) {
      AnxLog.severe('Error loading cached data: $e');
    }
  }

  /// Save cached data to SharedPreferences
  Future<void> _saveCachedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Save dictionary cache (limit to 1000 entries to avoid storage issues)
      final Map<String, dynamic> dictionaryData = {};
      final entries = _dictionaryCache.entries.take(1000).toList();
      for (final entry in entries) {
        dictionaryData[entry.key] = entry.value.toMap();
      }
      await prefs.setString(_dictionaryCacheKey, json.encode(dictionaryData));

      // Save character info cache
      final Map<String, dynamic> characterInfoData = {};
      for (final entry in _characterInfoCache.entries) {
        characterInfoData[entry.key] = entry.value.toMap();
      }
      await prefs.setString(
        _characterInfoCacheKey,
        json.encode(characterInfoData),
      );

      AnxLog.info(
        'Saved ${dictionaryData.length} dictionary entries and ${characterInfoData.length} character info entries to cache',
      );
    } catch (e) {
      AnxLog.severe('Error saving cached data: $e');
    }
  }

  /// Set offline mode
  void setOfflineMode(bool isOffline) {
    _isOfflineMode = isOffline;
    AnxLog.info('Dictionary service offline mode set to: $_isOfflineMode');
  }

  /// Play pronunciation for a Chinese word or character using Flutter TTS
  Future<bool> playPronunciation(String text, {BuildContext? context}) async {
    if (text.isEmpty) {
      AnxLog.info('Empty text provided for pronunciation');
      return false;
    }

    // Request access through TtsFactory conflict manager
    final factory = TtsFactory();
    final accessGranted = await factory.requestAccess(
      TtsContext.dictionaryPronunciation,
      TtsEngineType.systemTts,
    );

    if (!accessGranted) {
      AnxLog.warning('Dictionary pronunciation access denied due to conflict');

      final error = TtsError(
        context: TtsContext.dictionaryPronunciation,
        failedEngine: TtsEngineType.systemTts,
        type: TtsErrorType.resourceEngineInUse,
        message: 'Cannot play pronunciation - TTS resource is busy',
        severity: TtsErrorSeverity.low,
      );

      if (context != null && context.mounted) {
        _handleTtsError(error, context);
      } else {
        error.log(); // Still log the error even if context is not available
      }
      return false;
    }

    // Initialize TTS if not already done
    if (!_ttsInitialized) {
      await _initializeDictionaryTts();
    }

    try {
      // Use Flutter TTS for offline pronunciation (works without internet)
      AnxLog.info('Playing pronunciation for: "$text" using Flutter TTS');

      // Stop any current speech
      await _dictionaryTts.stop();

      // Speak the text
      final result = await _dictionaryTts.speak(text);

      if (result == 1) {
        AnxLog.info('Successfully started pronunciation for: "$text"');
        return true;
      } else {
        AnxLog.warning('TTS speak returned: $result for text: "$text"');

        // Try fallback with Google Translate TTS if Flutter TTS fails and we're online
        if (!_isOfflineMode) {
          final fallbackResult = context != null && context.mounted
              ? await _playPronunciationFallback(text, context)
              : await _playPronunciationFallback(text, null);
          if (fallbackResult) {
            return true;
          }
        }

        // Create and handle TTS error
        final error = TtsError(
          context: TtsContext.dictionaryPronunciation,
          failedEngine: TtsEngineType.systemTts,
          type: TtsErrorType.platformEngineUnavailable,
          message: 'System TTS failed to play pronunciation',
          severity: TtsErrorSeverity.medium,
          technicalDetails: 'Text: $text, Result: $result',
        );

        if (context != null && context.mounted) {
          _handleTtsError(error, context);
        } else {
          error.log(); // Still log the error even if context is not available
        }
        return false;
      }
    } catch (e) {
      AnxLog.severe('Error with Flutter TTS pronunciation: $e');

      // Try fallback with Google Translate TTS if we're online
      if (!_isOfflineMode) {
        final fallbackResult = context != null && context.mounted
            ? await _playPronunciationFallback(text, context)
            : await _playPronunciationFallback(text, null);
        if (fallbackResult) {
          return true;
        }
      }

      // Create and handle TTS error
      final error = TtsError(
        context: TtsContext.dictionaryPronunciation,
        failedEngine: TtsEngineType.systemTts,
        type: TtsErrorType.platformEngineUnavailable,
        message: 'Dictionary pronunciation failed',
        severity: TtsErrorSeverity.high,
        originalError: e,
        technicalDetails: 'Text: $text',
      );

      if (context != null && context.mounted) {
        _handleTtsError(error, context);
      } else {
        error.log(); // Still log the error even if context is not available
      }
      return false;
    } finally {
      // Always release access when done
      await factory.releaseAccess(
        TtsContext.dictionaryPronunciation,
        TtsEngineType.systemTts,
      );
    }
  }

  /// Fallback pronunciation using Google Translate TTS (requires internet)
  Future<bool> _playPronunciationFallback(
    String text,
    BuildContext? context,
  ) async {
    try {
      AnxLog.info('Trying Google Translate TTS fallback for: "$text"');

      // Use Google Translate TTS as fallback
      final fallbackUrl =
          'https://translate.google.com/translate_tts?ie=UTF-8&q=$text&tl=zh-CN&client=tw-ob';

      await _audioPlayer.setUrl(fallbackUrl);
      await _audioPlayer.play();

      AnxLog.info(
        'Successfully played pronunciation using Google Translate TTS',
      );
      return true;
    } catch (e) {
      AnxLog.severe('Google Translate TTS fallback failed: $e');

      // Show error message if context is provided and still mounted
      if (context != null && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Could not play pronunciation for "$text"'),
            duration: const Duration(seconds: 2),
          ),
        );
      }
      return false;
    }
  }

  /// Look up a Chinese word or character in online dictionary
  Future<DictionaryEntry?> lookupChinese(String word) async {
    if (word.isEmpty) return null;

    // Check cache first
    if (_dictionaryCache.containsKey(word)) {
      AnxLog.info('Dictionary cache hit for "$word"');
      return _dictionaryCache[word];
    }

    // If in offline mode, return null if not in cache
    if (_isOfflineMode) {
      AnxLog.info('Dictionary lookup skipped in offline mode for "$word"');
      return null;
    }

    AnxLog.info('Looking up Chinese word online: "$word"');

    try {
      // Try MDBG API first (unofficial API, but reliable)
      final response = await _httpClient.get(
        Uri.parse(
          'https://api.mdbg.net/chinese/dictionary?word=$word&fields=traditional,simplified,pinyin,definitions',
        ),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body) as Map<String, dynamic>?;
        if (data != null &&
            data.containsKey('results') &&
            (data['results'] as List?)?.isNotEmpty == true) {
          final results = data['results'] as List;
          final result = results[0] as Map<String, dynamic>;

          // Prioritize simplified Chinese - use simplified form for both fields
          final simplified = result['simplified'] as String? ?? word;

          final entry = DictionaryEntry(
            traditional: simplified, // Use simplified for both fields
            simplified: simplified,
            pinyin: result['pinyin'] as String? ?? '',
            definitions:
                _parseDefinitions(result['definitions'] as String? ?? ''),
            hskLevel: result['hsk_level'] as int?,
            frequency: result['frequency'] as int?,
          );

          // Cache the result
          _dictionaryCache[word] = entry;
          // Save cache periodically
          _saveCachedData();
          return entry;
        }
      }

      // Try fallback to Wiktionary API
      final wiktResponse = await _httpClient.get(
        Uri.parse(
          'https://en.wiktionary.org/api/rest_v1/page/definition/$word',
        ),
      );

      if (wiktResponse.statusCode == 200) {
        final data = json.decode(wiktResponse.body) as Map<String, dynamic>?;
        if (data != null &&
            data.containsKey('zh') &&
            (data['zh'] as List?)?.isNotEmpty == true) {
          final zhList = data['zh'] as List;
          final zhData = zhList[0] as Map<String, dynamic>;

          // Extract definitions
          List<String> definitions = [];
          if (zhData.containsKey('definitions') &&
              (zhData['definitions'] as List?)?.isNotEmpty == true) {
            final definitionsList = zhData['definitions'] as List;
            definitions = definitionsList
                .map(
                  (def) =>
                      (def as Map<String, dynamic>)['definition']?.toString() ??
                      '',
                )
                .where((def) => def.isNotEmpty)
                .toList();
          }

          final entry = DictionaryEntry(
            traditional: word, // Use the same word for both fields
            simplified: word,
            pinyin: '', // Wiktionary doesn't provide pinyin in the API
            definitions: definitions,
            hskLevel: 0,
            frequency: 0,
          );

          // Cache the result
          _dictionaryCache[word] = entry;
          // Save cache periodically
          _saveCachedData();
          return entry;
        }
      }

      // Try third fallback to Chinese Tools API
      final ctResponse = await _httpClient.get(
        Uri.parse(
          'https://www.chinese-tools.com/tools/chinese-dictionary-api.html?q=$word',
        ),
      );

      if (ctResponse.statusCode == 200) {
        // Parse HTML response (simplified implementation)
        final html = ctResponse.body;

        // Extract pinyin and definition using regex (simplified)
        final pinyinMatch =
            RegExp(r'<span class="pinyin">(.*?)</span>').firstMatch(html);
        final defMatch =
            RegExp(r'<div class="definition">(.*?)</div>').firstMatch(html);

        if (pinyinMatch != null || defMatch != null) {
          final definitions =
              defMatch?.group(1)?.replaceAll(RegExp(r'<[^>]*>'), '') ?? '';

          final entry = DictionaryEntry(
            traditional: word, // Use the same word for both fields
            simplified: word,
            pinyin: pinyinMatch?.group(1) ?? '',
            definitions: _parseDefinitions(definitions),
            hskLevel: 0,
            frequency: 0,
          );

          // Cache the result
          _dictionaryCache[word] = entry;
          // Save cache periodically
          _saveCachedData();
          return entry;
        }
      }
    } catch (e) {
      AnxLog.severe('Error looking up word online: $e');
    }

    // If all online lookups fail, create a basic entry
    AnxLog.info(
      'No online dictionary entry found for "$word", creating basic entry',
    );
    final basicEntry = DictionaryEntry(
      traditional: word, // Use the same word for both fields
      simplified: word,
      pinyin: '',
      definitions: ['No definition available'],
      hskLevel: 0,
      frequency: 0,
    );

    // Cache the basic entry
    _dictionaryCache[word] = basicEntry;
    return basicEntry;
  }

  /// Parse definition string into a list of definitions
  List<String> _parseDefinitions(String definitionsStr) {
    if (definitionsStr.isEmpty) return ['No definition available'];
    return definitionsStr.split('/').where((def) => def.isNotEmpty).toList();
  }

  /// Get character information
  Future<CharacterInfo?> getCharacterInfo(String character) async {
    if (character.isEmpty) return null;

    // Check cache first
    if (_characterInfoCache.containsKey(character)) {
      AnxLog.info('Character info cache hit for "$character"');
      return _characterInfoCache[character];
    }

    // If in offline mode, return null if not in cache
    if (_isOfflineMode) {
      AnxLog.info(
        'Character info lookup skipped in offline mode for "$character"',
      );
      return null;
    }

    AnxLog.info('Getting character info for: "$character"');

    try {
      // Try to get basic character info
      final response = await _httpClient.get(
        Uri.parse('https://api.ctext.org/getcharacter?char=$character'),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body) as Map<String, dynamic>?;

        if (data != null && data.containsKey('radical')) {
          // Create character info with basic data
          final characterInfo = CharacterInfo(
            character: character,
            radical: data['radical'] as String? ?? '',
            components: [character],
          );

          // Cache the result
          _characterInfoCache[character] = characterInfo;
          // Save cache periodically
          _saveCachedData();

          return characterInfo;
        }
      }
    } catch (e) {
      AnxLog.severe('Error getting character info: $e');
    }

    // If all lookups fail, create a basic entry
    final basicInfo = CharacterInfo(
      character: character,
      radical: '',
      components: [character],
    );

    // Cache the basic entry
    _characterInfoCache[character] = basicInfo;
    return basicInfo;
  }

  /// Find words containing a specific character
  Future<List<DictionaryEntry>> findWordsWithCharacter(String character) async {
    if (character.isEmpty) return [];

    // If in offline mode, only return cached entries
    if (_isOfflineMode) {
      AnxLog.info('Finding words with character in offline mode: "$character"');
      final cachedEntries = _dictionaryCache.values
          .where(
            (entry) =>
                entry.simplified.contains(character) ||
                entry.traditional.contains(character),
          )
          .toList();

      return cachedEntries;
    }

    AnxLog.info('Finding words with character online: "$character"');

    try {
      // Try MDBG API to find words containing the character
      final response = await _httpClient.get(
        Uri.parse(
          'https://api.mdbg.net/chinese/dictionary?word=$character&fields=words',
        ),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body) as Map<String, dynamic>?;
        if (data != null &&
            data.containsKey('words') &&
            (data['words'] as List?)?.isNotEmpty == true) {
          final words = data['words'] as List;
          final entries = <DictionaryEntry>[];

          for (final word in words) {
            final wordMap = word as Map<String, dynamic>;
            if (wordMap.containsKey('simplified') &&
                (wordMap['simplified'] as String?)?.contains(character) ==
                    true) {
              final entry = DictionaryEntry(
                traditional: wordMap['traditional'] as String? ??
                    wordMap['simplified'] as String? ??
                    '',
                simplified: wordMap['simplified'] as String? ?? '',
                pinyin: wordMap['pinyin'] as String? ?? '',
                definitions:
                    _parseDefinitions(wordMap['definitions'] as String? ?? ''),
                hskLevel: wordMap['hsk_level'] as int?,
                frequency: wordMap['frequency'] as int?,
              );
              entries.add(entry);

              // Cache the entry
              final simplifiedKey = wordMap['simplified'] as String? ?? '';
              if (simplifiedKey.isNotEmpty) {
                _dictionaryCache[simplifiedKey] = entry;
              }
            }
          }

          // Save cache periodically
          _saveCachedData();

          return entries;
        }
      }

      // If no results, return an empty list
      return [];
    } catch (e) {
      AnxLog.severe('Error finding words with character: $e');

      // In case of error, return cached entries containing the character
      final cachedEntries = _dictionaryCache.values
          .where(
            (entry) =>
                entry.simplified.contains(character) ||
                entry.traditional.contains(character),
          )
          .toList();

      return cachedEntries;
    }
  }

  /// Clear the cache
  Future<void> clearCache() async {
    _dictionaryCache.clear();
    _characterInfoCache.clear();

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_dictionaryCacheKey);
      await prefs.remove(_characterInfoCacheKey);
      AnxLog.info('Dictionary cache cleared');
    } catch (e) {
      AnxLog.severe('Error clearing dictionary cache: $e');
    }
  }

  /// Handle TTS errors with context-aware messaging
  void _handleTtsError(TtsError error, BuildContext? context) {
    // Log the error
    error.log();

    // Show user-friendly message if context is available and mounted
    if (context != null && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(error.getUserFriendlyMessage()),
          duration: const Duration(seconds: 3),
          backgroundColor: error.severity == TtsErrorSeverity.high
              ? Colors.red.shade700
              : Colors.orange.shade700,
        ),
      );
    }
  }

  /// Dispose resources
  void dispose() {
    _audioPlayer.dispose();

    // Stop and dispose dictionary TTS
    if (_ttsInitialized) {
      _dictionaryTts.stop();
      // Note: FlutterTts doesn't have a dispose method, just stop it
    }

    _saveCachedData();
    _httpClient.close();
  }
}

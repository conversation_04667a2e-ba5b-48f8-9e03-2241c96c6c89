import 'dart:async';
import 'dart:io';

import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/models/dictionary/dictionary_entry.dart';
import 'package:dasso_reader/service/dictionary/online_dictionary_service.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:flutter/services.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:pinyin/pinyin.dart';

/// Service class for Chinese dictionary operations
class DictionaryService {
  static final DictionaryService _instance = DictionaryService._internal();

  /// Singleton instance
  factory DictionaryService() => _instance;

  DictionaryService._internal();

  /// Database instance
  Database? _database;

  /// Flag to track initialization status
  bool _isInitialized = false;

  /// Flag to track dictionary preloading status
  bool _isPreloading = false;

  /// Flag to track if dictionary is preloaded
  bool _isPreloaded = false;

  /// Locks for synchronization (removed - using completers instead)

  /// In-memory cache for dictionary entries
  final Map<String, DictionaryEntry> _cache = {};

  /// Maximum cache size for non-preloaded mode
  final int _maxCacheSize = 500;

  /// Online dictionary service for fallback lookups
  final OnlineDictionaryService _onlineDictionaryService =
      OnlineDictionaryService();

  /// Flag to track network connectivity
  bool _isOffline = false;

  /// Subscription for connectivity changes
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;

  /// Completer for initialization to handle concurrent calls
  static Completer<void>? _initCompleter;

  /// Completer for preloading to handle concurrent calls
  static Completer<bool>? _preloadCompleter;

  /// Initialize the dictionary service
  Future<void> initialize() async {
    // Quick check without lock
    if (_isInitialized) return;

    // Use completer-based synchronization to handle concurrent calls
    if (_initCompleter != null) {
      // Another initialization is in progress, wait for it
      return await _initCompleter!.future;
    }

    // Create completer and start initialization
    _initCompleter = Completer<void>();

    try {
      AnxLog.info('Initializing dictionary service');

      // Double-check after acquiring the completer
      if (_isInitialized) {
        _initCompleter!.complete();
        return;
      }

      final db = await database;

      // Verify database has entries
      final count = Sqflite.firstIntValue(
        await db.rawQuery('SELECT COUNT(*) FROM dictionary'),
      );

      if (count == 0) {
        AnxLog.info(
          'Dictionary database is empty. Will use online dictionary services.',
        );
      } else {
        AnxLog.info('Dictionary database has $count entries');
      }

      // Initialize online dictionary service
      await _onlineDictionaryService.initialize();

      // Set up connectivity monitoring
      await _setupConnectivityMonitoring();

      _isInitialized = true;
      _initCompleter!.complete();
    } catch (e) {
      AnxLog.severe('Error initializing dictionary service: $e');
      // Continue with initialization even if there's an error
      // to allow fallback to online services
      _isInitialized = true;
      _initCompleter!.complete();
    } finally {
      _initCompleter = null;
    }
  }

  /// Set up monitoring for network connectivity changes
  Future<void> _setupConnectivityMonitoring() async {
    // Check initial connectivity
    final connectivityResults = await Connectivity().checkConnectivity();
    _updateConnectivityStatus(connectivityResults);

    // Listen for connectivity changes
    _connectivitySubscription =
        Connectivity().onConnectivityChanged.listen((results) {
      _updateConnectivityStatus(results);
    });
  }

  /// Update connectivity status and set offline mode
  void _updateConnectivityStatus(List<ConnectivityResult> results) {
    final wasOffline = _isOffline;
    _isOffline = results.isEmpty || results.contains(ConnectivityResult.none);

    // Only log if status changed
    if (wasOffline != _isOffline) {
      AnxLog.info(
        'Network connectivity changed: ${_isOffline ? 'offline' : 'online'}',
      );
      _onlineDictionaryService.setOfflineMode(_isOffline);
    }
  }

  /// Get the database instance, initializing if necessary
  Future<Database> get database async {
    if (_database != null) return _database!;

    // Simple synchronization for database initialization
    _database = await _initDatabase();
    return _database!;
  }

  /// Check if the dictionary service is initialized
  bool get isInitialized => _isInitialized;

  /// Check if the device is currently offline
  bool get isOffline => _isOffline;

  /// Initialize the database
  Future<Database> _initDatabase() async {
    // Get the documents directory
    final documentsDirectory = await getApplicationDocumentsDirectory();
    final path = join(documentsDirectory.path, 'chinese_dictionary.db');

    // Check if database already exists
    final exists = await databaseExists(path);

    // If database doesn't exist, create it
    if (!exists) {
      try {
        AnxLog.info(
          'Dictionary database does not exist, copying from assets...',
        );
        await _copyFromAsset(path);
        AnxLog.info('Dictionary database copied successfully');
      } catch (e) {
        AnxLog.severe('Failed to copy dictionary database: $e');
        // Create empty database if copy fails
        AnxLog.info('Creating empty database instead');
        return await _createEmptyDatabase(path);
      }
    }

    // Open the database
    try {
      final db = await openDatabase(path, version: 1);

      // Verify database has tables
      final tables = await db.query(
        'sqlite_master',
        where: "type = 'table' AND name IN ('dictionary', 'character_info')",
      );

      if (tables.length < 2) {
        AnxLog.severe('Dictionary database is missing tables, recreating...');
        await db.close();
        await deleteDatabase(path);
        return _initDatabase();
      }

      // Log some stats
      final dictCount = Sqflite.firstIntValue(
        await db.rawQuery('SELECT COUNT(*) FROM dictionary'),
      );
      final charCount = Sqflite.firstIntValue(
        await db.rawQuery('SELECT COUNT(*) FROM character_info'),
      );

      AnxLog.info(
        'Dictionary database opened successfully with $dictCount entries and $charCount characters',
      );

      _isInitialized = true;
      return db;
    } catch (e) {
      AnxLog.severe('Error opening dictionary database: $e');
      // If there's an error, try to recreate the database
      if (await databaseExists(path)) {
        await deleteDatabase(path);
        AnxLog.info('Deleted corrupted database, trying to recreate...');
        return _initDatabase();
      }

      // If all else fails, create an empty database
      return _createEmptyDatabase(path);
    }
  }

  /// Copy database from assets to documents directory
  Future<void> _copyFromAsset(String dbPath) async {
    try {
      // Read the pre-populated database from assets
      final data =
          await rootBundle.load('assets/dictionary/chinese_dictionary.db');

      // Write to documents directory
      final bytes =
          data.buffer.asUint8List(data.offsetInBytes, data.lengthInBytes);
      await File(dbPath).writeAsBytes(bytes, flush: true);

      AnxLog.info(
        'Dictionary database copied from assets (${bytes.length} bytes)',
      );
    } catch (e) {
      AnxLog.severe('Error copying dictionary database: $e');
      rethrow;
    }
  }

  /// Create an empty dictionary database
  Future<Database> _createEmptyDatabase(String dbPath) async {
    AnxLog.info('Creating empty dictionary database at $dbPath');

    final db = await openDatabase(
      dbPath,
      version: 1,
      onCreate: (db, version) async {
        // Create dictionary table
        await db.execute('''
          CREATE TABLE dictionary (
            id INTEGER PRIMARY KEY,
            traditional TEXT,
            simplified TEXT,
            pinyin TEXT,
            definitions TEXT,
            hsk_level INTEGER,
            frequency INTEGER
          )
        ''');

        // Create character info table
        await db.execute('''
          CREATE TABLE character_info (
            character TEXT PRIMARY KEY,
            radical TEXT,
            components TEXT
          )
        ''');

        // Create indices for faster lookups
        await db
            .execute('CREATE INDEX idx_simplified ON dictionary(simplified)');
        await db
            .execute('CREATE INDEX idx_traditional ON dictionary(traditional)');
        await db.execute('CREATE INDEX idx_pinyin ON dictionary(pinyin)');
      },
    );

    AnxLog.info('Empty dictionary database created successfully');
    return db;
  }

  /// Lookup Chinese word or character with optimized preloaded dictionary
  /// Returns the first (highest priority) matching entry
  Future<DictionaryEntry?> lookupChinese(String text) async {
    if (text.isEmpty) return null;

    // Clean the word (remove whitespace, etc.)
    final cleanWord = text.trim();

    // Note: With complete CC-CEDICT database, all characters should have correct primary pronunciations

    // Check cache first (this will be instant if dictionary is preloaded)
    if (_cache.containsKey(cleanWord)) {
      DictionaryEntry cachedEntry = _cache[cleanWord]!;

      // If the cached entry has "Unknown" pinyin, try to generate proper pinyin
      if (cachedEntry.pinyin == 'Unknown' && _isChinese(cleanWord)) {
        try {
          String pinyinText = await _generatePinyin(cleanWord);
          // Create a new entry with the generated pinyin
          cachedEntry = DictionaryEntry(
            traditional: cachedEntry.traditional,
            simplified: cachedEntry.simplified,
            pinyin: pinyinText,
            definitions: cachedEntry.definitions,
            hskLevel: cachedEntry.hskLevel,
            frequency: cachedEntry.frequency,
          );
          // Update the cache with the improved entry
          _cache[cleanWord] = cachedEntry;
        } catch (e) {
          AnxLog.warning('Error generating pinyin for cached entry: $e');
        }
      }

      return cachedEntry;
    }

    // If dictionary is preloaded, we should have found it already if it exists
    // But we'll still check for compound words and other special cases

    // For preloaded dictionary, try compound word match in memory first
    if (_isPreloaded && cleanWord.length > 1) {
      // Find entries that start with this word
      final compoundMatches = _cache.entries
          .where(
            (entry) =>
                entry.key.startsWith(cleanWord) && entry.key != cleanWord,
          )
          .map((entry) => entry.value)
          .toList();

      if (compoundMatches.isNotEmpty) {
        // Sort by length to get the closest match
        compoundMatches
            .sort((a, b) => a.simplified.length.compareTo(b.simplified.length));

        final match = compoundMatches.first;
        AnxLog.info(
          'Found compound match in preloaded dictionary: ${match.simplified}',
        );

        // Cache this result for the exact query
        _cache[cleanWord] = match;
        return match;
      }
    }

    // If not in preloaded dictionary or preloading is not enabled,
    // perform regular database lookup
    if (!_isPreloaded) {
      DictionaryEntry? entry = await _lookupChineseImpl(cleanWord);

      // Cache the result if not null
      if (entry != null) {
        // Implement simple LRU cache - remove oldest entry if cache is full
        if (!_isPreloaded && _cache.length >= _maxCacheSize) {
          _cache.remove(_cache.keys.first);
        }
        _cache[cleanWord] = entry;
      }

      return entry;
    }

    // If we get here with preloaded dictionary, it means the word wasn't found
    // Create a fallback entry with pinyin if it's Chinese
    if (_isChinese(cleanWord)) {
      String pinyinText = await _generatePinyin(cleanWord);

      final fallbackEntry = DictionaryEntry(
        traditional: cleanWord,
        simplified: cleanWord,
        pinyin: pinyinText,
        definitions: ['No definition available'],
        hskLevel: 0,
        frequency: null,
      );

      // Cache this fallback entry
      if (!_isPreloaded && _cache.length >= _maxCacheSize) {
        _cache.remove(_cache.keys.first);
      }
      _cache[cleanWord] = fallbackEntry;

      AnxLog.info('Created fallback entry for "$cleanWord"');

      return fallbackEntry;
    }

    return null;
  }

  /// Lookup Chinese word or character and return ALL matching entries
  /// This method returns all CC-CEDICT entries for comprehensive definition display
  Future<List<DictionaryEntry>> lookupChineseAll(String text) async {
    if (text.isEmpty) return [];

    // Clean the word (remove whitespace, etc.)
    final cleanWord = text.trim();

    try {
      final db = await database;

      // Get ALL exact matches with simplified Chinese with proper prioritization
      var results = await db.query(
        'dictionary',
        where: 'simplified = ?',
        whereArgs: [cleanWord],
        orderBy: _getPriorityOrderBy(),
        // No limit - get all entries
      );

      if (results.isNotEmpty) {
        AnxLog.info(
          'Found ${results.length} dictionary entries for simplified form: $cleanWord',
        );
        return results.map((map) => DictionaryEntry.fromMap(map)).toList();
      }

      // Try exact match with traditional Chinese with proper prioritization
      results = await db.query(
        'dictionary',
        where: 'traditional = ?',
        whereArgs: [cleanWord],
        orderBy: _getPriorityOrderBy(),
        // No limit - get all entries
      );

      if (results.isNotEmpty) {
        AnxLog.info(
          'Found ${results.length} dictionary entries for traditional form: $cleanWord',
        );
        return results.map((map) => DictionaryEntry.fromMap(map)).toList();
      }

      // If no exact matches found, return empty list
      // (compound word matching can be handled separately if needed)
      AnxLog.info('No dictionary entries found for "$cleanWord"');
      return [];
    } catch (e) {
      AnxLog.severe('Error looking up Chinese word (all entries): $e');
      return [];
    }
  }

  /// Helper method to generate pinyin for Chinese text
  Future<String> _generatePinyin(String text) async {
    try {
      // Use the pinyin package to generate pinyin with tone marks
      return PinyinHelper.getPinyin(
        text,
        separator: ' ',
        format: PinyinFormat.WITH_TONE_MARK,
      );
    } catch (e) {
      AnxLog.warning('Error generating pinyin: $e');
      return 'Unknown';
    }
  }

  /// Check if the text is Chinese
  bool _isChinese(String text) {
    // Simple check: if the text contains any Chinese characters
    return RegExp(r'[\u4e00-\u9fa5]').hasMatch(text);
  }

  /// Get the ORDER BY clause for prioritizing dictionary entries
  /// Prioritizes entries by: HSK level (lower first), frequency (lower first), then ID
  String _getPriorityOrderBy() {
    return '''
      CASE
        WHEN hsk_level IS NOT NULL AND hsk_level > 0 THEN hsk_level
        ELSE 999
      END ASC,
      CASE
        WHEN frequency IS NOT NULL THEN frequency
        ELSE 999999
      END ASC,
      id ASC
    ''';
  }

  /// Implementation of the Chinese lookup without caching
  /// Returns the first matching entry (for backward compatibility)
  Future<DictionaryEntry?> _lookupChineseImpl(String text) async {
    if (text.isEmpty) return null;

    // Clean the word (remove whitespace, etc.)
    final cleanWord = text.trim();

    try {
      final db = await database;

      try {
        // Step 1: Try exact match with simplified Chinese with proper prioritization
        var results = await db.query(
          'dictionary',
          where: 'simplified = ?',
          whereArgs: [cleanWord],
          orderBy: _getPriorityOrderBy(),
          limit: 1,
        );

        if (results.isNotEmpty) {
          AnxLog.info(
            'Found exact dictionary entry in local database: ${results.first}',
          );
          return DictionaryEntry.fromMap(results.first);
        }

        // Step 2: Try exact match with traditional Chinese with proper prioritization
        results = await db.query(
          'dictionary',
          where: 'traditional = ?',
          whereArgs: [cleanWord],
          orderBy: _getPriorityOrderBy(),
          limit: 1,
        );

        if (results.isNotEmpty) {
          AnxLog.info(
            'Found exact dictionary entry by traditional form in local database: ${results.first}',
          );
          return DictionaryEntry.fromMap(results.first);
        }

        // Step 3: Try compound word match (for terms like 船上交货 when searching for 船上)
        if (cleanWord.length > 1) {
          results = await db.query(
            'dictionary',
            where: 'simplified LIKE ? OR traditional LIKE ?',
            whereArgs: ['$cleanWord%', '$cleanWord%'],
            limit: 1,
          );

          if (results.isNotEmpty) {
            AnxLog.info(
              'Found compound dictionary entry in local database: ${results.first}',
            );
            return DictionaryEntry.fromMap(results.first);
          }
        }

        // Step 4: If no local results, try online lookup
        AnxLog.info('No local dictionary entry found, trying online lookup');
        final onlineEntry =
            await _onlineDictionaryService.lookupChinese(cleanWord);

        if (onlineEntry != null) {
          AnxLog.info('Found dictionary entry online');

          // Save the online entry to local database for future use
          try {
            final id = await db.insert('dictionary', onlineEntry.toMap());
            AnxLog.info('Saved online entry to local database with ID: $id');
          } catch (e) {
            AnxLog.info('Could not save online entry to local database: $e');
          }

          return onlineEntry;
        }

        // If all lookups fail, create a fallback entry with pinyin
        if (_isChinese(cleanWord)) {
          String pinyinText = await _generatePinyin(cleanWord);

          final fallbackEntry = DictionaryEntry(
            traditional: cleanWord,
            simplified: cleanWord,
            pinyin: pinyinText,
            definitions: ['No definition available'],
            hskLevel: 0,
            frequency: null,
          );

          AnxLog.info('Created fallback entry for "$cleanWord"');
          return fallbackEntry;
        }

        AnxLog.info('No dictionary entry found for "$cleanWord"');
        return null;
      } catch (e) {
        AnxLog.severe('Error looking up Chinese word: $e');

        // Try online lookup as fallback for any database errors
        try {
          return await _onlineDictionaryService.lookupChinese(cleanWord);
        } catch (e) {
          AnxLog.severe('Online lookup also failed: $e');
          return null;
        }
      }
    } catch (e) {
      AnxLog.severe('Error accessing dictionary database: $e');

      // Try online lookup as fallback
      try {
        return await _onlineDictionaryService.lookupChinese(cleanWord);
      } catch (e) {
        AnxLog.severe('Online lookup also failed: $e');
        return null;
      }
    }
  }

  /// Debug method to inspect all entries for a specific character
  Future<List<DictionaryEntry>> debugGetAllEntriesForCharacter(
    String character,
  ) async {
    if (character.isEmpty) return [];

    // Initialize if needed
    if (!_isInitialized) {
      await initialize();
    }

    try {
      final db = await database;

      // Query ALL entries for this character (no limit)
      final results = await db.query(
        'dictionary',
        where: 'simplified = ? OR traditional = ?',
        whereArgs: [character, character],
        orderBy: _getPriorityOrderBy(),
      );

      AnxLog.info(
        'Found ${results.length} entries for character "$character":',
      );
      for (int i = 0; i < results.length; i++) {
        final entry = results[i];
        AnxLog.info(
          'Entry ${i + 1}: ${entry['simplified']} ${entry['pinyin']} HSK:${entry['hsk_level']} Freq:${entry['frequency']} - ${entry['definitions']}',
        );
      }

      return results.map((map) => DictionaryEntry.fromMap(map)).toList();
    } catch (e) {
      AnxLog.severe('Error debugging character entries: $e');
      return [];
    }
  }

  /// Find words containing a specific character
  Future<List<DictionaryEntry>> findWordsWithCharacter(String character) async {
    if (character.isEmpty) return [];

    // Initialize if needed
    if (!_isInitialized) {
      await initialize();
    }

    try {
      final db = await database;

      // Query the database for words containing the character with prioritization
      final results = await db.query(
        'dictionary',
        where: 'simplified LIKE ? OR traditional LIKE ?',
        whereArgs: ['%$character%', '%$character%'],
        orderBy: _getPriorityOrderBy(),
        limit: 20, // Limit to 20 results for performance
      );

      if (results.isNotEmpty) {
        return results.map((map) => DictionaryEntry.fromMap(map)).toList();
      }

      // If no results in local database, try online service
      if (!_isOffline) {
        return await _onlineDictionaryService.findWordsWithCharacter(character);
      }

      return [];
    } catch (e) {
      AnxLog.severe('Error finding words with character: $e');
      return [];
    }
  }

  /// Lookup English word in definitions
  Future<List<DictionaryEntry>> lookupEnglish(String query) async {
    if (query.isEmpty) return [];

    // Initialize if needed
    if (!_isInitialized) {
      await initialize();
    }

    try {
      final db = await database;

      // Query the database for definitions containing the English word
      final results = await db.query(
        'dictionary',
        where: 'definitions LIKE ?',
        whereArgs: ['%$query%'],
        limit: 20, // Limit to 20 results for performance
      );

      if (results.isNotEmpty) {
        return results.map((map) => DictionaryEntry.fromMap(map)).toList();
      }

      // If no results in local database, return empty list
      // We could implement online English lookup in the future
      return [];
    } catch (e) {
      AnxLog.severe('Error looking up English word: $e');
      return [];
    }
  }

  /// Preload the entire dictionary into memory for faster lookups
  Future<bool> preloadDictionary() async {
    // Check if already preloaded
    if (_isPreloaded) {
      AnxLog.info('Dictionary already preloaded');
      return true;
    }

    // Use completer-based synchronization to handle concurrent calls
    if (_preloadCompleter != null) {
      AnxLog.info('Dictionary preloading already in progress, waiting...');
      return await _preloadCompleter!.future;
    }

    // Create completer and start preloading
    _preloadCompleter = Completer<bool>();

    try {
      // Initialize if needed
      if (!_isInitialized) {
        await initialize();
      }

      // Double-check after initialization
      if (_isPreloaded) {
        _preloadCompleter!.complete(true);
        return true;
      }

      // Set preloading status
      _isPreloading = true;
      Prefs().dictionaryPreloadStatus = 1; // Loading

      AnxLog.info('Starting optimized dictionary preloading...');
      final startTime = DateTime.now();

      // Clear existing cache
      _cache.clear();

      final db = await database;

      // Get total count for progress tracking
      final totalCount = Sqflite.firstIntValue(
            await db.rawQuery('SELECT COUNT(*) FROM dictionary'),
          ) ??
          0;

      // Update entry count in preferences
      Prefs().dictionaryEntryCount = totalCount;

      AnxLog.info('Preloading $totalCount dictionary entries...');

      // Use progressive loading strategy
      final result = await _progressivePreload(db, totalCount);

      if (!result) {
        _preloadCompleter!.complete(false);
        return false;
      }

      final duration = DateTime.now().difference(startTime);

      // Set preloaded status
      _isPreloaded = true;
      _isPreloading = false;
      Prefs().dictionaryPreloadStatus = 2; // Loaded

      AnxLog.info(
        'Dictionary preloading completed: ${_cache.length} entries loaded in ${duration.inMilliseconds}ms',
      );

      _preloadCompleter!.complete(true);
      return true;
    } catch (e) {
      AnxLog.severe('Error preloading dictionary: $e');
      _isPreloading = false;
      _isPreloaded = false;
      Prefs().dictionaryPreloadStatus = 0; // Not loaded
      _preloadCompleter!.complete(false);
      return false;
    } finally {
      _preloadCompleter = null;
    }
  }

  /// Progressive dictionary preloading with optimized batching
  /// This method implements smart loading strategies for better performance
  Future<bool> _progressivePreload(Database db, int totalCount) async {
    try {
      // Phase 1: Load high-frequency words first (HSK 1-3)
      await _loadHighFrequencyWords(db);

      // Phase 2: Load remaining words in optimized batches
      await _loadRemainingWords(db, totalCount);

      return true;
    } catch (e) {
      AnxLog.severe('Error in progressive preload: $e');
      return false;
    }
  }

  /// Load high-frequency words first for immediate usability
  Future<void> _loadHighFrequencyWords(Database db) async {
    AnxLog.info('Phase 1: Loading high-frequency words (HSK 1-3)...');

    // Load HSK 1-3 words first (most commonly used)
    final highFreqResults = await db.query(
      'dictionary',
      where: 'hsk_level <= ? AND hsk_level > 0',
      whereArgs: [3],
      orderBy: 'hsk_level ASC, frequency DESC',
    );

    int processed = 0;
    for (var row in highFreqResults) {
      final entry = DictionaryEntry.fromMap(row);

      // Cache by simplified form
      _cache[entry.simplified] = entry;

      // Also cache by traditional form if different
      if (entry.traditional != entry.simplified) {
        _cache[entry.traditional] = entry;
      }

      processed++;

      // Yield control every 500 entries for high-priority words
      if (processed % 500 == 0) {
        await Future<void>.delayed(Duration.zero);
      }
    }

    AnxLog.info('Phase 1 complete: Loaded $processed high-frequency words');
  }

  /// Load remaining words with optimized batching
  Future<void> _loadRemainingWords(Database db, int totalCount) async {
    AnxLog.info('Phase 2: Loading remaining words...');

    // Get count of already loaded high-frequency words
    final loadedCount = _cache.length;
    final remainingCount = totalCount - loadedCount;

    if (remainingCount <= 0) {
      AnxLog.info('All words already loaded in Phase 1');
      return;
    }

    // Use adaptive batch size based on remaining count
    final batchSize = _calculateOptimalBatchSize(remainingCount);

    // Load remaining words (HSK 4+ and unclassified)
    int offset = 0;
    int processedInPhase2 = 0;

    while (processedInPhase2 < remainingCount) {
      // Query batch of remaining words
      final results = await db.query(
        'dictionary',
        where: 'hsk_level > ? OR hsk_level IS NULL OR hsk_level = 0',
        whereArgs: [3],
        limit: batchSize,
        offset: offset,
        orderBy: 'frequency DESC NULLS LAST',
      );

      if (results.isEmpty) break;

      // Process batch with micro-yielding
      await _processBatchWithMicroYielding(results);

      processedInPhase2 += results.length;
      offset += batchSize;

      // Progress update
      final totalProcessed = loadedCount + processedInPhase2;
      if (totalProcessed % 2000 == 0) {
        AnxLog.info('Preloaded $totalProcessed/$totalCount dictionary entries');
      }

      // Adaptive yielding based on batch size
      await Future<void>.delayed(Duration(microseconds: batchSize ~/ 10));
    }

    AnxLog.info('Phase 2 complete: Loaded $processedInPhase2 additional words');
  }

  /// Calculate optimal batch size based on remaining entries
  int _calculateOptimalBatchSize(int remainingCount) {
    if (remainingCount < 10000) return 1000;
    if (remainingCount < 50000) return 2000;
    if (remainingCount < 100000) return 3000;
    return 5000; // Maximum batch size
  }

  /// Process batch with micro-yielding to prevent UI blocking
  Future<void> _processBatchWithMicroYielding(
    List<Map<String, Object?>> results,
  ) async {
    const microBatchSize = 100;

    for (int i = 0; i < results.length; i += microBatchSize) {
      final endIndex = (i + microBatchSize < results.length)
          ? i + microBatchSize
          : results.length;

      // Process micro-batch
      for (int j = i; j < endIndex; j++) {
        final entry = DictionaryEntry.fromMap(results[j]);

        // Skip if already cached (from Phase 1)
        if (_cache.containsKey(entry.simplified)) continue;

        // Cache by simplified form
        _cache[entry.simplified] = entry;

        // Also cache by traditional form if different
        if (entry.traditional != entry.simplified &&
            !_cache.containsKey(entry.traditional)) {
          _cache[entry.traditional] = entry;
        }
      }

      // Micro-yield to prevent blocking
      if (i + microBatchSize < results.length) {
        await Future<void>.delayed(Duration.zero);
      }
    }
  }

  /// Unload the preloaded dictionary to free memory
  void unloadDictionary() {
    if (!_isPreloaded) return;

    AnxLog.info('Unloading preloaded dictionary...');
    _cache.clear();
    _isPreloaded = false;
    Prefs().dictionaryPreloadStatus = 0; // Not loaded
    AnxLog.info('Dictionary unloaded');
  }

  /// Check if the dictionary is preloaded
  bool get isPreloaded => _isPreloaded;

  /// Check if the dictionary is currently being preloaded
  bool get isPreloading => _isPreloading;

  /// Dispose resources
  void dispose() async {
    // Unload dictionary first
    unloadDictionary();

    if (_database != null) {
      await _database!.close();
      _database = null;
    }

    _connectivitySubscription?.cancel();
    _onlineDictionaryService.dispose();

    _isInitialized = false;
  }
}

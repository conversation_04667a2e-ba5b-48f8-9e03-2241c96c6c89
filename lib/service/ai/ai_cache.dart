import 'dart:convert';
import 'dart:io';

import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/utils/get_path/get_cache_dir.dart';

class AiCache {
  static const String cacheFileName = 'ai_cache.json';

  static Future<Map<String, dynamic>> readCache() async {
    final cacheDir = await getAnxCacheDir();
    final file = File('${cacheDir.path}/$cacheFileName');

    if (await file.exists()) {
      try {
        final content = await file.readAsString();
        return json.decode(content) as Map<String, dynamic>;
      } catch (e) {
        file.delete();
        return {};
      }
    }
    return {};
  }

  static Future<void> setAiCache(
    int hash,
    String data,
    String identifier,
  ) async {
    final cacheDir = await getAnxCacheDir();
    final file = File('${cacheDir.path}/$cacheFileName');
    final cache = await readCache();

    cache[hash.toString()] = {
      'data': data,
      'identifier': identifier,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };

    await file.writeAsString(json.encode(cache));
    await cleanCache();
  }

  static Future<String?> getAiCache(int hash) async {
    final cache = await readCache();
    final entry = cache[hash.toString()];
    if (entry != null) {
      String data = entry['data'] as String;
      String identifier = entry['identifier'] as String;
      // Use English text to avoid BuildContext async issues in static method
      return '$data\n\n> Cached by $identifier';
    }
    return null;
  }

  static Future<void> cleanCache() async {
    final maxCount = Prefs().maxAiCacheCount;
    var cache = await readCache();
    if (cache.length > maxCount) {
      final keys = cache.keys.toList();
      keys.sort((a, b) {
        final timestampA = (cache[a]['timestamp'] as num?)?.toInt() ?? 0;
        final timestampB = (cache[b]['timestamp'] as num?)?.toInt() ?? 0;
        return timestampA - timestampB;
      });
      final keysToRemove = keys.sublist(0, cache.length - maxCount);
      cache.removeWhere((key, _) => keysToRemove.contains(key));

      final cacheDir = await getAnxCacheDir();
      final file = File('${cacheDir.path}/$cacheFileName');
      await file.writeAsString(json.encode(cache), mode: FileMode.writeOnly);
    }
  }

  static Future<int> get cacheCount async {
    final cache = await readCache();
    return cache.length;
  }

  static Future<void> clearCache() async {
    final cacheDir = await getAnxCacheDir();
    final file = File('${cacheDir.path}/$cacheFileName');
    if (await file.exists()) {
      await file.delete();
    }
  }
}

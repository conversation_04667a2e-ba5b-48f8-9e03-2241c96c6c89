import 'dart:io';

import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/enums/ai_prompts.dart';

String generatePromptTest() {
  String prompt = Prefs().getAiPrompt(AiPrompts.test);
  String currentLocale = Prefs().locale?.languageCode ?? Platform.localeName;
  prompt = prompt.replaceAll('{{language_locale}}', currentLocale);
  return prompt;
}

String generatePromptSummaryTheChapter(String chapter) {
  String prompt = Prefs().getAiPrompt(AiPrompts.summaryTheChapter);
  prompt = prompt.replaceAll('{{chapter}}', chapter.trim());
  return prompt;
}

String generatePromptSummaryTheBook(String book, String author) {
  String prompt = Prefs().getAiPrompt(AiPrompts.summaryTheBook);
  prompt = prompt.replaceAll('{{book}}', book);
  prompt = prompt.replaceAll('{{author}}', author);
  return prompt;
}

String generatePromptSummaryThePreviousContent(String previousContent) {
  String prompt = Prefs().getAiPrompt(AiPrompts.summaryThePreviousContent);
  prompt = prompt.replaceAll('{{previous_content}}', previousContent.trim());
  return prompt;
}

String generatePromptGrammar(String text) {
  String prompt = Prefs().getAiPrompt(AiPrompts.grammar);
  prompt = prompt.replaceAll('{{text}}', text.trim());
  return prompt;
}

String generatePromptExampleSentences(String text) {
  String prompt = Prefs().getAiPrompt(AiPrompts.exampleSentences);
  prompt = prompt.replaceAll('{{text}}', text.trim());
  return prompt;
}

String generatePromptCharacterBreakdown(String text) {
  String prompt = Prefs().getAiPrompt(AiPrompts.characterBreakdown);
  prompt = prompt.replaceAll('{{text}}', text.trim());
  return prompt;
}

String generatePromptWordUsage(String text) {
  String prompt = Prefs().getAiPrompt(AiPrompts.wordUsage);
  prompt = prompt.replaceAll('{{text}}', text.trim());
  return prompt;
}

String generatePromptSynonymsAntonyms(String text) {
  String prompt = Prefs().getAiPrompt(AiPrompts.synonymsAntonyms);
  prompt = prompt.replaceAll('{{text}}', text.trim());
  return prompt;
}

String generatePromptCharacterMnemonics(String text) {
  String prompt = Prefs().getAiPrompt(AiPrompts.characterMnemonics);
  prompt = prompt.replaceAll('{{text}}', text.trim());
  return prompt;
}

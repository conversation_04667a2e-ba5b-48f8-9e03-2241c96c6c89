import 'dart:io';

import 'package:dasso_reader/config/design_system.dart';
import 'package:dasso_reader/config/shared_preference_provider.dart';
import 'package:dasso_reader/models/book.dart';
import 'package:dasso_reader/providers/book_list.dart';
import 'package:dasso_reader/service/book.dart';
import 'package:dasso_reader/utils/get_path/get_temp_dir.dart';
import 'package:dasso_reader/utils/log/common.dart';
import 'package:dasso_reader/widgets/bookshelf/book_bottom_sheet.dart';
import 'package:dasso_reader/widgets/bookshelf/book_folder.dart';
import 'package:dasso_reader/widgets/tips/bookshelf_tips.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_reorderable_grid_view/widgets/custom_draggable.dart';
import 'package:flutter_reorderable_grid_view/widgets/reorderable_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class BookshelfPage extends ConsumerStatefulWidget {
  const BookshelfPage({super.key});

  @override
  ConsumerState<BookshelfPage> createState() => BookshelfPageState();
}

class BookshelfPageState extends ConsumerState<BookshelfPage> {
  final _scrollController = ScrollController();
  final _gridViewKey = GlobalKey();
  TextEditingController searchBarController = TextEditingController();

  // Helper method to calculate optimal column count based on screen width
  int _calculateOptimalColumnCount(double width) {
    // Base on preferred width but enforce min/max constraints
    const double minBookWidth = 110.0; // Minimum width for book items
    const double maxBookWidth = 180.0; // Maximum width for book items
    final double preferredWidth = Prefs().bookCoverWidth;

    // Clamp the preferred width between min and max
    final double effectiveWidth =
        preferredWidth.clamp(minBookWidth, maxBookWidth);

    // Calculate columns based on breakpoints
    if (width >= DesignSystem.breakpointDesktop) {
      // More columns for desktop
      int columns = (width / effectiveWidth).floor();
      return columns.clamp(4, 8);
    } else if (width >= DesignSystem.breakpointTablet) {
      // Medium number for tablets
      int columns = (width / effectiveWidth).floor();
      return columns.clamp(3, 6);
    } else {
      // Fewer columns for phones
      int columns = (width / effectiveWidth).floor();
      return columns.clamp(2, 4);
    }
  }

  // Public method to search books
  void searchBooks(String? query) {
    // Provide haptic feedback when search changes
    HapticFeedback.selectionClick();

    // Use the provider to filter books
    ref.read(bookListProvider.notifier).search(query).then((_) {
      // Refresh state after search completes
      if (mounted) setState(() {});
    });
  }

  Future<void> importBook() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.any,
      allowMultiple: true,
    );

    if (result == null) {
      return;
    }

    List<PlatformFile> files = result.files;
    AnxLog.info('importBook files: ${files.toString()}');
    List<File> fileList = [];
    // FilePicker on Windows will return files with original path,
    // but on Android it will return files with temporary path.
    // So we need to save the files to the temp directory.
    if (!Platform.isAndroid) {
      fileList = await Future.wait(
        files.map((file) async {
          Directory tempDir = await getAnxTempDir();
          File tempFile = File('${tempDir.path}/${file.name}');
          await File(file.path!).copy(tempFile.path);
          return tempFile;
        }).toList(),
      );
    } else {
      fileList = files.map((file) => File(file.path!)).toList();
    }

    if (!mounted) return; // Guard against disposed widget
    importBookList(fileList, context, ref);
  }

  @override
  Widget build(BuildContext context) {
    void handleBottomSheet(BuildContext context, Book book) {
      HapticFeedback.mediumImpact(); // Add haptic feedback
      showBottomSheet(
        context: context,
        builder: (context) => BookBottomSheet(book: book),
      );
    }

    List<int> lockedIndices = [];

    Widget buildBookshelfBody = ref.watch(bookListProvider).when(
          data: (books) {
            for (int i = 0; i < books.length; i++) {
              // folder can't be dragged
              if (books[i].length != 1) {
                lockedIndices.add(i);
              }
            }
            return books.isEmpty
                ? const Center(child: BookshelfTips())
                : ReorderableBuilder(
                    // lock all index of books
                    lockedIndices: lockedIndices,
                    enableDraggable: true,
                    longPressDelay: const Duration(milliseconds: 300),
                    onReorder: (ReorderedListFunction<dynamic>
                        reorderedListFunction,) {},
                    scrollController: _scrollController,
                    onDragStarted: (index) {
                      if (books[index].length == 1) {
                        handleBottomSheet(context, books[index].first);
                        // add other books to lockedIndices
                        for (int i = 0; i < books.length; i++) {
                          if (i != index) {
                            lockedIndices.add(i);
                          }
                        }
                      }
                    },
                    onDragEnd: (index) {
                      // remove all books from lockedIndices
                      lockedIndices = [];
                      for (int i = 0; i < books.length; i++) {
                        if (books[i].length != 1) {
                          lockedIndices.add(i);
                        }
                      }
                      setState(() {});
                    },
                    children: [
                      ...books.map(
                        (book) {
                          return book.length == 1
                              ? CustomDraggable(
                                  key: Key(book.first.id.toString()),
                                  data: book.first,
                                  child: BookFolder(books: book),
                                )
                              : BookFolder(
                                  key: Key(book.first.id.toString()),
                                  books: book,
                                );
                        },
                      ),
                    ],
                    builder: (children) {
                      return LayoutBuilder(
                        builder: (context, constraints) {
                          return GridView(
                            key: _gridViewKey,
                            controller: _scrollController,
                            padding: EdgeInsets.symmetric(
                              horizontal:
                                  _getAdaptivePadding(constraints.maxWidth),
                              vertical: DesignSystem.spaceM,
                            ),
                            gridDelegate:
                                SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: _calculateOptimalColumnCount(
                                constraints.maxWidth,
                              ),
                              childAspectRatio: 0.55,
                              mainAxisSpacing: DesignSystem.spaceL,
                              crossAxisSpacing: DesignSystem.spaceM,
                            ),
                            children: children,
                          );
                        },
                      );
                    },
                  );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Center(child: Text(error.toString())),
        );

    // Replace DropTarget with regular container for Android only
    Widget body = buildBookshelfBody;

    return Scaffold(
      body: body,
    );
  }

  // Helper method to calculate adaptive padding based on screen width
  double _getAdaptivePadding(double width) {
    // Use DesignSystem constants for consistent spacing
    if (width >= DesignSystem.breakpointDesktop) {
      return DesignSystem.spaceXL; // More padding on very large screens
    } else if (width >= DesignSystem.breakpointTablet) {
      return DesignSystem.spaceL; // Medium padding on large screens
    } else {
      return DesignSystem.spaceM; // Standard padding on smaller screens
    }
  }
}

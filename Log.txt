INFO^*^ 2025-05-30 20:06:24.162872^*^ [Log file: /data/user/0/io.github.dassodev.dasso_reader/app_flutter/anx_reader.log]
INFO^*^ 2025-05-30 20:06:24.204952^*^ [Database: upgrade database from 0 to 6]
INFO^*^ 2025-05-30 20:06:24.205129^*^ [Database: create database version 6]
INFO^*^ 2025-05-30 20:06:24.299991^*^ [Initializing dictionary service]
INFO^*^ 2025-05-30 20:06:24.301666^*^ [Initializing dictionary service]
INFO^*^ 2025-05-30 20:06:24.303632^*^ [Dictionary database does not exist, copying from assets...]
INFO^*^ 2025-05-30 20:06:24.445345^*^ [Dictionary database copied from assets (18354176 bytes)]
INFO^*^ 2025-05-30 20:06:24.445439^*^ [Dictionary database copied successfully]
INFO^*^ 2025-05-30 20:06:24.458007^*^ [Dictionary database opened successfully with 123126 entries and 21 characters]
INFO^*^ 2025-05-30 20:06:24.458954^*^ [Dictionary database has 123126 entries]
INFO^*^ 2025-05-30 20:06:24.460184^*^ [Dictionary preloading is enabled, starting preloading...]
INFO^*^ 2025-05-30 20:06:24.460243^*^ [Starting dictionary preloading...]
INFO^*^ 2025-05-30 20:06:24.466067^*^ [Server: Serving at http://localhost:33953]
INFO^*^ 2025-05-30 20:06:24.466372^*^ [Preloading 123126 dictionary entries...]
INFO^*^ 2025-05-30 20:06:24.664316^*^ [Preloaded 1000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:24.670507^*^ [Preloaded 2000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:24.672256^*^ [Preloaded 3000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:24.679069^*^ [Preloaded 4000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:24.679730^*^ [Preloaded 5000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:24.684789^*^ [share: Receive share intent: ()]
INFO^*^ 2025-05-30 20:06:24.801954^*^ [Preloaded 6000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:24.803683^*^ [Preloaded 7000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:24.805454^*^ [Preloaded 8000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:24.806898^*^ [Preloaded 9000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:24.808439^*^ [Preloaded 10000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:24.853729^*^ [Preloaded 11000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:24.854685^*^ [Preloaded 12000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:24.855444^*^ [Preloaded 13000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:24.856272^*^ [Preloaded 14000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:24.857053^*^ [Preloaded 15000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:24.892625^*^ [Preloaded 16000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:24.893986^*^ [Preloaded 17000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:24.895370^*^ [Preloaded 18000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:24.896756^*^ [Preloaded 19000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:24.897930^*^ [Preloaded 20000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:24.939514^*^ [Preloaded 21000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:24.944345^*^ [Preloaded 22000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:24.945398^*^ [Preloaded 23000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:24.946420^*^ [Preloaded 24000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:24.947429^*^ [Preloaded 25000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:24.979609^*^ [Preloaded 26000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:24.980820^*^ [Preloaded 27000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:24.982996^*^ [Preloaded 28000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:24.984374^*^ [Preloaded 29000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:24.985180^*^ [Preloaded 30000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.017190^*^ [Preloaded 31000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.018312^*^ [Preloaded 32000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.019019^*^ [Preloaded 33000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.020^*^ [Preloaded 34000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.020650^*^ [Preloaded 35000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.047661^*^ [Preloaded 36000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.048693^*^ [Preloaded 37000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.049482^*^ [Preloaded 38000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.050251^*^ [Preloaded 39000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.050951^*^ [Preloaded 40000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.075246^*^ [Preloaded 41000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.076702^*^ [Preloaded 42000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.083912^*^ [Preloaded 43000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.084762^*^ [Preloaded 44000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.092251^*^ [Preloaded 45000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.123729^*^ [Preloaded 46000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.124648^*^ [Preloaded 47000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.126436^*^ [Preloaded 48000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.127540^*^ [Preloaded 49000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.128276^*^ [Preloaded 50000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.170799^*^ [Preloaded 51000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.172651^*^ [Preloaded 52000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.176729^*^ [Preloaded 53000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.177845^*^ [Preloaded 54000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.179335^*^ [Preloaded 55000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.218693^*^ [Preloaded 56000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.219785^*^ [Preloaded 57000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.220756^*^ [Preloaded 58000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.221711^*^ [Preloaded 59000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.222588^*^ [Preloaded 60000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.286983^*^ [Preloaded 61000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.288169^*^ [Preloaded 62000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.289340^*^ [Preloaded 63000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.290721^*^ [Preloaded 64000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.292185^*^ [Preloaded 65000/123126 dictionary entries]
SEVERE^*^ 2025-05-30 20:06:25.312362^*^ [Exception: Update: Failed to check for updates DioException [bad response]: This exception was thrown because the response has a status code of 404 and RequestOptions.validateStatus was configured to throw for this status code. The status code of 404 has the following meaning: "Client error - the request contains bad syntax or cannot be fulfilled" Read more about status codes at https://developer.mozilla.org/en-US/docs/Web/HTTP/Status In order to resolve this exception you typically have either to verify and fix your request code or you have to fix the server code. ] : #0      checkUpdate (package:dasso_reader/utils/check_update.dart:41) <asynchronous suspension> 
INFO^*^ 2025-05-30 20:06:25.327989^*^ [Preloaded 66000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.329678^*^ [Preloaded 67000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.330735^*^ [Preloaded 68000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.332479^*^ [Preloaded 69000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.335982^*^ [Preloaded 70000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.386714^*^ [Preloaded 71000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.388349^*^ [Preloaded 72000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.392559^*^ [Preloaded 73000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.395788^*^ [Preloaded 74000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.399687^*^ [Preloaded 75000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.432523^*^ [Preloaded 76000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.434104^*^ [Preloaded 77000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.435850^*^ [Preloaded 78000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.441587^*^ [Preloaded 79000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.442563^*^ [Preloaded 80000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.469381^*^ [Preloaded 81000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.470420^*^ [Preloaded 82000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.471225^*^ [Preloaded 83000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.472082^*^ [Preloaded 84000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.472947^*^ [Preloaded 85000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.514677^*^ [Preloaded 86000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.515648^*^ [Preloaded 87000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.516768^*^ [Preloaded 88000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.523385^*^ [Preloaded 89000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.524701^*^ [Preloaded 90000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.555894^*^ [Preloaded 91000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.557326^*^ [Preloaded 92000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.558616^*^ [Preloaded 93000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.560137^*^ [Preloaded 94000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.561531^*^ [Preloaded 95000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.588596^*^ [Preloaded 96000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.589626^*^ [Preloaded 97000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.590849^*^ [Preloaded 98000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.592276^*^ [Preloaded 99000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.593736^*^ [Preloaded 100000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.629280^*^ [Preloaded 101000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.630373^*^ [Preloaded 102000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.631216^*^ [Preloaded 103000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.631994^*^ [Preloaded 104000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.632793^*^ [Preloaded 105000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.660052^*^ [Preloaded 106000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.661186^*^ [Preloaded 107000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.661967^*^ [Preloaded 108000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.662731^*^ [Preloaded 109000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.663509^*^ [Preloaded 110000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.699689^*^ [Preloaded 111000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.700722^*^ [Preloaded 112000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.701596^*^ [Preloaded 113000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.702449^*^ [Preloaded 114000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.703507^*^ [Preloaded 115000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.729778^*^ [Preloaded 116000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.730712^*^ [Preloaded 117000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.731533^*^ [Preloaded 118000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.732302^*^ [Preloaded 119000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.733075^*^ [Preloaded 120000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.753283^*^ [Preloaded 121000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.754253^*^ [Preloaded 122000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.755175^*^ [Preloaded 123000/123126 dictionary entries]
INFO^*^ 2025-05-30 20:06:25.755572^*^ [Dictionary preloading completed: 195057 entries loaded in 1295ms]
INFO^*^ 2025-05-30 20:06:25.755612^*^ [Dictionary preloading completed successfully]
INFO^*^ 2025-05-30 20:06:41.905428^*^ [importBook files: [PlatformFile(path /data/user/0/io.github.dassodev.dasso_reader/cache/file_picker/1748606801848/Alice_cn.epub, name: Alice_cn.epub, bytes: null, readStream: null, size: 59212)]]
INFO^*^ 2025-05-30 20:06:41.905694^*^ [importBook fileList: [File: '/data/user/0/io.github.dassodev.dasso_reader/cache/file_picker/1748606801848/Alice_cn.epub']]
INFO^*^ 2025-05-30 20:06:42.791492^*^ [import start: book url: http://localhost:33953/1062065500]
INFO^*^ 2025-05-30 20:06:43.631142^*^ [Webview: book.js]
INFO^*^ 2025-05-30 20:06:43.650621^*^ [Webview: loadBook]
INFO^*^ 2025-05-30 20:06:43.652716^*^ [Webview: navigator.userAgent Mozilla/5.0 (Linux; Android 15; sdk_gphone64_arm64 Build/AE3A.240806.036; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/124.0.6367.219 Mobile Safari/537.36]
SEVERE^*^ 2025-05-30 20:06:43.703360^*^ [Error saving image: Invalid base64 format, expected "data:type;base64,data"]
INFO^*^ 2025-05-30 20:06:46.105508^*^ [Webview: book.js]
INFO^*^ 2025-05-30 20:06:46.155116^*^ [Webview: loadBook]
INFO^*^ 2025-05-30 20:06:46.171012^*^ [Webview: navigator.userAgent Mozilla/5.0 (Linux; Android 15; sdk_gphone64_arm64 Build/AE3A.240806.036; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/124.0.6367.219 Mobile Safari/537.36]
INFO^*^ 2025-05-30 20:06:46.186035^*^ [Server: Request for book: /data/user/0/io.github.dassodev.dasso_reader/app_flutter/file/çˆ±ä¸½ä¸æ¢¦æ¸¸ä»™å¢ƒ-1748606803702.epub]
WARNING^*^ 2025-05-30 20:06:46.248865^*^ [Webview: This page contains the following errors:error on line 23 at column 25: Opening and ending tag mismatch: br line 19 and p Below is a rendering of the page up to the first error.]
WARNING^*^ 2025-05-30 20:06:46.250668^*^ [Webview: This page contains the following errors:error on line 23 at column 25: Opening and ending tag mismatch: br line 19 and p Below is a rendering of the page up to the first error.]
INFO^*^ 2025-05-30 20:07:02.564683^*^ [Webview: Processing Chinese selection: é’¥]
INFO^*^ 2025-05-30 20:07:02.565126^*^ [Webview: Chinese selection detected: "é’¥"]
INFO^*^ 2025-05-30 20:07:02.565283^*^ [Webview: Original selection range: 274-275]
INFO^*^ 2025-05-30 20:07:02.565391^*^ [Webview: Using dictionary-based selection refinement]
INFO^*^ 2025-05-30 20:07:02.633984^*^ [Initializing Chinese segmentation service]
INFO^*^ 2025-05-30 20:07:02.634090^*^ [Initializing Maximum Matching Chinese segmentation service]
INFO^*^ 2025-05-30 20:07:02.757876^*^ [Chinese segmentation service initialized with 109759 words]
INFO^*^ 2025-05-30 20:07:02.757975^*^ [Chinese segmentation service initialized]
INFO^*^ 2025-05-30 20:07:02.866889^*^ [Segmented text into 249 parts in 108ms]
INFO^*^ 2025-05-30 20:07:02.867019^*^ [Total segmentation time: 109ms (processing: 108ms)]
INFO^*^ 2025-05-30 20:07:02.867044^*^ [Found word boundary 274-276 for position 274]
INFO^*^ 2025-05-30 20:07:02.867114^*^ [Found boundary [274, 276] for position 274 in "çˆ±ä¸½ä¸æ‰“å¼€äº†é—¨ï¼Œå‘çŽ°é—¨å¤–æ˜¯ä¸€æ¡å°èµ°å»Šï¼Œæ¯”..."]
INFO^*^ 2025-05-30 20:07:02.976524^*^ [Segmented text into 249 parts in 27ms]
INFO^*^ 2025-05-30 20:07:02.977088^*^ [Total segmentation time: 27ms (processing: 27ms)]
INFO^*^ 2025-05-30 20:07:02.977136^*^ [Found word boundary 274-276 for position 274]
INFO^*^ 2025-05-30 20:07:02.977155^*^ [Found boundary [274, 276] for position 274 in "çˆ±ä¸½ä¸æ‰“å¼€äº†é—¨ï¼Œå‘çŽ°é—¨å¤–æ˜¯ä¸€æ¡å°èµ°å»Šï¼Œæ¯”..."]
INFO^*^ 2025-05-30 20:07:02.989701^*^ [Webview: Dictionary-based selection refinement: [274, 275] to [274, 276]]
INFO^*^ 2025-05-30 20:07:02.990060^*^ [Webview: New selection text (dictionary-based): "é’¥åŒ™"]
INFO^*^ 2025-05-30 20:07:08.491931^*^ [Webview: Processing Chinese selection: çš„]
INFO^*^ 2025-05-30 20:07:08.492383^*^ [Webview: Chinese selection detected: "çš„"]
INFO^*^ 2025-05-30 20:07:08.493251^*^ [Webview: Original selection range: 63-64]
INFO^*^ 2025-05-30 20:07:08.493523^*^ [Webview: Using dictionary-based selection refinement]
INFO^*^ 2025-05-30 20:07:08.497326^*^ [Segmented Chinese text into 53 parts using Maximum Matching]
INFO^*^ 2025-05-30 20:07:08.497418^*^ [Segmented text into 53 parts in 1ms]
INFO^*^ 2025-05-30 20:07:08.497454^*^ [Total segmentation time: 1ms (processing: 1ms)]
INFO^*^ 2025-05-30 20:07:08.497475^*^ [Found word boundary 63-64 for position 63]
INFO^*^ 2025-05-30 20:07:08.497491^*^ [Found boundary [63, 64] for position 63 in "ç„¶è€Œç“¶å­ä¸Šæ²¡æœ‰"æ¯’è¯"å­—æ ·ï¼Œæ‰€ä»¥çˆ±ä¸½ä¸å†’..."]
INFO^*^ 2025-05-30 20:07:08.533567^*^ [âœ… MEMORY CACHE HIT: Retrieved cached segmentation with 53 parts]
INFO^*^ 2025-05-30 20:07:08.533670^*^ [Found word boundary 63-64 for position 63]
INFO^*^ 2025-05-30 20:07:08.533691^*^ [Found boundary [63, 64] for position 63 in "ç„¶è€Œç“¶å­ä¸Šæ²¡æœ‰"æ¯’è¯"å­—æ ·ï¼Œæ‰€ä»¥çˆ±ä¸½ä¸å†’..."]
INFO^*^ 2025-05-30 20:07:08.550667^*^ [Webview: Dictionary-based selection refinement: [63, 64] to [63, 64]]
INFO^*^ 2025-05-30 20:07:08.550892^*^ [Webview: New selection text (dictionary-based): "çš„"]
INFO^*^ 2025-05-30 20:07:18.399492^*^ [Webview: changeStyle {"fontSize":1.4,"fontName":"book","fontPath":"","fontWeight":400,"letterSpacing":0,"spacing":1.8,"paragraphSpacing":1,"textIndent":0,"fontColor":"#feffebff","backgroundColor":"#040404ff","topMargin":90,"bottomMargin":50,"sideMargin":6,"justify":true,"hyphenate":true,"pageTurnStyle":"slide","maxColumnCount":0}]
INFO^*^ 2025-05-30 20:07:19.601865^*^ [Webview: changeStyle {"fontSize":1.4,"fontName":"book","fontPath":"","fontWeight":400,"letterSpacing":0,"spacing":1.8,"paragraphSpacing":1,"textIndent":0,"fontColor":"#343434ff","backgroundColor":"#fbfbf3ff","topMargin":90,"bottomMargin":50,"sideMargin":6,"justify":true,"hyphenate":true,"pageTurnStyle":"slide","maxColumnCount":0}]
INFO^*^ 2025-05-30 20:07:22.066794^*^ [Webview: changeStyle {"fontSize":1.5,"fontName":"book","fontPath":"","fontWeight":400,"letterSpacing":0,"spacing":1.8,"paragraphSpacing":1,"textIndent":0,"fontColor":"#343434ff","backgroundColor":"#fbfbf3ff","topMargin":90,"bottomMargin":50,"sideMargin":6,"justify":true,"hyphenate":true,"pageTurnStyle":"slide","maxColumnCount":0}]
INFO^*^ 2025-05-30 20:07:23.097904^*^ [Webview: changeStyle {"fontSize":1.4,"fontName":"book","fontPath":"","fontWeight":400,"letterSpacing":0,"spacing":1.8,"paragraphSpacing":1,"textIndent":0,"fontColor":"#343434ff","backgroundColor":"#fbfbf3ff","topMargin":90,"bottomMargin":50,"sideMargin":6,"justify":true,"hyphenate":true,"pageTurnStyle":"slide","maxColumnCount":0}]
INFO^*^ 2025-05-30 20:07:28.149490^*^ [Webview: changeStyle {"fontSize":1.4,"fontName":"book","fontPath":"","fontWeight":400,"letterSpacing":0,"spacing":1.8,"paragraphSpacing":1,"textIndent":0,"fontColor":"#343434ff","backgroundColor":"#fbfbf3ff","topMargin":90,"bottomMargin":50,"sideMargin":6,"justify":true,"hyphenate":true,"pageTurnStyle":"scroll","maxColumnCount":0}]
INFO^*^ 2025-05-30 20:07:44.086072^*^ [Webview: changeStyle {"fontSize":1.4,"fontName":"book","fontPath":"","fontWeight":400,"letterSpacing":0,"spacing":1.8,"paragraphSpacing":1,"textIndent":0,"fontColor":"#343434ff","backgroundColor":"#fbfbf3ff","topMargin":90,"bottomMargin":50,"sideMargin":6,"justify":true,"hyphenate":true,"pageTurnStyle":"slide","maxColumnCount":0}]
INFO^*^ 2025-05-30 20:07:54.811014^*^ [Building main dictionary tab for text: "æ—¶å€™"]
INFO^*^ 2025-05-30 20:07:59.609576^*^ [Building main dictionary tab for text: "æ—¶"]
INFO^*^ 2025-05-30 20:07:59.612691^*^ [Building main dictionary tab for text: "æ—¶å€™"]
SEVERE^*^ 2025-05-30 20:08:08.642720^*^ [Error loading stroke order data: ClientException with SocketException: No route to host (OS Error: No route to host, errno = 113), address = cdn.jsdelivr.net, port = 38928, uri=https://cdn.jsdelivr.net/npm/hanzi-writer-data@2.0.1/%E6%97%B6.json]
INFO^*^ 2025-05-30 20:08:37.014373^*^ [Building main dictionary tab for text: "æ—¶å€™"]
INFO^*^ 2025-05-30 20:08:37.388385^*^ [Webview: book.js]
INFO^*^ 2025-05-30 20:08:37.410821^*^ [Webview: loadBook]
INFO^*^ 2025-05-30 20:08:37.435950^*^ [Webview: navigator.userAgent Mozilla/5.0 (Linux; Android 15; sdk_gphone64_arm64 Build/AE3A.240806.036; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/124.0.6367.219 Mobile Safari/537.36]
INFO^*^ 2025-05-30 20:08:37.438301^*^ [Server: Request for book: /data/user/0/io.github.dassodev.dasso_reader/app_flutter/file/çˆ±ä¸½ä¸æ¢¦æ¸¸ä»™å¢ƒ-1748606803702.epub]
WARNING^*^ 2025-05-30 20:08:37.503306^*^ [Webview: This page contains the following errors:error on line 23 at column 25: Opening and ending tag mismatch: br line 19 and p Below is a rendering of the page up to the first error.]
SEVERE^*^ 2025-05-30 20:08:37.815455^*^ [Webview: ResizeObserver loop completed with undelivered notifications.]
INFO^*^ 2025-05-30 20:08:49.078148^*^ [Building main dictionary tab for text: "æ—¶å€™"]
INFO^*^ 2025-05-30 20:08:50.403522^*^ [Webview: book.js]
INFO^*^ 2025-05-30 20:08:50.445745^*^ [Webview: loadBook]
INFO^*^ 2025-05-30 20:08:50.471341^*^ [Webview: navigator.userAgent Mozilla/5.0 (Linux; Android 15; sdk_gphone64_arm64 Build/AE3A.240806.036; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/124.0.6367.219 Mobile Safari/537.36]
INFO^*^ 2025-05-30 20:08:50.484943^*^ [Server: Request for book: /data/user/0/io.github.dassodev.dasso_reader/app_flutter/file/çˆ±ä¸½ä¸æ¢¦æ¸¸ä»™å¢ƒ-1748606803702.epub]
WARNING^*^ 2025-05-30 20:08:50.534957^*^ [Webview: This page contains the following errors:error on line 23 at column 25: Opening and ending tag mismatch: br line 19 and p Below is a rendering of the page up to the first error.]
SEVERE^*^ 2025-05-30 20:08:51.099098^*^ [Webview: ResizeObserver loop completed with undelivered notifications.]
INFO^*^ 2025-05-30 20:08:56.346026^*^ [Building main dictionary tab for text: "æ—¶å€™"]
INFO^*^ 2025-05-30 20:08:57.843031^*^ [Building main dictionary tab for text: "æ—¶å€™"]
INFO^*^ 2025-05-30 20:08:57.859609^*^ [Building main dictionary tab for text: "æ—¶å€™"]
INFO^*^ 2025-05-30 20:08:57.875627^*^ [Building main dictionary tab for text: "æ—¶å€™"]
INFO^*^ 2025-05-30 20:08:57.891935^*^ [Building main dictionary tab for text: "æ—¶å€™"]
INFO^*^ 2025-05-30 20:08:57.908318^*^ [Building main dictionary tab for text: "æ—¶å€™"]
INFO^*^ 2025-05-30 20:08:57.925425^*^ [Building main dictionary tab for text: "æ—¶å€™"]
INFO^*^ 2025-05-30 20:08:57.944019^*^ [Building main dictionary tab for text: "æ—¶å€™"]
INFO^*^ 2025-05-30 20:08:57.958532^*^ [Building main dictionary tab for text: "æ—¶å€™"]
INFO^*^ 2025-05-30 20:08:57.974942^*^ [Building main dictionary tab for text: "æ—¶å€™"]
INFO^*^ 2025-05-30 20:08:57.991676^*^ [Building main dictionary tab for text: "æ—¶å€™"]
INFO^*^ 2025-05-30 20:08:58.009165^*^ [Building main dictionary tab for text: "æ—¶å€™"]
INFO^*^ 2025-05-30 20:08:58.025471^*^ [Building main dictionary tab for text: "æ—¶å€™"]
INFO^*^ 2025-05-30 20:08:58.959299^*^ [Building main dictionary tab for text: "æ—¶å€™"]
INFO^*^ 2025-05-30 20:08:58.975987^*^ [Building main dictionary tab for text: "æ—¶å€™"]
INFO^*^ 2025-05-30 20:08:58.992938^*^ [Building main dictionary tab for text: "æ—¶å€™"]
INFO^*^ 2025-05-30 20:08:59.008339^*^ [Building main dictionary tab for text: "æ—¶å€™"]
INFO^*^ 2025-05-30 20:08:59.026161^*^ [Building main dictionary tab for text: "æ—¶å€™"]
INFO^*^ 2025-05-30 20:08:59.042847^*^ [Building main dictionary tab for text: "æ—¶å€™"]
INFO^*^ 2025-05-30 20:08:59.061829^*^ [Building main dictionary tab for text: "æ—¶å€™"]
INFO^*^ 2025-05-30 20:08:59.076050^*^ [Building main dictionary tab for text: "æ—¶å€™"]
INFO^*^ 2025-05-30 20:08:59.092868^*^ [Building main dictionary tab for text: "æ—¶å€™"]
INFO^*^ 2025-05-30 20:08:59.108107^*^ [Building main dictionary tab for text: "æ—¶å€™"]
INFO^*^ 2025-05-30 20:08:59.124555^*^ [Building main dictionary tab for text: "æ—¶å€™"]
INFO^*^ 2025-05-30 20:08:59.141962^*^ [Building main dictionary tab for text: "æ—¶å€™"]
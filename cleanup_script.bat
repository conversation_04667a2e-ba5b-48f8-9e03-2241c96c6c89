@echo off
REM 🧹 Flutter Project Safe Cleanup Script (Windows)
REM This script performs safe cleanup operations on your Flutter project
REM Based on comprehensive analysis findings

echo 🧹 Starting Flutter Project Cleanup...
echo 📁 Working directory: %CD%

REM Verify we're in a Flutter project
if not exist "pubspec.yaml" (
    echo ❌ Error: pubspec.yaml not found. Please run this script from your Flutter project root.
    exit /b 1
)

REM Create backup directory
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "BACKUP_DIR=backup_%YYYY%%MM%%DD%_%HH%%Min%%Sec%"

echo 📦 Creating backup in %BACKUP_DIR%...
mkdir "%BACKUP_DIR%"

echo.
echo 🔍 Phase 1: Analysis and Verification
echo ==================================

REM Run flutter analyze to get current state
echo Running flutter analyze...
flutter analyze > "%BACKUP_DIR%\flutter_analyze_before.txt" 2>&1

REM Run dependency analysis
echo Checking dependencies...
dart run assets_cleaner scanlib > "%BACKUP_DIR%\dependency_analysis.txt" 2>&1

REM Run asset analysis  
echo Checking assets...
dart run assets_cleaner unused > "%BACKUP_DIR%\asset_analysis.txt" 2>&1

echo.
echo 🧹 Phase 2: Safe Cleanup Operations
echo ==================================

REM Format all Dart files
echo 📝 Formatting Dart files...
dart format lib\

echo 🗑️  Files that need manual import cleanup:
echo   - lib\page\home_page\hsk_page\hsk_review_screen.dart
echo   - lib\page\home_page\hsk_page\hsk_set_details_screen.dart
echo   - lib\page\home_page\hsk_page\hsk_time_over_screen.dart
echo   - lib\page\home_page\settings_page.dart
echo   - lib\page\home_page\vocabulary_page.dart
echo   - lib\page\settings_page\narrate.dart
echo   - lib\page\settings_page\storege.dart
echo   - lib\service\dictionary\dictionary_service.dart
echo   - lib\utils\state_management\app_state_manager.dart
echo   - lib\widgets\ai_chat_stream.dart
echo   - lib\widgets\reading_page\search_page.dart
echo   - lib\widgets\reading_page\style_widget.dart
echo   - lib\widgets\reading_page\toc_widget.dart
echo   - lib\widgets\settings\settings_section.dart

echo.
echo ⚠️  MANUAL ACTION REQUIRED:
echo Please use your IDE's 'Organize Imports' feature on the files listed above.
echo This is safer than automated text replacement.

echo.
echo 🧪 Phase 3: Verification
echo ======================

REM Run tests if they exist
if exist "test" (
    echo 🧪 Running tests...
    flutter test
) else (
    echo ℹ️  No tests found to run
)

REM Run analysis again
echo 🔍 Running final analysis...
flutter analyze > "%BACKUP_DIR%\flutter_analyze_after.txt" 2>&1

echo.
echo 📊 Cleanup Summary
echo ==================

if exist "%BACKUP_DIR%\dependency_analysis.txt" (
    echo.
    echo 📦 Dependency Status:
    type "%BACKUP_DIR%\dependency_analysis.txt"
)

if exist "%BACKUP_DIR%\asset_analysis.txt" (
    echo.
    echo 🖼️  Asset Status:
    type "%BACKUP_DIR%\asset_analysis.txt"
)

echo.
echo ✅ Cleanup completed!
echo 📁 Backup created in: %BACKUP_DIR%
echo.
echo 🎯 Next Steps:
echo 1. Use your IDE to organize imports in the listed files
echo 2. Remove unused variables manually (see analysis report)
echo 3. Test your app thoroughly
echo 4. Run 'flutter analyze' to verify improvements
echo.
echo 📄 For detailed analysis, see: FLUTTER_PROJECT_CLEANUP_ANALYSIS.md

pause

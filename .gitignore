# Miscellaneous
*.class
*.log
*.pyc
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/
build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# riverpod generated files
lib/gen/
release.sh
*.g.dart
*.freezed.dart

/.vscode/

# Python virtual environments (for development scripts)
venv/
env/
.env/
.venv/
__pycache__/

# Development artifacts and orphaned files
chineseinflow/
gradle/

# Backup directories (created by cleanup scripts)
backup_*/

# macOS specific
.DS_Store
.AppleDouble
.LSOverride

# Windows specific
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux specific
*~

# Temporary files
*.tmp
*.temp
*.bak
*.swp
*.swo

# Coverage reports
coverage/
*.lcov

# Test artifacts
test/coverage/
test_driver/screenshots/

# IDE specific files
*.code-workspace
.history/

# Flutter web build
/web/build/

# Flutter desktop builds
/windows/flutter/
/linux/flutter/
/macos/Flutter/

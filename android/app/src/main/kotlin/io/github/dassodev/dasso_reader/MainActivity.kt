package io.github.dassodev.dasso_reader

import android.app.ActivityManager
import android.content.Context
import android.os.Debug
import com.ryanheise.audioservice.AudioServiceActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import java.io.File
import java.io.RandomAccessFile

class MainActivity: AudioServiceActivity() {
    private val CPU_CHANNEL = "dasso_reader/cpu"
    private val MEMORY_CHANNEL = "dasso_reader/memory"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // Setup CPU monitoring channel
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CPU_CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "getCPUInfo" -> {
                    try {
                        val cpuUsage = getCPUUsage()
                        val cpuInfo = mapOf(
                            "cpuUsage" to cpuUsage,
                            "timestamp" to System.currentTimeMillis()
                        )
                        result.success(cpuInfo)
                    } catch (e: Exception) {
                        result.error("CPU_ERROR", "Failed to get CPU info: ${e.message}", null)
                    }
                }
                else -> result.notImplemented()
            }
        }

        // Setup Memory monitoring channel
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, MEMORY_CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "getMemoryInfo" -> {
                    try {
                        val memoryInfo = getMemoryInfo()
                        result.success(memoryInfo)
                    } catch (e: Exception) {
                        result.error("MEMORY_ERROR", "Failed to get memory info: ${e.message}", null)
                    }
                }
                else -> result.notImplemented()
            }
        }
    }

    private fun getCPUUsage(): Double {
        return try {
            val statFile = File("/proc/stat")
            if (!statFile.exists()) return 0.0

            val reader = RandomAccessFile(statFile, "r")
            val load = reader.readLine()
            reader.close()

            val toks = load.split(" +".toRegex()).toTypedArray()

            val idle1 = toks[4].toLong()
            val cpu1 = toks[1].toLong() + toks[2].toLong() + toks[3].toLong() +
                      toks[5].toLong() + toks[6].toLong() + toks[7].toLong() + toks[8].toLong()

            Thread.sleep(100) // Short delay for measurement

            val reader2 = RandomAccessFile(statFile, "r")
            val load2 = reader2.readLine()
            reader2.close()

            val toks2 = load2.split(" +".toRegex()).toTypedArray()

            val idle2 = toks2[4].toLong()
            val cpu2 = toks2[1].toLong() + toks2[2].toLong() + toks2[3].toLong() +
                       toks2[5].toLong() + toks2[6].toLong() + toks2[7].toLong() + toks2[8].toLong()

            val cpuUsage = (cpu2 - cpu1).toDouble() / ((cpu2 + idle2) - (cpu1 + idle1)) * 100.0
            cpuUsage.coerceIn(0.0, 100.0)
        } catch (e: Exception) {
            // Fallback estimation
            5.0
        }
    }

    private fun getMemoryInfo(): Map<String, Any> {
        return try {
            val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val memoryInfo = ActivityManager.MemoryInfo()
            activityManager.getMemoryInfo(memoryInfo)

            // Get app-specific memory usage
            val runtime = Runtime.getRuntime()
            val usedMemory = runtime.totalMemory() - runtime.freeMemory()
            val usedMemoryMB = usedMemory / (1024 * 1024)

            // Get system memory info
            val totalMemoryMB = memoryInfo.totalMem / (1024 * 1024)
            val availableMemoryMB = memoryInfo.availMem / (1024 * 1024)

            // Get native heap info
            val nativeHeapSize = Debug.getNativeHeapSize() / (1024 * 1024)
            val nativeHeapUsed = Debug.getNativeHeapAllocatedSize() / (1024 * 1024)

            mapOf(
                "usedMemoryMB" to usedMemoryMB,
                "totalMemoryMB" to totalMemoryMB,
                "availableMemoryMB" to availableMemoryMB,
                "nativeHeapSizeMB" to nativeHeapSize,
                "nativeHeapUsedMB" to nativeHeapUsed,
                "memoryPressure" to memoryInfo.lowMemory,
                "timestamp" to System.currentTimeMillis()
            )
        } catch (e: Exception) {
            // Fallback values
            mapOf(
                "usedMemoryMB" to 50,
                "totalMemoryMB" to 300,
                "availableMemoryMB" to 250,
                "nativeHeapSizeMB" to 20,
                "nativeHeapUsedMB" to 10,
                "memoryPressure" to false,
                "timestamp" to System.currentTimeMillis()
            )
        }
    }
}
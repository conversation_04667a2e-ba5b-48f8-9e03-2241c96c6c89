#!/bin/bash

# 🧹 Flutter Project Safe Cleanup Script
# This script performs safe cleanup operations on your Flutter project
# Based on comprehensive analysis findings

set -e  # Exit on any error

echo "🧹 Starting Flutter Project Cleanup..."
echo "📁 Working directory: $(pwd)"

# Verify we're in a Flutter project
if [ ! -f "pubspec.yaml" ]; then
    echo "❌ Error: pubspec.yaml not found. Please run this script from your Flutter project root."
    exit 1
fi

# Create backup directory
BACKUP_DIR="backup_$(date +%Y%m%d_%H%M%S)"
echo "📦 Creating backup in $BACKUP_DIR..."
mkdir -p "$BACKUP_DIR"

# Function to backup a file before modifying
backup_file() {
    local file="$1"
    if [ -f "$file" ]; then
        cp "$file" "$BACKUP_DIR/$(basename "$file").backup"
        echo "✅ Backed up: $file"
    fi
}

echo ""
echo "🔍 Phase 1: Analysis and Verification"
echo "=================================="

# Run flutter analyze to get current state
echo "Running flutter analyze..."
flutter analyze > "$BACKUP_DIR/flutter_analyze_before.txt" 2>&1 || true

# Run dependency analysis
echo "Checking dependencies..."
dart run assets_cleaner scanlib > "$BACKUP_DIR/dependency_analysis.txt" 2>&1 || true

# Run asset analysis  
echo "Checking assets..."
dart run assets_cleaner unused > "$BACKUP_DIR/asset_analysis.txt" 2>&1 || true

echo ""
echo "🧹 Phase 2: Safe Cleanup Operations"
echo "=================================="

# Format all Dart files
echo "📝 Formatting Dart files..."
dart format lib/ --set-exit-if-changed || true

# List of files with unused imports to clean
declare -a FILES_TO_CLEAN=(
    "lib/page/home_page/hsk_page/hsk_review_screen.dart"
    "lib/page/home_page/hsk_page/hsk_set_details_screen.dart" 
    "lib/page/home_page/hsk_page/hsk_time_over_screen.dart"
    "lib/page/home_page/settings_page.dart"
    "lib/page/home_page/vocabulary_page.dart"
    "lib/page/settings_page/narrate.dart"
    "lib/page/settings_page/storege.dart"
    "lib/service/dictionary/dictionary_service.dart"
    "lib/utils/state_management/app_state_manager.dart"
    "lib/widgets/ai_chat_stream.dart"
    "lib/widgets/reading_page/search_page.dart"
    "lib/widgets/reading_page/style_widget.dart"
    "lib/widgets/reading_page/toc_widget.dart"
    "lib/widgets/settings/settings_section.dart"
)

echo "🗑️  Removing unused imports from ${#FILES_TO_CLEAN[@]} files..."

# Backup files before cleaning
for file in "${FILES_TO_CLEAN[@]}"; do
    if [ -f "$file" ]; then
        backup_file "$file"
    fi
done

# Note: Actual import removal would require more sophisticated parsing
# For safety, we'll just report what should be cleaned
echo "📋 Files that need manual import cleanup:"
for file in "${FILES_TO_CLEAN[@]}"; do
    if [ -f "$file" ]; then
        echo "  - $file"
    fi
done

echo ""
echo "⚠️  MANUAL ACTION REQUIRED:"
echo "Please use your IDE's 'Organize Imports' feature on the files listed above."
echo "This is safer than automated text replacement."

echo ""
echo "🧪 Phase 3: Verification"
echo "======================"

# Run tests if they exist
if [ -d "test" ] && [ "$(ls -A test)" ]; then
    echo "🧪 Running tests..."
    flutter test || echo "⚠️  Some tests failed - please review"
else
    echo "ℹ️  No tests found to run"
fi

# Run analysis again
echo "🔍 Running final analysis..."
flutter analyze > "$BACKUP_DIR/flutter_analyze_after.txt" 2>&1 || true

# Compare before and after
echo ""
echo "📊 Cleanup Summary"
echo "=================="

BEFORE_ISSUES=$(grep -c "issues found" "$BACKUP_DIR/flutter_analyze_before.txt" 2>/dev/null || echo "0")
AFTER_ISSUES=$(grep -c "issues found" "$BACKUP_DIR/flutter_analyze_after.txt" 2>/dev/null || echo "0")

echo "Analysis issues before: $BEFORE_ISSUES"
echo "Analysis issues after: $AFTER_ISSUES"

if [ -f "$BACKUP_DIR/dependency_analysis.txt" ]; then
    echo ""
    echo "📦 Dependency Status:"
    cat "$BACKUP_DIR/dependency_analysis.txt"
fi

if [ -f "$BACKUP_DIR/asset_analysis.txt" ]; then
    echo ""
    echo "🖼️  Asset Status:"
    cat "$BACKUP_DIR/asset_analysis.txt"
fi

echo ""
echo "✅ Cleanup completed!"
echo "📁 Backup created in: $BACKUP_DIR"
echo ""
echo "🎯 Next Steps:"
echo "1. Use your IDE to organize imports in the listed files"
echo "2. Remove unused variables manually (see analysis report)"
echo "3. Test your app thoroughly"
echo "4. Run 'flutter analyze' to verify improvements"
echo ""
echo "📄 For detailed analysis, see: FLUTTER_PROJECT_CLEANUP_ANALYSIS.md"

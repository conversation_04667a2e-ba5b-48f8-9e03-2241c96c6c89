#!/bin/bash

# 🧹 Safe Flutter Project Dead Code Cleanup Executor
# This script performs safe, incremental cleanup with full backup and testing

set -e  # Exit on any error

echo "🧹 Starting Safe Flutter Project Cleanup..."
echo "📁 Working directory: $(pwd)"

# Verify we're in a Flutter project
if [ ! -f "pubspec.yaml" ]; then
    echo "❌ Error: pubspec.yaml not found. Please run this script from your Flutter project root."
    exit 1
fi

# Create timestamped backup directory
BACKUP_DIR="cleanup_backup_$(date +%Y%m%d_%H%M%S)"
echo "📦 Creating comprehensive backup in $BACKUP_DIR..."
mkdir -p "$BACKUP_DIR"

# Backup entire lib directory and critical files
cp -r lib/ "$BACKUP_DIR/"
cp pubspec.yaml "$BACKUP_DIR/"
cp pubspec.lock "$BACKUP_DIR/"

echo "✅ Backup created successfully"

# Function to run tests and verify build
verify_project_health() {
    echo "🔍 Verifying project health..."
    
    # Clean and get dependencies
    flutter clean > /dev/null 2>&1
    flutter pub get > /dev/null 2>&1
    
    # Run flutter analyze
    echo "  📊 Running flutter analyze..."
    if flutter analyze --no-fatal-infos > "$BACKUP_DIR/analyze_$(date +%H%M%S).txt" 2>&1; then
        echo "  ✅ Analysis completed"
    else
        echo "  ⚠️ Analysis found issues (logged)"
    fi
    
    # Test build
    echo "  🔨 Testing debug build..."
    if flutter build apk --debug > /dev/null 2>&1; then
        echo "  ✅ Build successful"
        return 0
    else
        echo "  ❌ Build failed"
        return 1
    fi
}

# Initial health check
echo ""
echo "🔍 Phase 0: Initial Health Check"
echo "================================"
verify_project_health

echo ""
echo "🧹 Phase 1: Safe Import Cleanup"
echo "==============================="

# List of files with unused imports (from analysis)
declare -a IMPORT_CLEANUP_FILES=(
    "lib/page/home_page/hsk_page/hsk_learn_screen.dart"
    "lib/page/settings_page/narrate.dart"
    "lib/page/settings_page/storege.dart"
    "lib/utils/state_management/app_state_manager.dart"
    "lib/widgets/reading_page/search_page.dart"
    "lib/widgets/reading_page/style_widget.dart"
    "lib/widgets/reading_page/toc_widget.dart"
    "lib/widgets/settings/settings_section.dart"
)

echo "📝 Formatting and organizing imports..."
dart format lib/ --set-exit-if-changed || true

echo "🗑️ Files that need manual import cleanup:"
for file in "${IMPORT_CLEANUP_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "  - $file"
    fi
done

echo ""
echo "🔍 Phase 1 Verification"
echo "======================"
if verify_project_health; then
    echo "✅ Phase 1 completed successfully"
else
    echo "❌ Phase 1 failed - check logs"
    exit 1
fi

echo ""
echo "📊 Cleanup Summary"
echo "=================="

# Count issues before and after
INITIAL_ISSUES=$(grep -o "[0-9]\+ issues found" "$BACKUP_DIR/analyze_"*.txt | head -1 | grep -o "[0-9]\+" || echo "0")
CURRENT_ISSUES=$(flutter analyze --no-fatal-infos 2>&1 | grep -o "[0-9]\+ issues found" | grep -o "[0-9]\+" || echo "0")

echo "Analysis issues before: $INITIAL_ISSUES"
echo "Analysis issues after: $CURRENT_ISSUES"

if [ "$CURRENT_ISSUES" -lt "$INITIAL_ISSUES" ]; then
    IMPROVEMENT=$((INITIAL_ISSUES - CURRENT_ISSUES))
    echo "✅ Improvement: $IMPROVEMENT issues resolved"
else
    echo "ℹ️ No change in issue count (formatting/organization completed)"
fi

echo ""
echo "📁 Backup location: $BACKUP_DIR"
echo ""
echo "🎯 Next Manual Steps:"
echo "1. Use your IDE to organize imports in the listed files"
echo "2. Remove unused variables manually (see COMPREHENSIVE_CLEANUP_SCRIPT.md)"
echo "3. Test your app thoroughly"
echo "4. Run 'flutter analyze' to verify improvements"
echo ""
echo "📄 For detailed cleanup instructions, see: COMPREHENSIVE_CLEANUP_SCRIPT.md"

echo ""
echo "✅ Safe cleanup phase completed!"
echo "🔒 Your original code is safely backed up in: $BACKUP_DIR"

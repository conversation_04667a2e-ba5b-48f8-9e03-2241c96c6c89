# Theme Adaptation Implementation Guide
## Step-by-Step Implementation Plan

### 🎯 Overview
This guide provides detailed, step-by-step instructions for adapting anx-reader's theme system to dasso-reader, including adding E-ink Mode and removing theme templates.

---

## 📋 Pre-Implementation Checklist

### ✅ **Before Starting:**
- [ ] Backup current dasso-reader project
- [ ] Ensure all existing functionality is working
- [ ] Review theme_system_analysis_and_adaptation_plan.md
- [ ] Test current theme template functionality (for comparison)

### ✅ **Required Files Analysis:**
- [ ] Confirm location of all files to be modified
- [ ] Identify dependencies between theme-related components
- [ ] Review current user preferences storage

---

## 🚀 Phase 1: Add E-ink Mode Support

### Step 1.1: Add E-ink Mode to SharedPreferences

**File:** `dasso-reader/lib/config/shared_preference_provider.dart`

**Action:** Add E-ink mode properties after the `trueDarkMode` implementation (around line 317)

```dart
// Add after trueDarkMode getter/setter (around line 317)
set eInkMode(bool status) {
  prefs.setBool('eInkMode', status);
  notifyListeners();
}

bool get eInkMode {
  return prefs.getBool('eInkMode') ?? false;
}
```

### Step 1.2: Add E-ink Mode Toggle to Settings UI

**File:** `dasso-reader/lib/page/settings_page/appearance.dart`

**Action:** Add E-ink mode toggle after OLED Dark Mode toggle (around line 82)

```dart
// Add after OLED Dark Mode SettingsTile.switchTile (around line 82)
SettingsTile.switchTile(
  title: Text(L10n.of(context).e_ink_mode),
  leading: const Icon(Icons.contrast),
  initialValue: Prefs().eInkMode,
  onToggle: (bool value) {
    setState(() {
      if (value) {
        // Force light mode when E-ink is enabled
        Prefs().saveThemeModeToPrefs('light');
      }
      Prefs().eInkMode = value;
    });
  },
),
```

### Step 1.3: Add E-ink Mode Localization

**File:** `dasso-reader/lib/l10n/app_en.arb`

**Action:** Add E-ink mode translation key

```json
"e_ink_mode": "E-ink Mode"
```

**File:** `dasso-reader/lib/l10n/app_zh.arb`

**Action:** Add Chinese translation

```json
"e_ink_mode": "墨水屏模式"
```

### Step 1.4: Implement E-ink Mode in Main Theme Builder

**File:** `dasso-reader/lib/main.dart`

**Action:** Modify theme building logic to support E-ink mode

**Location:** In the `_buildLightTheme` method (around line 317)

```dart
/// Build light theme with E-ink mode and template support
ThemeData _buildLightTheme(BuildContext context, Prefs prefsNotifier) {
  // Check for E-ink mode first
  if (prefsNotifier.eInkMode) {
    final eInkColorScheme = const ColorScheme.light(
      primary: Colors.black,
      onPrimary: Colors.white,
      secondary: Colors.grey,
      onSecondary: Colors.white,
      surface: Colors.white,
      onSurface: Colors.black,
      primaryContainer: Colors.grey,
      onPrimaryContainer: Colors.black,
    );
    
    return FlexThemeData.light(
      useMaterial3: true,
      swapLegacyOnMaterial3: true,
      colorScheme: eInkColorScheme,
    ).copyWith(
      textTheme: AppTypography.getTextTheme(context),
    ).useSystemChineseFont(Brightness.light);
  }
  
  // Continue with existing template logic...
  final template = prefsNotifier.currentThemeTemplate;
  // ... rest of existing implementation
}
```

---

## 🗑️ Phase 2: Remove Theme Templates

### Step 2.1: Remove Template Section from Settings UI

**File:** `dasso-reader/lib/page/settings_page/appearance.dart`

**Action:** Remove the entire template section (lines 50-57)

```dart
// REMOVE THIS ENTIRE SECTION:
SettingsSection(
  title: Text(L10n.of(context).settings_appearance_templates),
  tiles: [
    const CustomSettingsTile(
      child: ThemeTemplateSelector(),
    ),
  ],
),
```

**Action:** Remove template-related imports

```dart
// REMOVE this import:
import 'package:dasso_reader/widgets/settings/theme_template_selector.dart';
```

### Step 2.2: Remove Template Logic from Main Theme Builder

**File:** `dasso-reader/lib/main.dart`

**Action:** Simplify `_buildLightTheme` method to remove template logic

```dart
/// Build light theme with E-ink mode support (simplified)
ThemeData _buildLightTheme(BuildContext context, Prefs prefsNotifier) {
  // Check for E-ink mode first
  if (prefsNotifier.eInkMode) {
    // E-ink mode implementation (from Step 1.4)
    // ... E-ink color scheme code
  }
  
  // Use simple color scheme from theme color
  final colorScheme = ColorScheme.fromSeed(
    seedColor: prefsNotifier.themeColor,
    brightness: Brightness.light,
  );

  return FlexThemeData.light(
    useMaterial3: true,
    swapLegacyOnMaterial3: true,
    colorScheme: colorScheme,
  ).copyWith(
    textTheme: AppTypography.getTextTheme(context),
  ).useSystemChineseFont(Brightness.light);
}
```

**Action:** Simplify `_buildDarkTheme` method similarly

```dart
/// Build dark theme (simplified)
ThemeData _buildDarkTheme(BuildContext context, Prefs prefsNotifier) {
  // E-ink mode uses light theme, so skip for dark theme
  
  final colorScheme = ColorScheme.fromSeed(
    seedColor: prefsNotifier.themeColor,
    brightness: Brightness.dark,
  );

  return FlexThemeData.dark(
    useMaterial3: true,
    swapLegacyOnMaterial3: true,
    darkIsTrueBlack: prefsNotifier.trueDarkMode,
    colorScheme: colorScheme,
  ).copyWith(
    textTheme: AppTypography.getTextTheme(context),
  ).useSystemChineseFont(Brightness.dark);
}
```

### Step 2.3: Remove Template Properties from SharedPreferences

**File:** `dasso-reader/lib/config/shared_preference_provider.dart`

**Action:** Remove template-related properties (lines 542-572)

```dart
// REMOVE ALL THESE METHODS:
// - selectedThemeTemplate setter/getter
// - currentThemeTemplate getter
// - isThemeTemplateActive getter
// - clearThemeTemplate method
```

**Action:** Remove template import

```dart
// REMOVE this import:
import 'package:dasso_reader/models/theme_template.dart';
```

### Step 2.4: Remove Template-Related Files

**Files to Delete:**
1. `dasso-reader/lib/models/theme_template.dart`
2. `dasso-reader/lib/widgets/settings/theme_template_selector.dart`

### Step 2.5: Remove Template Localization Keys

**File:** `dasso-reader/lib/l10n/app_en.arb`

**Action:** Remove template-related keys

```json
// REMOVE these keys:
"settings_appearance_templates"
"settings_appearance_templates_description"
"theme_template_relaxed_red"
"theme_template_relaxed_red_description"
"theme_template_clean_white"
"theme_template_clean_white_description"
"theme_template_oled_dark"
"theme_template_oled_dark_description"
"theme_template_soft_blue"
"theme_template_soft_blue_description"
```

**File:** `dasso-reader/lib/l10n/app_zh.arb`

**Action:** Remove corresponding Chinese keys

---

## 🧪 Phase 3: Testing & Validation

### Step 3.1: Functional Testing

**Test Cases:**
- [ ] E-ink mode toggle works correctly
- [ ] E-ink mode forces light theme
- [ ] OLED dark mode still functions
- [ ] Theme color picker works
- [ ] Theme mode switching works
- [ ] Settings UI displays correctly
- [ ] No template-related UI elements remain

### Step 3.2: Visual Testing

**Verify:**
- [ ] E-ink mode produces black/white/grey color scheme
- [ ] Status bar adapts correctly in E-ink mode
- [ ] All UI elements are visible in E-ink mode
- [ ] Theme transitions are smooth
- [ ] No visual artifacts from removed templates

### Step 3.3: Persistence Testing

**Verify:**
- [ ] E-ink mode setting persists across app restarts
- [ ] Theme preferences are maintained
- [ ] No crashes when loading saved preferences
- [ ] Migration from template preferences works gracefully

---

## ⚠️ Troubleshooting Guide

### Common Issues:

1. **E-ink mode not applying:**
   - Check if `eInkMode` property is properly saved
   - Verify theme building logic order

2. **Settings UI crashes:**
   - Ensure all template imports are removed
   - Check localization keys exist

3. **Theme not persisting:**
   - Verify SharedPreferences implementation
   - Check notifyListeners() calls

4. **Status bar issues:**
   - Keep existing StatusBarDesign system
   - Test with different theme combinations

---

## ✅ Completion Checklist

### Final Verification:
- [ ] All template-related code removed
- [ ] E-ink mode fully functional
- [ ] No breaking changes to existing features
- [ ] All tests pass
- [ ] UI matches anx-reader simplicity
- [ ] System app bar functionality preserved
- [ ] Localization complete
- [ ] Documentation updated

---

*This implementation guide ensures a systematic approach to theme system adaptation while maintaining code quality and functionality.*

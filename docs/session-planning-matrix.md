# DassoShu Reader - Session Planning Matrix

**Date**: June 26, 2025  
**Phase**: 1 - Session Planning & Execution Strategy  
**Total Issues**: 173 → Target: <50 (70% reduction)  
**Total Sessions**: 10 (4 Automated + 6 Manual)  
**Estimated Duration**: 7 hours

## 📋 **Session Overview Matrix**

| Session | Type | Duration | Issues | Risk | Validation |
|---------|------|----------|--------|------|------------|
| **A1** | Auto | 15-20 min | 20-25 | Very Low | Build Test |
| **A2** | Auto | 20-30 min | 8-10 | Low | Compile + Test |
| **A3** | Auto | 15-20 min | 3-5 | Low | Log Verification |
| **A4** | Auto | 25-30 min | 15-20 | Very Low | Build Test |
| **M1** | Manual | 45-60 min | 15-20 | Medium | UI Testing |
| **M2** | Manual | 45-60 min | 25-30 | High | Settings Testing |
| **M3** | Manual | 45-60 min | 20-25 | High | State Testing |
| **M4** | Manual | 45-60 min | 15-20 | Medium | Service Testing |
| **M5** | Manual | 45-60 min | 15-20 | Medium | Widget Testing |
| **M6** | Manual | 30-45 min | 5-8 | Medium | API Testing |

---

## 🤖 **AUTOMATED SESSIONS (Phase 2)**

### **Session A1: Automated Formatting & Trailing Commas**
**Duration**: 15-20 minutes  
**Target Issues**: 20-25 formatting issues  
**Risk Level**: Very Low  
**Automation Level**: 100%

#### **📋 Analysis Phase (5 min)**
```bash
# Identify all trailing comma issues
flutter analyze --no-fatal-infos | grep "require_trailing_commas"
# Expected: ~20-25 issues across multiple files
```

#### **🛠️ Implementation Phase (10 min)**
```bash
# Automated formatting
dart format lib/ --set-exit-if-changed

# Verify changes
git diff --name-only
# Expected files: search_result_model.dart, bookshelf_page.dart, etc.
```

#### **✅ Validation Phase (5 min)**
```bash
# Build validation
flutter clean && flutter pub get
flutter build apk --debug --no-tree-shake-icons
# Expected: Successful build, reduced formatting issues
```

#### **Success Criteria**
- [ ] All `require_trailing_commas` issues resolved
- [ ] No new compilation errors introduced
- [ ] Build completes successfully
- [ ] Git diff shows only formatting changes

---

### **Session A2: Constant Naming Convention Updates**
**Duration**: 20-30 minutes  
**Target Issues**: 8-10 constant naming issues  
**Risk Level**: Low  
**Automation Level**: 90%

#### **📋 Analysis Phase (5 min)**
```bash
# Identify constant naming issues
flutter analyze lib/dao/database.dart | grep "constant_identifier_names"
flutter analyze lib/models/java_metrics_learn.dart | grep "non_constant_identifier_names"
# Map all references to constants
grep -r "CREATE_BOOK_SQL\|PRIMARY_THEME_1\|FINISHED" lib/ --include="*.dart"
```

#### **🛠️ Implementation Phase (20 min)**
```bash
# Database constants (lib/dao/database.dart)
sed -i 's/CREATE_BOOK_SQL/createBookSql/g' lib/dao/database.dart
sed -i 's/CREATE_THEME_SQL/createThemeSql/g' lib/dao/database.dart
sed -i 's/CREATE_STYLE_SQL/createStyleSql/g' lib/dao/database.dart
sed -i 's/PRIMARY_THEME_1/primaryTheme1/g' lib/dao/database.dart
sed -i 's/PRIMARY_THEME_2/primaryTheme2/g' lib/dao/database.dart
sed -i 's/CREATE_NOTE_SQL/createNoteSql/g' lib/dao/database.dart
sed -i 's/CREATE_READING_TIME_SQL/createReadingTimeSql/g' lib/dao/database.dart
sed -i 's/CREATE_READING_TIME_INDEXES/createReadingTimeIndexes/g' lib/dao/database.dart

# Model constants (lib/models/java_metrics_learn.dart)
sed -i 's/FINISHED/finished/g' lib/models/java_metrics_learn.dart

# Update all references across codebase
find lib/ -name "*.dart" -exec sed -i 's/CREATE_BOOK_SQL/createBookSql/g' {} \;
find lib/ -name "*.dart" -exec sed -i 's/PRIMARY_THEME_1/primaryTheme1/g' {} \;
find lib/ -name "*.dart" -exec sed -i 's/FINISHED/finished/g' {} \;
```

#### **✅ Validation Phase (5 min)**
```bash
# Compile validation
flutter analyze lib/dao/database.dart
flutter analyze lib/models/java_metrics_learn.dart

# Reference validation
grep -r "CREATE_BOOK_SQL\|PRIMARY_THEME_1\|FINISHED" lib/ --include="*.dart"
# Expected: No matches (all updated)

# Build validation
flutter build apk --debug
```

#### **Success Criteria**
- [ ] All `constant_identifier_names` issues resolved
- [ ] All references updated consistently
- [ ] No compilation errors
- [ ] Database operations function correctly

---

### **Session A3: Debug Print Statement Cleanup**
**Duration**: 15-20 minutes  
**Target Issues**: 3-5 print statements  
**Risk Level**: Low  
**Automation Level**: 80%

#### **📋 Analysis Phase (5 min)**
```bash
# Identify print statements
flutter analyze --no-fatal-infos | grep "avoid_print"
grep -n "print(" lib/page/home_page/hsk_page/hsk_practice_screen.dart
grep -n "print(" lib/providers/hsk_providers.dart
```

#### **🛠️ Implementation Phase (10 min)**
```bash
# Replace print with AnxLog.info
sed -i 's/print(/AnxLog.info(/g' lib/page/home_page/hsk_page/hsk_practice_screen.dart
sed -i 's/print(/AnxLog.info(/g' lib/providers/hsk_providers.dart

# Verify AnxLog imports exist, add if missing
grep -q "import.*log.*common" lib/page/home_page/hsk_page/hsk_practice_screen.dart || \
  sed -i '1i import '\''package:dasso_reader/utils/log/common.dart'\'';' lib/page/home_page/hsk_page/hsk_practice_screen.dart

grep -q "import.*log.*common" lib/providers/hsk_providers.dart || \
  sed -i '1i import '\''package:dasso_reader/utils/log/common.dart'\'';' lib/providers/hsk_providers.dart
```

#### **✅ Validation Phase (5 min)**
```bash
# Compile validation
flutter analyze lib/page/home_page/hsk_page/hsk_practice_screen.dart
flutter analyze lib/providers/hsk_providers.dart

# Runtime validation
flutter run --debug
# Verify log output appears correctly
```

#### **Success Criteria**
- [ ] All `avoid_print` issues resolved
- [ ] AnxLog imports added correctly
- [ ] Logging functionality preserved
- [ ] No compilation errors

---

### **Session A4: Unused Import & Variable Cleanup**
**Duration**: 25-30 minutes  
**Target Issues**: 15-20 unused items  
**Risk Level**: Very Low  
**Automation Level**: 95%

#### **📋 Analysis Phase (10 min)**
```bash
# Identify all unused elements
flutter analyze --no-fatal-infos | grep "unused_"
# Categories: unused_import, unused_field, unused_element, unused_local_variable
```

#### **🛠️ Implementation Phase (15 min)**
```bash
# Remove unused imports
sed -i '/import.*flutter\/cupertino.dart/d' lib/service/notes/export_notes.dart

# Remove unused fields (use IDE safe delete or manual removal)
# lib/page/home_page/hsk_page/hsk_practice_screen.dart:39:10 • _questionTimerStartValue
# lib/page/home_page/hsk_page/hsk_review_screen.dart:53:10 • _audioErrorMsg
# lib/page/home_page/hsk_page/hsk_review_screen.dart:56:17 • _startTime
# lib/page/home_page/hsk_page/hsk_time_over_screen.dart:37:8 • _initialized

# Remove unused elements
# lib/page/home_page/hsk_page/hsk_review_screen.dart:84:13 • _goodButtonColor
# lib/page/home_page/hsk_page/hsk_review_screen.dart:85:13 • _againButtonColor
# lib/page/home_page/hsk_page/hsk_review_screen.dart:146:8 • _loadSettings

# Remove unused local variables
# Multiple files - use IDE assistance for safe removal
```

#### **✅ Validation Phase (5 min)**
```bash
# Comprehensive validation
flutter clean && flutter pub get
flutter analyze --no-fatal-infos
flutter build apk --debug

# Feature testing
# Test HSK functionality (most affected area)
```

#### **Success Criteria**
- [ ] All `unused_*` issues resolved
- [ ] No functionality impact
- [ ] Clean build success
- [ ] HSK features working correctly

---

## 🔧 **MANUAL SESSIONS (Phase 3)**

### **Session M1: Type Inference - Function Return Types**
**Duration**: 45-60 minutes  
**Target Issues**: 15-20 function return type issues  
**Risk Level**: Medium  
**Files**: Context menu, reading page widgets, bookmark functionality

#### **📋 Analysis Phase (15 min)**
- Examine all `inference_failure_on_function_return_type` issues
- Map function usage patterns and expected return types
- Identify callback functions vs regular methods

#### **🛠️ Implementation Phase (35 min)**
```dart
// Pattern: Add explicit return types to function expressions
// Before: onPressed: () { ... }
// After: onPressed: () => void { ... }

// Before: onChanged: (value) { ... }
// After: onChanged: (bool value) => void { ... }

// Target files:
// - lib/widgets/context_menu/unified_context_menu.dart (5+ issues)
// - lib/widgets/reading_page/* (7+ issues)
// - lib/page/book_notes_page.dart (1+ issues)
```

#### **✅ Validation Phase (10 min)**
- Test context menu functionality
- Test reading page interactions
- Test bookmark operations
- Verify UI responsiveness

#### **Success Criteria**
- [ ] All function return type inference issues resolved
- [ ] Context menu functions correctly
- [ ] Reading page interactions work
- [ ] No UI regressions

---

### **Session M2: Dynamic Type Safety - Settings & Configuration**
**Duration**: 45-60 minutes  
**Target Issues**: 25-30 dynamic type issues  
**Risk Level**: High  
**Files**: AI settings, sync settings, more settings pages

#### **📋 Analysis Phase (15 min)**
- Map all dynamic type assignments in settings
- Understand configuration data structures
- Identify safe casting patterns

#### **🛠️ Implementation Phase (35 min)**
```dart
// Pattern: Safe dynamic casting with defaults
// Before: final url = config['url'];
// After: final url = config['url'] as String? ?? '';

// Before: final headers = config['headers'];
// After: final headers = config['headers'] as Map<String, String>? ?? {};

// Target files:
// - lib/page/settings_page/ai.dart (15+ issues)
// - lib/page/settings_page/more_settings_page.dart (5+ issues)
// - lib/page/settings_page/sync.dart (3+ issues)
```

#### **✅ Validation Phase (10 min)**
- Test all AI service configurations
- Test sync settings functionality
- Test settings persistence
- Verify no data loss

#### **Success Criteria**
- [ ] All dynamic type issues resolved in settings
- [ ] AI configuration works correctly
- [ ] Sync settings function properly
- [ ] Settings persistence maintained

---

### **Session M3: Provider & State Management Type Safety**
**Duration**: 45-60 minutes  
**Target Issues**: 20-25 provider issues  
**Risk Level**: High  
**Files**: book_list, hsk_providers, bookmark providers

#### **📋 Analysis Phase (15 min)**
- Analyze Riverpod provider type issues
- Map state management data flow
- Identify return type mismatches

#### **🛠️ Implementation Phase (35 min)**
```dart
// Pattern: Explicit provider return types
// Before: int get bookCount { return ref.watch(...).fold(0, (sum, group) => sum + group.length); }
// After: int get bookCount { return ref.watch(...).fold<int>(0, (int sum, group) => sum + group.length); }

// Pattern: Safe dynamic casting in providers
// Before: final data = json['data'];
// After: final data = json['data'] as Map<String, dynamic>? ?? {};

// Target files:
// - lib/providers/book_list.dart (8+ issues)
// - lib/providers/hsk_providers.dart (5+ issues)
// - lib/providers/bookmark.dart (2+ issues)
```

#### **✅ Validation Phase (10 min)**
- Test book list functionality
- Test HSK learning features
- Test bookmark operations
- Verify state persistence

#### **Success Criteria**
- [ ] All provider type issues resolved
- [ ] Book list functions correctly
- [ ] HSK features work properly
- [ ] State management stable

---

### **Session M4: Service Layer Type Safety**
**Duration**: 45-60 minutes  
**Target Issues**: 15-20 service issues  
**Risk Level**: Medium  
**Files**: Dictionary services, cache services, book operations

#### **📋 Analysis Phase (15 min)**
- Analyze service layer type issues
- Map data flow between services
- Identify boolean operand problems

#### **🛠️ Implementation Phase (35 min)**
```dart
// Pattern: Boolean operand safety
// Before: if (data && data.isValid)
// After: if ((data ?? false) && (data?.isValid ?? false))

// Pattern: Safe service data handling
// Before: final result = response['data'];
// After: final result = response['data'] as Map<String, dynamic>? ?? {};

// Target files:
// - lib/service/dictionary/online_dictionary_service.dart (15+ issues)
// - lib/service/cache/segmentation_cache_service.dart (2+ issues)
// - lib/service/book.dart (3+ issues)
```

#### **✅ Validation Phase (10 min)**
- Test dictionary lookup functionality
- Test cache operations
- Test book import/export
- Verify service reliability

#### **Success Criteria**
- [ ] All service type issues resolved
- [ ] Dictionary services work correctly
- [ ] Cache operations stable
- [ ] Book operations function properly

---

### **Session M5: Widget & UI Type Inference**
**Duration**: 45-60 minutes  
**Target Issues**: 15-20 widget issues  
**Risk Level**: Medium  
**Files**: AI stream widgets, reading page components, common widgets

#### **📋 Analysis Phase (15 min)**
- Analyze widget type inference issues
- Map UI component interactions
- Identify generic type requirements

#### **🛠️ Implementation Phase (35 min)**
```dart
// Pattern: Generic type arguments for widgets
// Before: showDialog(...)
// After: showDialog<bool>(...)

// Before: PopupMenuItem(...)
// After: PopupMenuItem<String>(...)

// Pattern: Widget callback types
// Before: onTap: () { ... }
// After: onTap: () => void { ... }

// Target files:
// - lib/widgets/ai_stream.dart (2+ issues)
// - lib/widgets/common/dynamic_image_widget.dart (1+ issue)
// - Various reading page widgets (10+ issues)
```

#### **✅ Validation Phase (10 min)**
- Test AI streaming functionality
- Test widget interactions
- Test dialog operations
- Verify UI responsiveness

#### **Success Criteria**
- [ ] All widget type issues resolved
- [ ] AI streaming works correctly
- [ ] Widget interactions function
- [ ] No UI regressions

---

### **Session M6: Deprecated API Migration**
**Duration**: 30-45 minutes  
**Target Issues**: 5-8 deprecated API usages  
**Risk Level**: Medium  
**Files**: Riverpod providers, Material Design components

#### **📋 Analysis Phase (10 min)**
- Identify all deprecated API usage
- Research current API alternatives
- Plan migration strategy

#### **🛠️ Implementation Phase (25 min)**
```dart
// Riverpod migration
// Before: HskLevelsRef, HskCharacterSetsRef, HskReviewSettingsRef
// After: Ref<AsyncValue<List<HskLevel>>>

// Material Design migration
// Before: background, onBackground
// After: surface, onSurface

// Target files:
// - lib/providers/hsk_providers.dart (3+ deprecated refs)
// - lib/widgets/context_menu/unified_context_menu.dart (2+ Material properties)
```

#### **✅ Validation Phase (10 min)**
- Test HSK provider functionality
- Test context menu appearance
- Verify Material Design compliance
- Check for visual regressions

#### **Success Criteria**
- [ ] All deprecated APIs updated
- [ ] HSK providers work correctly
- [ ] Context menu appearance maintained
- [ ] Material Design 3 compliance

---

## 📊 **Session Success Metrics**

### **Overall Project Targets**
- **Issue Reduction**: 173 → <50 (70%+ reduction)
- **Functionality**: 100% preservation
- **Build Success**: Clean compilation
- **Performance**: No regressions
- **Design System**: Full compliance maintained

### **Session-Level Validation**
- **Automated Sessions**: Build success + basic functionality
- **Manual Sessions**: Comprehensive feature testing
- **Risk Mitigation**: Git commits after each session
- **Rollback Strategy**: Immediate revert capability

### **Quality Gates**
- Each session must pass validation before proceeding
- No session should introduce breaking changes
- All changes must maintain design system compliance
- Performance benchmarks must remain stable

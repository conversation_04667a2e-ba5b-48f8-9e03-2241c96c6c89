# Polyphonic Character Enhancement Test Plan

## Overview
This document outlines the testing approach for the enhanced polyphonic character handling in our 3-tab context menu system.

## Enhancements Implemented

### 1. ContextMenuCharacterTab Enhancements
- **Enhanced character loading**: Now uses `lookupChineseAll()` to get all pronunciation variants
- **Polyphonic variant display**: Each pronunciation variant shows as separate entry
- **Visual indicators**: Polyphonic characters show numbered badges (1, 2, 3, etc.)
- **Enhanced selection**: Copy includes character + pinyin for better context
- **Variant tracking**: Each entry tracks its variant index and total variants

### 2. ContextMenuDictionaryTab Enhancements
- **Enhanced lookup logic**: Falls back to individual character lookups for compound words
- **Definition aggregation**: Combines ALL definitions from all pronunciation variants
- **Primary pronunciation display**: Shows the first/highest priority pronunciation in header
- **Unified format**: Maintains original single-character display format with combined definitions

## Test Cases

### Test Case 1: Single Polyphonic Character "没"
**Input**: Select "没"
**Expected Results**:
- **Dict Tab**: Shows single character display with:
  - Character: 没
  - Pronunciation: méi (primary/first pronunciation)
  - Combined definitions: "negative prefix for verbs / have not / not / drowned / to end / to die / to inundate"
- **CHAR Tab**: Shows two separate entries:
  - 没 (méi) with badge "1"
  - 没 (mò) with badge "2"

### Test Case 2: Compound Word with Polyphonic Characters "没有"
**Input**: Select "没有"
**Expected Results**:
- **Dict Tab**: Shows combined definitions from all characters with primary pronunciations
  - Uses first character's primary pronunciation for header
  - Aggregates ALL definitions from both characters (including polyphonic variants)
- **CHAR Tab**: Shows separate entries for:
  - 没 (méi) with badge "1"
  - 没 (mò) with badge "2"
  - 有 (yǒu) - single pronunciation, no badge

### Test Case 3: Non-Polyphonic Character "你"
**Input**: Select "你"
**Expected Results**:
- **Dict Tab**: Shows standard single pronunciation display
- **CHAR Tab**: Shows single entry without variant badge

### Test Case 4: Mixed Text "你没有"
**Input**: Select "你没有"
**Expected Results**:
- **Dict Tab**: Shows character-by-character definitions with polyphonic support
- **CHAR Tab**: Shows:
  - 你 (nǐ) - no badge
  - 没 (méi) with badge "1"
  - 没 (mò) with badge "2"
  - 有 (yǒu) - no badge

## Key Features to Verify

### Visual Design
- ✅ Polyphonic characters show numbered badges with Material 3 colors
- ✅ Compact card-based layout in CHAR tab with proper scrolling
- ✅ Scrollable definition containers in both tabs (max 2-4 lines)
- ✅ Material 3 design consistency with proper elevation and colors
- ✅ Optimized vertical space usage with fixed-height containers

### Functionality
- ✅ Tap-to-copy includes character + pinyin for polyphonic variants
- ✅ All pronunciation variants are displayed
- ✅ HSK levels shown correctly for each variant
- ✅ Loading states work properly
- ✅ Error handling for missing definitions

### Performance
- ✅ No duplicate database queries
- ✅ Efficient loading of character variants
- ✅ Smooth scrolling in both tabs
- ✅ Proper memory management

## Expected User Experience

### For "没有" Selection:
1. **Dict Tab**: User sees unified view with aggregated definitions
   - Character: 没有 (or first character for header)
   - Pronunciation: Primary pronunciation (méi for 没)
   - Combined definitions: All definitions from both characters including polyphonic variants

2. **CHAR Tab**: User sees individual character breakdown
   - 没 (méi) - Badge "1" - HSK 1 - definitions for "have not"
   - 没 (mò) - Badge "2" - HSK 1 - definitions for "drowned"
   - 有 (yǒu) - No badge - HSK 1 - definitions for "have"

3. **Interaction**: Tapping any character variant copies "character (pinyin)" format

## Success Criteria
- ✅ All polyphonic characters show multiple pronunciation variants
- ✅ Visual indicators clearly distinguish variants
- ✅ Definitions are comprehensive and properly grouped
- ✅ User interaction (copy) provides meaningful context
- ✅ Performance remains optimal
- ✅ UI remains clean and intuitive
- ✅ Existing functionality for non-polyphonic characters unchanged

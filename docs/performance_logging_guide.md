# Performance Logging Configuration Guide

## Overview

The Dasso Reader performance system includes comprehensive monitoring and optimization features. To provide a clean development experience, performance logging is **disabled by default** while keeping all monitoring functionality active.

## Quick Configuration

### Default (Recommended for Development)
```dart
// In main.dart - Current default setting
PerformanceLoggingConfig.setMode(PerformanceLoggingMode.quiet);
```
**Result**: Clean terminal output, no performance logs, monitoring still active.

### Enable Performance Debugging
```dart
// In main.dart - Change when you need performance insights
PerformanceLoggingConfig.setMode(PerformanceLoggingMode.verbose);
```
**Result**: Detailed jank detection, optimization logs, performance insights.

## Logging Modes

### 🔇 Quiet Mode (Default)
- **Purpose**: Clean development experience
- **Logs**: No performance logs
- **Monitoring**: ✅ Active (data collection continues)
- **Dashboard**: ✅ Functional
- **Use Case**: Daily development work

### 📝 Minimal Mode
- **Purpose**: Basic awareness
- **Logs**: Initialization messages only
- **Monitoring**: ✅ Active
- **Dashboard**: ✅ Functional
- **Use Case**: Verify system is running

### 📊 Verbose Mode
- **Purpose**: Performance optimization
- **Logs**: Detailed performance metrics
- **Monitoring**: ✅ Active
- **Dashboard**: ✅ Functional
- **Use Case**: Performance debugging and optimization

### 🔍 Debug Mode
- **Purpose**: Deep performance analysis
- **Logs**: Full jank detection with stack traces
- **Monitoring**: ✅ Active
- **Dashboard**: ✅ Functional
- **Use Case**: Investigating specific performance issues

## How to Change Modes

### Method 1: Edit main.dart (Permanent)
```dart
// In lib/main.dart, find this line:
PerformanceLoggingConfig.setMode(PerformanceLoggingMode.quiet);

// Change to:
PerformanceLoggingConfig.setMode(PerformanceLoggingMode.verbose);
```

### Method 2: Runtime Toggle (Temporary)
```dart
// Anywhere in your code:
PerformanceLoggingMode.verbose.activate();

// Or back to quiet:
PerformanceLoggingMode.quiet.activate();
```

## What Each Mode Shows

### Quiet Mode Output
```
// Clean terminal - no performance logs
I/flutter: 🚀 App initialized successfully
```

### Verbose Mode Output
```
I/flutter: 🚀 PerformanceMetrics: Initialized and monitoring
I/flutter: 🎯 FrameRateMonitor: Started monitoring
I/flutter: WARNING: 🐌 Jank detected: 19ms (52.6 FPS, mild) [PATTERN: 100 janks in 5s] 💡 Frequent mild jank - check for inefficient rebuilds
I/flutter: 📊 Performance Optimization Summary: Issues detected: 2 (Critical: 0, High: 1)
```

## Performance Dashboard

### **Visibility by Build Mode**

- **Debug/Profile Mode**: ✅ Fully visible and functional
- **Release Mode**: ❌ Hidden from users (professional app experience)

### **Access**
- **Development**: Settings → More Settings → Advanced → Performance Dashboard
- **Production**: Not available (hidden for end users)

### **Data Availability**
- **Debug/Profile**: Complete real-time metrics
- **Release**: Essential metrics only (memory, battery optimization)

## Best Practices

### For Daily Development
```dart
PerformanceLoggingConfig.setMode(PerformanceLoggingMode.quiet);
```
- Clean terminal output
- Focus on your actual development work
- Performance monitoring continues silently

### For Performance Optimization
```dart
PerformanceLoggingConfig.setMode(PerformanceLoggingMode.verbose);
```
- Enable when you notice performance issues
- Use with Performance Dashboard for complete analysis
- Switch back to quiet when done

### For Bug Investigation
```dart
PerformanceLoggingConfig.setMode(PerformanceLoggingMode.debug);
```
- Maximum detail for troubleshooting
- Stack traces for jank events
- Comprehensive optimization logs

## System Status by Build Mode

### **Debug/Profile Mode**
✅ **Performance Monitoring**: Full monitoring (frame rate, CPU, memory, battery, network)
✅ **Performance Dashboard**: Fully functional and visible
✅ **Jank Mitigation**: Complete jank detection and mitigation
✅ **Memory Optimization**: Advanced memory management
✅ **Battery Optimization**: Full power saving features

### **Release Mode (Production)**
✅ **Essential Monitoring**: Memory and battery monitoring only
❌ **Performance Dashboard**: Hidden from end users
✅ **Memory Optimization**: Essential memory management active
✅ **Battery Optimization**: Full power saving features active
❌ **Detailed Monitoring**: Frame rate, CPU, network monitoring disabled

**Professional Approach**: Release builds focus on essential optimizations while hiding developer tools from end users.

## Quick Commands

```dart
// Quick mode switches (add anywhere in your code for testing)
PerformanceLoggingMode.quiet.activate();    // Clean development
PerformanceLoggingMode.minimal.activate();  // Basic info
PerformanceLoggingMode.verbose.activate();  // Performance debugging
PerformanceLoggingMode.debug.activate();    // Full analysis
```

---

**Remember**: You can always access detailed performance data through the Performance Dashboard in Settings, regardless of the logging mode!

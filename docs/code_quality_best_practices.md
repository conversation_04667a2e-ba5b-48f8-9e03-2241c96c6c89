# Flutter Code Quality & Best Practices Review Checklist

## Readability & Formatting

**Relevant Tools & Project Files:** `analysis_options.yaml`, Dart formatter (`dart format`), IDE formatting tools.

### Coding Style & Formatting
- [ ] Is the Dart code consistently formatted (e.g., using `dart format`)?
- [ ] Are there established lint rules in `analysis_options.yaml` and are they being followed (e.g., from `flutter_lints`, `effective_dart`)?
- [ ] Is the code free of lint warnings and errors?
- [ ] Is there a consistent naming convention for variables, functions, classes, and files (e.g., `lowerCamelCase` for variables/functions, `UpperCamelCase` for classes, `snake_case` for files)?
- [ ] Are lines kept to a reasonable length (e.g., 80-100 characters)?
- [ ] Is indentation consistent and correct?
- [ ] Is whitespace used effectively to separate logical blocks of code?
- [ ] Are braces `{}` used consistently for all control flow statements (if, else, while, for)?

### Commenting
- [ ] Is complex logic adequately explained with comments?
- [ ] Are comments clear, concise, and up-to-date?
- [ ] Are there any commented-out blocks of code that should be removed?
- [ ] Are public APIs (classes, methods, functions) documented with Dartdoc comments (`///`)?
- [ ] Do comments explain *why* something is done, not just *what* is being done (if the "what" is already clear from the code)?

### Code Structure & Organization
- [ ] Are files and folders organized logically (e.g., by feature, by layer)?
- [ ] Are individual files and classes focused on a single responsibility?
- [ ] Are functions and methods kept short and focused?
- [ ] Is code duplication minimized by using functions, classes, or mixins?
- [ ] Are imports organized and free of unused imports? (Often handled by the IDE/formatter)
- [ ] Are constants and magic numbers replaced with named constants?

## Widget Best Practices

**Relevant Code Files/Patterns:** Widget classes (StatelessWidget, StatefulWidget), build methods, widget tree structure.

### Widget Granularity
- [ ] Are large widgets broken down into smaller, more manageable sub-widgets?
- [ ] Does each widget have a single, clear responsibility?
- [ ] Are widgets reusable where appropriate?

### `build()` Method Efficiency
- [ ] Is the `build()` method free of side effects or heavy computations?
- [ ] Are widgets rebuilt unnecessarily? (Use Flutter DevTools "Highlight Repaints" or "Track Widget Rebuilds")
- [ ] Is `const` constructor used for widgets that do not change, to prevent unnecessary rebuilds?
- [ ] For `StatefulWidget`s, is `setState()` called only when necessary and as locally as possible?

### Choosing Between StatelessWidget and StatefulWidget
- [ ] Is `StatelessWidget` used whenever a widget doesn't need to manage internal state?
- [ ] Is `StatefulWidget` used appropriately when a widget's appearance or behavior needs to change based on internal state or lifecycle events?

### Immutability
- [ ] Are properties of `StatelessWidget`s and `State` objects marked as `final` where possible?
- [ ] Is immutable state preferred to avoid unexpected side effects?

### Keys
- [ ] Are keys used appropriately, especially when managing lists of widgets or preserving state during widget reordering/modification? (e.g., `ValueKey`, `ObjectKey`, `GlobalKey`)
- [ ] Is the use of `GlobalKey`s minimized due to their higher cost?

### Context Usage
- [ ] Is `BuildContext` used correctly and not held onto beyond its lifecycle?
- [ ] Is `context` passed down appropriately, or is state management used to avoid excessive prop drilling?

## Error Handling & Logging

**Relevant Tools & Project Files:** `try-catch` blocks, logging packages (e.g., `logging`, `logger`), crash reporting tools (e.g., Firebase Crashlytics, Sentry).

### Exception Handling
- [ ] Are potential errors (e.g., network requests, file I/O, parsing) handled gracefully using `try-catch` blocks?
- [ ] Are specific exception types caught where possible, rather than generic `Exception` or `Error`?
- [ ] Is user-facing error feedback clear, informative, and user-friendly?
- [ ] Does the app recover from errors where possible, or fail gracefully?
- [ ] Are there any empty `catch` blocks that swallow exceptions without handling or logging?

### Logging Strategy
- [ ] Is there a consistent logging strategy in place?
- [ ] Are log levels (e.g., debug, info, warning, error) used appropriately?
- [ ] Is sensitive information (passwords, API keys, PII) excluded from logs?
- [ ] Are logs informative enough to help diagnose issues in development and production?
- [ ] Are logs excessively noisy, making it hard to find important information?

### Crash Reporting
- [ ] Is a crash reporting tool integrated into the app?
- [ ] Are crashes being monitored and addressed?
- [ ] Is sufficient context (e.g., stack trace, device info, user state) sent with crash reports?

## Testing

**Relevant Tools & Project Files:** `test/` directory, `flutter_test` package, `integration_test` package, mocking libraries (e.g., `mockito`).

### Unit Tests
- [ ] Are critical business logic units (functions, classes, services) covered by unit tests?
- [ ] Do unit tests focus on a single unit of behavior?
- [ ] Are tests well-named, clearly describing what they are testing?
- [ ] Are mocks and stubs used effectively to isolate units under test?
- [ ] Is there a good balance of positive and negative test cases?

### Widget Tests
- [ ] Are important widgets and their interactions covered by widget tests?
- [ ] Do widget tests verify UI rendering, state changes, and event handling?
- [ ] Are finders used effectively to locate widgets (`find.text`, `find.byKey`, `find.byType`)?
- [ ] Is `tester.pumpWidget()` and `tester.pump()` used correctly to simulate frames and state changes?

### Integration Tests
- [ ] Are critical user flows and interactions between multiple parts of the app covered by integration tests?
- [ ] Do integration tests run on real devices or emulators?
- [ ] Do tests verify the complete flow, including navigation, data fetching, and state updates across screens?

### Test Coverage
- [ ] Is test coverage measured, and are there targets for coverage? (Note: High coverage doesn't guarantee quality, but low coverage indicates risk).
- [ ] Are new features and bug fixes accompanied by relevant tests?

### Test Maintainability
- [ ] Are tests easy to read, understand, and maintain?
- [ ] Are tests independent and not reliant on the state of previous tests?
- [ ] Is common test setup code refactored into helper methods or `setUp`/`tearDown` blocks?

## Security

**Relevant Tools & Project Files:** Secure storage (e.g., `flutter_secure_storage`), HTTPS usage, input validation logic, `android/app/build.gradle`, `ios/Runner/Info.plist`.

### Data Storage
- [ ] Is sensitive data (API keys, tokens, PII) stored securely (e.g., using `flutter_secure_storage` or platform-specific keychain/keystore)?
- [ ] Is data encryption used for sensitive information at rest?
- [ ] Is caching of sensitive data handled carefully and cleared when appropriate?

### Network Communication
- [ ] Is HTTPS used for all network communication?
- [ ] Are SSL pinning or certificate validation mechanisms considered for high-security applications?
- [ ] Is input validation performed on data received from the network?
- [ ] Are API keys and secrets handled securely, not hardcoded in client-side code (use environment variables or build configurations)?

### Input Validation
- [ ] Is all user input validated on both the client-side and server-side (if applicable)?
- [ ] Are potential injection vulnerabilities (SQLi, XSS - less common in Flutter but consider web views) addressed?
- [ ] Is the app protected against common vulnerabilities like deep linking exploits (validate incoming links)?

### Dependencies
- [ ] Are third-party libraries regularly updated to patch security vulnerabilities?
- [ ] Are dependencies audited for known security issues? (e.g., using `flutter pub outdated` and checking advisories).

### Platform Security
- [ ] Are platform-specific security best practices followed (e.g., Proguard/R8 for Android, App Transport Security for iOS)?
- [ ] Are permissions requested minimally and only when necessary?

## Build & Deployment

**Relevant Tools & Project Files:** `pubspec.yaml`, CI/CD scripts (e.g., GitHub Actions, GitLab CI, Jenkinsfile), `build.yaml` (for build_runner), fastlane files.

### Version Control
- [ ] Is all code managed in a version control system (e.g., Git)?
- [ ] Are commit messages clear and descriptive?
- [ ] Are branches used effectively for feature development and releases?
- [ ] Is `.gitignore` configured properly to exclude unnecessary files (e.g., IDE files, build artifacts, local config)?

### Dependency Management
- [ ] Is `pubspec.yaml` well-maintained?
- [ ] Are dependencies versioned appropriately (e.g., using caret `^` for stable versions, pinning critical dependencies if needed)?
- [ ] Are unused dependencies removed?
- [ ] Is `flutter pub get` run after `pubspec.yaml` changes?

### Build Process
- [ ] Is there a repeatable and reliable build process?
- [ ] Are different build configurations (e.g., debug, release, staging) managed effectively (e.g., using flavors or separate entry points)?
- [ ] Are API keys, environment variables, and other configurations handled securely for different builds (not hardcoded)?
- [ ] Is code obfuscation (e.g., `--obfuscate --split-debug-info` for Flutter) used for release builds?

### Continuous Integration/Continuous Deployment (CI/CD)
- [ ] Is CI/CD set up to automate testing, building, and deployment?
- [ ] Do CI pipelines run tests on every commit or pull request?
- [ ] Are build artifacts archived and versioned?

### App Signing & Distribution
- [ ] Are app signing keys (keystore for Android, provisioning profiles/certificates for iOS) managed securely?
- [ ] Is the app versioning scheme (e.g., `version: 1.0.0+1` in `pubspec.yaml`) followed consistently?
- [ ] Are releases tagged in version control?

## Documentation (Code & Project)

**Relevant Tools & Project Files:** `README.md`, `CONTRIBUTING.md`, `CHANGELOG.md`, `docs/` folder, Dartdoc comments.

### Code Documentation (Dartdoc)
- [ ] Are all public classes, methods, and properties documented using Dartdoc (`///`)?
- [ ] Do Dartdoc comments clearly explain the purpose, parameters, and return values?
- [ ] Are examples provided in Dartdoc where helpful?
- [ ] Can API documentation be generated successfully using `dart doc .`?

### Project-Level Documentation
- [ ] Is there a `README.md` file with a project overview, setup instructions, and key information?
- [ ] Is there a `CHANGELOG.md` to track notable changes for each version?
- [ ] Is there a `CONTRIBUTING.md` if contributions are expected?
- [ ] Is architectural design or complex system documentation available (e.g., in a `docs/` folder or wiki)?
- [ ] Is documentation kept up-to-date with code changes?

### Onboarding & Maintainability
- [ ] Is the codebase structured and documented in a way that makes it easy for new developers to understand?
- [ ] Are complex or non-obvious parts of the code well-explained either through comments or separate documentation?
- [ ] Is configuration management (e.g., environment variables, feature flags) documented?

# 🎨 DassoShu Reader - Code Templates for Design System Compliance

## 📋 **MANDATORY CODE TEMPLATES**

Copy these templates for guaranteed design system compliance. **Never deviate from these patterns.**

---

## 🏗️ **WIDGET TEMPLATES**

### **Template 1: Basic Container with Padding**
```dart
// ✅ TEMPLATE: Standard container with design system compliance
Container(
  padding: EdgeInsets.all(DesignSystem.spaceM),
  margin: EdgeInsets.symmetric(
    horizontal: DesignSystem.spaceS,
    vertical: DesignSystem.spaceXS,
  ),
  decoration: BoxDecoration(
    borderRadius: BorderRadius.circular(DesignSystem.radiusM),
    color: Theme.of(context).colorScheme.surface,
  ),
  child: YourContentWidget(),
)
```

### **Template 2: Responsive Container with Manufacturer Adjustments**
```dart
// ✅ TEMPLATE: High-priority container with pixel-perfect adjustments
Container(
  padding: DesignSystem.getAdaptivePaddingWithManufacturerAdjustment(
    context,
    EdgeInsets.all(DesignSystem.spaceM),
  ),
  child: YourContentWidget(),
)
```

### **Template 3: Text Widget with Material Design 3 Typography Scale**
```dart
// ✅ TEMPLATE: Use Material Design 3 typography scale with manufacturer adjustments
Text(
  'Your Text',
  style: Theme.of(context).textTheme.titleMedium?.copyWith(
    fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w600),
    color: Theme.of(context).colorScheme.onSurface,
  ),
)

// ✅ TEMPLATE: For different semantic roles
Text('App Title', style: Theme.of(context).textTheme.displaySmall)
Text('Page Heading', style: Theme.of(context).textTheme.headlineMedium)
Text('Card Title', style: Theme.of(context).textTheme.titleMedium)
Text('Body Text', style: Theme.of(context).textTheme.bodyMedium)
Text('Caption', style: Theme.of(context).textTheme.bodySmall)
Text('Button Label', style: Theme.of(context).textTheme.labelLarge)
```

### **Template 4: Icon with Manufacturer Adjustments**
```dart
// ✅ TEMPLATE: Icon for navigation, buttons, high-visibility areas
Icon(
  Icons.your_icon,
  size: DesignSystem.getAdjustedIconSize(DesignSystem.widgetIconSizeMedium),
  color: Theme.of(context).colorScheme.onSurface,
)
```

### **Template 5: Button with Design System Compliance**
```dart
// ✅ TEMPLATE: Standard button with proper touch targets and styling
ElevatedButton(
  onPressed: onPressed,
  style: ElevatedButton.styleFrom(
    minimumSize: Size.fromHeight(DesignSystem.widgetMinTouchTarget),
    padding: EdgeInsets.symmetric(
      horizontal: DesignSystem.spaceL,
      vertical: DesignSystem.spaceS,
    ),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(DesignSystem.radiusM),
    ),
  ),
  child: Text(
    'Button Text',
    style: TextStyle(
      fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w600),
    ),
  ),
)
```

---

## 📱 **PAGE TEMPLATES**

### **Template 6: Standard Page Layout**
```dart
// ✅ TEMPLATE: Standard page with proper scaffold and padding
class YourPage extends StatelessWidget {
  const YourPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Page Title',
          style: TextStyle(
            fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w600),
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.surface,
      ),
      body: Padding(
        padding: DesignSystem.getAdaptivePadding(context),
        child: YourPageContent(),
      ),
    );
  }
}
```

### **Template 7: Settings Page Layout**
```dart
// ✅ TEMPLATE: Settings page with proper list styling
class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Settings',
          style: TextStyle(
            fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w600),
          ),
        ),
      ),
      body: ListView(
        padding: DesignSystem.getAdaptivePadding(context),
        children: [
          _buildSettingsSection(context, 'Section Title', [
            _buildSettingsTile(context, 'Setting Name', 'Description'),
          ]),
        ],
      ),
    );
  }

  Widget _buildSettingsSection(BuildContext context, String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(
            left: DesignSystem.spaceS,
            bottom: DesignSystem.spaceXS,
          ),
          child: Text(
            title,
            style: TextStyle(
              fontSize: DesignSystem.fontSizeS,
              fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w600),
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ),
        ...children,
        SizedBox(height: DesignSystem.spaceL),
      ],
    );
  }

  Widget _buildSettingsTile(BuildContext context, String title, String subtitle) {
    return ListTile(
      title: Text(
        title,
        style: TextStyle(
          fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w500),
        ),
      ),
      subtitle: Text(subtitle),
      contentPadding: EdgeInsets.symmetric(
        horizontal: DesignSystem.spaceS,
        vertical: DesignSystem.spaceXS,
      ),
      onTap: () {
        // Handle tap
      },
    );
  }
}
```

---

## 🎯 **NAVIGATION TEMPLATES**

### **Template 8: TabBar with Pixel-Perfect Implementation**
```dart
// ✅ TEMPLATE: TabBar with full manufacturer adjustments
TabBar(
  controller: tabController,
  labelStyle: TextStyle(
    fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w600),
    fontSize: DesignSystem.fontSizeM,
  ),
  unselectedLabelStyle: TextStyle(
    fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.normal),
    fontSize: DesignSystem.fontSizeM,
  ),
  labelPadding: DesignSystem.getAdaptivePaddingWithManufacturerAdjustment(
    context,
    EdgeInsets.symmetric(horizontal: DesignSystem.spaceS),
  ),
  tabs: tabs.map((tab) => Tab(
    child: Text(tab.label),
    icon: Icon(
      tab.icon,
      size: DesignSystem.getAdjustedIconSize(DesignSystem.widgetIconSizeMedium),
    ),
  )).toList(),
)
```

### **Template 9: Context Menu with Pixel-Perfect Implementation**
```dart
// ✅ TEMPLATE: Context menu with manufacturer adjustments
Container(
  padding: DesignSystem.getAdaptivePaddingWithManufacturerAdjustment(
    context,
    EdgeInsets.all(DesignSystem.spaceS),
  ),
  decoration: BoxDecoration(
    color: Theme.of(context).colorScheme.surface,
    borderRadius: BorderRadius.circular(DesignSystem.radiusM),
    boxShadow: [
      BoxShadow(
        color: Theme.of(context).colorScheme.shadow.withOpacity(0.1),
        blurRadius: DesignSystem.elevationM,
        offset: const Offset(0, 2),
      ),
    ],
  ),
  child: Row(
    mainAxisSize: MainAxisSize.min,
    children: [
      // Action buttons with proper spacing
      ...actionButtons.map((button) => [
        button,
        if (button != actionButtons.last)
          SizedBox(
            width: DesignSystem.getAdaptiveSpacingWithManufacturerAdjustment(
              context,
              DesignSystem.spaceXS,
            ),
          ),
      ]).expand((element) => element),
    ],
  ),
)
```

---

## 🔧 **UTILITY TEMPLATES**

### **Template 10: Spacing Widgets**
```dart
// ✅ TEMPLATE: Vertical spacing
SizedBox(height: DesignSystem.spaceM)

// ✅ TEMPLATE: Horizontal spacing
SizedBox(width: DesignSystem.spaceS)

// ✅ TEMPLATE: Manufacturer-adjusted spacing
SizedBox(
  width: DesignSystem.getAdaptiveSpacingWithManufacturerAdjustment(
    context,
    DesignSystem.spaceS,
  ),
)
```

### **Template 11: Responsive Breakpoints**
```dart
// ✅ TEMPLATE: Responsive layout decisions
Widget build(BuildContext context) {
  if (DesignSystem.isSmallPhone(context)) {
    return CompactLayout();
  } else if (DesignSystem.isTablet(context)) {
    return TabletLayout();
  } else {
    return StandardLayout();
  }
}
```

---

## 📋 **TEMPLATE USAGE CHECKLIST**

Before using any template, verify:
- [ ] All spacing uses DesignSystem constants
- [ ] All colors use Theme.of(context).colorScheme
- [ ] High-visibility text uses getAdjustedFontWeight()
- [ ] Icons use getAdjustedIconSize() for navigation/buttons
- [ ] Touch targets meet 44dp minimum
- [ ] No hardcoded values anywhere

**Remember: These templates are mandatory patterns. Deviation requires architectural review.**

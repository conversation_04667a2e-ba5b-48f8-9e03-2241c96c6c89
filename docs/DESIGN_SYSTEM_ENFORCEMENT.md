# 🏗️ DassoShu Reader - Design System Enforcement Guide

## 🎯 **MANDATORY ARCHITECTURE COMPLIANCE**

This document establishes **non-negotiable rules** for all UI development in DassoShu Reader. Every widget, page, and component **MUST** follow these patterns.

---

## 📋 **DUAL DESIGN SYSTEM ARCHITECTURE**

### **System 1: DesignSystem (Base Foundation)**
```dart
// ✅ MANDATORY: Always use DesignSystem constants
padding: EdgeInsets.all(DesignSystem.spaceM)           // ✅ CORRECT
padding: EdgeInsets.all(16.0)                          // ❌ FORBIDDEN

borderRadius: BorderRadius.circular(DesignSystem.radiusM)  // ✅ CORRECT
borderRadius: BorderRadius.circular(8.0)                   // ❌ FORBIDDEN
```

### **System 2: Pixel-Perfect Adjustments (Manufacturer Layer)**
```dart
// ✅ MANDATORY: Use for high-visibility components
fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w600)  // ✅ CORRECT
fontWeight: FontWeight.w600                                      // ❌ SUBOPTIMAL

iconSize: DesignSystem.getAdjustedIconSize(24.0)               // ✅ CORRECT
iconSize: 24.0                                                 // ❌ SUBOPTIMAL
```

---

## 🚫 **FORBIDDEN PATTERNS**

### **❌ NEVER USE HARDCODED VALUES**
```dart
// ❌ FORBIDDEN - Will cause lint errors
Container(
  padding: EdgeInsets.all(16.0),        // ❌ Use DesignSystem.spaceM
  margin: EdgeInsets.only(top: 8.0),    // ❌ Use DesignSystem.spaceS
  width: 200.0,                         // ❌ Use responsive methods
  height: 48.0,                         // ❌ Use DesignSystem.widgetMinTouchTarget
)

Text(
  'Label',
  style: TextStyle(
    fontSize: 14.0,                     // ❌ Use DesignSystem.fontSizeM
    fontWeight: FontWeight.w600,        // ❌ Use getAdjustedFontWeight()
    color: Colors.black,                // ❌ Use Theme.of(context).colorScheme
  ),
)
```

### **❌ FORBIDDEN SPACING PATTERNS**
```dart
// ❌ FORBIDDEN
SizedBox(height: 16.0)                 // Use DesignSystem.spaceM
SizedBox(width: 8.0)                   // Use DesignSystem.spaceS
Padding(padding: EdgeInsets.all(12.0)) // Use DesignSystem constants
```

---

## ✅ **MANDATORY PATTERNS**

### **✅ CORRECT SPACING USAGE**
```dart
// ✅ MANDATORY: Always use DesignSystem constants
Container(
  padding: EdgeInsets.all(DesignSystem.spaceM),
  margin: EdgeInsets.symmetric(
    horizontal: DesignSystem.spaceS,
    vertical: DesignSystem.spaceXS,
  ),
  child: content,
)

// ✅ MANDATORY: Use adaptive methods for responsive design
Container(
  padding: DesignSystem.getAdaptivePadding(context),
  child: content,
)
```

### **✅ CORRECT TEXT STYLING**
```dart
// ✅ MANDATORY: Use manufacturer-adjusted text for high-visibility components
Text(
  'Tab Label',
  style: TextStyle(
    fontSize: DesignSystem.fontSizeM,
    fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w600),
    color: Theme.of(context).colorScheme.onSurface,
  ),
)

// ✅ MANDATORY: Use theme colors, never hardcoded colors
Text(
  'Content',
  style: TextStyle(
    color: Theme.of(context).colorScheme.onSurface,     // ✅ CORRECT
    // color: Colors.black,                              // ❌ FORBIDDEN
  ),
)
```

### **✅ CORRECT ICON USAGE**
```dart
// ✅ MANDATORY: Use adjusted icon sizes for cross-manufacturer consistency
Icon(
  Icons.settings,
  size: DesignSystem.getAdjustedIconSize(DesignSystem.widgetIconSizeMedium),
  color: Theme.of(context).colorScheme.onSurface,
)

// ✅ MANDATORY: Use adaptive icon sizes
Icon(
  Icons.home,
  size: DesignSystem.getAdaptiveTabIconSize(context),
)
```

---

## 🎯 **COMPONENT-SPECIFIC REQUIREMENTS**

### **Navigation Components (TabBar, BottomBar)**
```dart
// ✅ MANDATORY: Full pixel-perfect implementation required
TabBar(
  labelStyle: TextStyle(
    fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w600),
    fontSize: DesignSystem.fontSizeM,
  ),
  labelPadding: DesignSystem.getAdaptivePaddingWithManufacturerAdjustment(
    context,
    EdgeInsets.symmetric(horizontal: DesignSystem.spaceS),
  ),
)
```

### **Reading Interface Components**
```dart
// ✅ MANDATORY: Critical for reading experience consistency
Container(
  padding: DesignSystem.getAdaptivePaddingWithManufacturerAdjustment(
    context,
    EdgeInsets.all(DesignSystem.spaceM),
  ),
  child: Text(
    'Reading Content',
    style: TextStyle(
      fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.normal),
    ),
  ),
)
```

### **Settings & List Components**
```dart
// ✅ MANDATORY: Ensure consistent text rendering
ListTile(
  title: Text(
    'Setting Name',
    style: TextStyle(
      fontWeight: DesignSystem.getAdjustedFontWeight(FontWeight.w500),
    ),
  ),
  contentPadding: DesignSystem.getAdaptivePadding(context),
)
```

---

## 🔧 **ENFORCEMENT MECHANISMS**

### **1. Automated Lint Rules**
- **Error Level**: Hardcoded values (padding, margins, colors, font sizes)
- **Warning Level**: Missing manufacturer adjustments for text/icons
- **Info Level**: Suggestions for responsive methods

### **2. Code Review Checklist**
- [ ] No hardcoded spacing values
- [ ] No hardcoded colors (use theme colors)
- [ ] No hardcoded font sizes
- [ ] Manufacturer adjustments applied to high-visibility text
- [ ] Icon sizes use adjusted methods
- [ ] Touch targets meet 44dp minimum

### **3. IDE Integration**
- Custom lint rules provide real-time feedback
- Auto-suggestions for DesignSystem methods
- Error highlighting for forbidden patterns

---

## 📚 **QUICK REFERENCE**

### **Essential Constants**
```dart
// Spacing
DesignSystem.spaceXS    // 4.0
DesignSystem.spaceS     // 8.0
DesignSystem.spaceM     // 16.0
DesignSystem.spaceL     // 24.0
DesignSystem.spaceXL    // 32.0

// Font Sizes - Standard Scale
DesignSystem.fontSizeXS  // 10.0 - Captions, fine print
DesignSystem.fontSizeS   // 12.0 - Body small, labels
DesignSystem.fontSizeM   // 14.0 - Body medium, default text
DesignSystem.fontSizeL   // 16.0 - Body large, primary text
DesignSystem.fontSizeXL  // 18.0 - Title medium, headings
DesignSystem.fontSizeXXL // 20.0 - Title large

// Font Sizes - Headings & Display
DesignSystem.fontSizeHeadingS  // 22.0 - Headline small
DesignSystem.fontSizeHeadingM  // 24.0 - Headline medium
DesignSystem.fontSizeHeadingL  // 28.0 - Headline large
DesignSystem.fontSizeHeadingXL // 32.0 - Display small
DesignSystem.fontSizeDisplayM  // 36.0 - Display medium
DesignSystem.fontSizeDisplayL  // 45.0 - Display large
DesignSystem.fontSizeDisplayXL // 57.0 - Display extra large

// Font Sizes - Chinese Language Specific
DesignSystem.fontSizeChineseM  // 20.0 - HSK answer buttons (optimized for multi-character display)
DesignSystem.fontSizeChineseL  // 64.0 - Learning cards
DesignSystem.fontSizeChineseXL // 72.0 - Hero display

// Font Sizes - Pinyin & Pronunciation
DesignSystem.fontSizePinyinS   // 14.0 - Compact guides
DesignSystem.fontSizePinyinM   // 18.0 - Secondary pronunciation
DesignSystem.fontSizePinyinL   // 26.0 - Main pronunciation

// Touch Targets
DesignSystem.widgetMinTouchTarget // 44.0
```

### **Essential Methods**
```dart
// Manufacturer Adjustments (High-Priority Components)
DesignSystem.getAdjustedFontWeight(FontWeight.w600)
DesignSystem.getAdjustedIconSize(24.0)
DesignSystem.getAdjustedFontSize(context, DesignSystem.fontSizeM)
DesignSystem.getAdaptiveSpacingWithManufacturerAdjustment(context, spacing)

// Responsive Design (All Components)
DesignSystem.getAdaptivePadding(context)
DesignSystem.getAdaptiveFontSize(context, baseFontSize)
DesignSystem.getAccessibleFontSize(context, baseFontSize)
DesignSystem.isSmallPhone(context)
DesignSystem.getAdaptiveTabIconSize(context)

// Convenience Methods for Common Patterns
DesignSystem.getBodyFontSize(context)      // Body text with full adaptation
DesignSystem.getHeadingFontSize(context)   // Headings with full adaptation
DesignSystem.getChineseFontSize(context)   // Chinese characters with adaptation
DesignSystem.getPinyinFontSize(context)    // Pinyin with adaptation
DesignSystem.getLabelFontSize(context)     // Labels with accessibility
```

---

## ⚡ **PERFORMANCE NOTES**

- Manufacturer detection: ~0.1ms (one-time initialization)
- Adjustment calculations: ~0.01ms per call
- Memory overhead: ~2KB for adjustment tables
- **Zero performance impact** on user experience

---

## 🎯 **COMPLIANCE VERIFICATION**

Run these commands to verify compliance:
```bash
flutter analyze                    # Check lint rules
dart run custom_lint               # Check design system rules
flutter test test/design_system/   # Run design system tests
```

**Remember: These are not suggestions - they are mandatory requirements for maintaining our professional, cross-manufacturer consistent user experience.**

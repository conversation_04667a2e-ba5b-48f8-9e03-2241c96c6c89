# Text-to-Speech (TTS) Technical Documentation

## Overview

The Dasso Reader implements a comprehensive dual-TTS system supporting both system-native TTS and enhanced cloud-based TTS services. The architecture provides seamless switching between TTS engines, robust error handling, and optimized performance for continuous reading experiences.

## Architecture Overview

### Core Components

```
TtsFactory (Singleton)
├── SystemTts (flutter_tts)
└── EdgeTts (Microsoft Edge TTS API)
    └── EdgeTTSApi (WebSocket-based API client)

TtsHandler (Audio Service Integration)
├── Audio Session Management
├── Background Playback Support
└── Media Controls Integration

Dictionary TTS (Separate System)
├── OnlineDictionaryService
├── Flutter TTS (Primary)
└── Google Translate TTS (Fallback)
```

### TTS Engine Selection Logic

The system uses a factory pattern to determine which TTS engine to use:

<augment_code_snippet path="lib/service/tts/tts_factory.dart" mode="EXCERPT">
````dart
BaseTts createTts() {
  final bool isSystemTts = Prefs().isSystemTts;
  return isSystemTts ? SystemTts() : EdgeTts();
}
````
</augment_code_snippet>

## TTS Engines

### 1. System TTS (flutter_tts)

**Primary Implementation**: `lib/service/tts/system_tts.dart`

**Features:**
- Offline functionality using device's built-in TTS engine
- Platform-specific optimizations (Android/iOS/Windows)
- Direct integration with system audio controls
- Automatic voice and engine detection

**Configuration:**
- Volume: 0.0 - 1.0 (default: 1.0)
- Pitch: 0.5 - 2.0 (default: 1.0) 
- Rate: 0.0 - 2.0 (default: 0.6)

<augment_code_snippet path="lib/service/tts/system_tts.dart" mode="EXCERPT">
````dart
@override
Future<void> speak({String? content}) async {
  await setAwaitOptions();
  if (content != null) {
    _currentVoiceText = content;
  }
  _currentVoiceText ??= await getHereFunction() as String?;
  await flutterTts.setVolume(volume);
  await flutterTts.setSpeechRate(rate);
  await flutterTts.setPitch(pitch);

  await flutterTts.speak(_currentVoiceText!);
}
````
</augment_code_snippet>

### 2. Edge TTS (Microsoft Edge TTS API)

**Primary Implementation**: `lib/service/tts/edge_tts.dart`

**Features:**
- High-quality neural voices (400+ voices across 140+ languages)
- WebSocket-based streaming audio
- Audio preloading for seamless playback
- SSML support for advanced speech control

**Voice Model Selection:**
- Automatic language detection based on system locale
- Fallback to English (en-US-JennyNeural) if no match found
- User-configurable voice selection via settings

<augment_code_snippet path="lib/service/tts/edge_tts_api.dart" mode="EXCERPT">
````dart
static String createSsml() {
  String escapedText = escapeXml(text);
  int pitchValue = ((pitch - 1) * 100).toInt();
  int rateValue = (rate * 100).toInt();
  int volumeValue = (volume * 100).toInt();

  String pitchStr = pitchValue > 0 ? '+${pitchValue}Hz' : '-${pitchValue.abs()}Hz';

  return """
<speak version='1.0' xmlns='http://www.w3.org/2001/10/synthesis' xml:lang='en-US'>
<voice name='$voice'>
<prosody pitch='$pitchStr' rate='+$rateValue%' volume='+$volumeValue%'>
$escapedText
</prosody>
</voice>
</speak>""";
}
````
</augment_code_snippet>

## Settings and Configuration

### Shared Preferences Integration

All TTS settings are persisted using SharedPreferences:

<augment_code_snippet path="lib/config/shared_preference_provider.dart" mode="EXCERPT">
````dart
// TTS Settings
set ttsVolume(double volume) {
  prefs.setDouble('ttsVolume', volume);
  notifyListeners();
}

double get ttsVolume {
  return prefs.getDouble('ttsVolume') ?? 1.0;
}

bool get isSystemTts {
  return prefs.getBool('isSystemTts') ?? false;
}

String get ttsVoiceModel {
  String? model = prefs.getString('ttsVoiceModel');
  if (model == null) {
    final languageCode = getCurrentLanguageCode().toLowerCase();
    // Auto-select voice based on system language
    // Fallback to 'en-US-JennyNeural' if no match
  }
  return model;
}
````
</augment_code_snippet>

### Voice Model Management

The system includes 400+ pre-configured voice models from Microsoft Edge TTS:

- **File**: `lib/utils/tts_model_list.dart`
- **Structure**: Each voice includes Name, ShortName, Gender, Locale, and VoiceTag metadata
- **Selection Logic**: Automatic language matching with manual override capability

## UI Integration

### Reading Interface TTS Controls

**Primary Widget**: `lib/widgets/reading_page/tts_widget.dart`

**Features:**
- Play/Pause/Stop controls
- Volume, Pitch, Rate sliders
- Section navigation (Previous/Next)
- Auto-stop timer functionality
- Real-time state synchronization

<augment_code_snippet path="lib/widgets/reading_page/tts_widget.dart" mode="EXCERPT">
````dart
@override
void initState() {
  if (TtsHandler().ttsStateNotifier.value != TtsStateEnum.playing) {
    TtsHandler()
        .init(
      widget.epubPlayerKey.currentState!.initTts,
      widget.epubPlayerKey.currentState!.ttsNext,
      widget.epubPlayerKey.currentState!.ttsPrev,
    )
        .then((value) {
      audioHandler.play();
    });
  }
  super.initState();
}
````
</augment_code_snippet>

### Settings Interface

**Primary Widget**: `lib/page/settings_page/narrate.dart`

**Features:**
- System TTS vs Enhanced TTS toggle
- Voice model selection with language grouping
- Real-time voice preview
- Animated selection highlighting

## EPUB Integration

### Text Extraction and Processing

The TTS system integrates with the Foliate.js-based EPUB reader through JavaScript bridge methods:

<augment_code_snippet path="lib/page/book_player/epub_player.dart" mode="EXCERPT">
````dart
Future<void> initTts() async =>
    await webViewController.evaluateJavascript(source: 'window.ttsHere()');

Future<String> ttsNext() async {
  final result = await webViewController.callAsyncJavaScript(
    functionBody: 'return await ttsNext()',
  );
  return (result?.value as String?) ?? '';
}

Future<String> ttsPrepare() async {
  final result =
      await webViewController.evaluateJavascript(source: 'ttsPrepare()');
  return (result as String?) ?? '';
}
````
</augment_code_snippet>

### JavaScript TTS Engine

**File**: `assets/foliate-js/tts.js`

**Key Features:**
- Sentence-level text segmentation using regex patterns
- Multi-language sentence boundary detection
- Range-based text highlighting synchronization
- Iterator pattern for efficient text navigation

<augment_code_snippet path="assets/foliate-js/tts.js" mode="EXCERPT">
````javascript
function* getBlocks(doc) {
    const walker = doc.createTreeWalker(doc.body, NodeFilter.SHOW_TEXT)
    let lastRange = null

    for (let node = walker.nextNode(); node; node = walker.nextNode()) {
        let match
        const regex = /[.!?。！？]["'""'']?/g
        while ((match = regex.exec(node.textContent)) !== null) {
            const range = doc.createRange()
            if (lastRange) {
                lastRange.setEnd(node, match.index + match[0].length)
                if (!rangeIsEmpty(lastRange)) yield lastRange
            }
            lastRange = doc.createRange()
            lastRange.setStart(node, match.index + match[0].length)
        }
    }
}
````
</augment_code_snippet>

## Audio Service Integration

### Background Playback Support

**Implementation**: `lib/service/tts/tts_handler.dart`

The TTS system extends `BaseAudioHandler` to provide:
- Background audio playback
- System media controls integration
- Audio session management
- Notification-based controls

<augment_code_snippet path="lib/service/tts/tts_handler.dart" mode="EXCERPT">
````dart
class TtsHandler extends BaseAudioHandler with QueueHandler, SeekHandler {
  final TtsFactory _ttsFactory = TtsFactory();

  TtsHandler() {
    _initAudioSession();
  }

  Future<void> _initAudioSession() async {
    final session = await AudioSession.instance;
    await session.configure(const AudioSessionConfiguration.speech());
  }
}
````
</augment_code_snippet>

## Dictionary TTS System

### Separate TTS Implementation

**Purpose**: Provides pronunciation for Chinese characters and words in dictionary lookups

**Primary Service**: `lib/service/dictionary/online_dictionary_service.dart`

**Architecture:**
1. **Primary**: Flutter TTS (offline, system-based)
2. **Fallback**: Google Translate TTS API (online)

<augment_code_snippet path="lib/service/dictionary/online_dictionary_service.dart" mode="EXCERPT">
````dart
Future<bool> playPronunciation(String text, {BuildContext? context}) async {
  if (!_ttsInitialized) {
    await _initializeDictionaryTts();
  }

  try {
    // Use Flutter TTS for offline pronunciation
    await _dictionaryTts.stop();
    final result = await _dictionaryTts.speak(text);
    
    if (result == 1) {
      return true;
    } else {
      // Try fallback with Google Translate TTS if Flutter TTS fails
      if (!_isOfflineMode) {
        return await _playPronunciationFallback(text, context);
      }
    }
  } catch (e) {
    // Fallback to Google Translate TTS
    if (!_isOfflineMode) {
      return await _playPronunciationFallback(text, context);
    }
  }
  return false;
}
````
</augment_code_snippet>

## Dependencies

### Core TTS Packages

```yaml
dependencies:
  flutter_tts: ^4.2.2          # System TTS engine
  audioplayers: ^6.4.0         # Audio playback for Edge TTS
  audio_service: ^0.18.16      # Background audio service
  audio_session: ^0.1.23       # Audio session management
  web_socket_channel: ^3.0.2   # WebSocket for Edge TTS API
  http: ^1.3.0                 # HTTP client for API calls
  crypto: ^3.0.6               # Cryptographic functions for Edge TTS
  uuid: ^4.5.1                 # UUID generation for Edge TTS
```

## Performance Considerations

### Memory Management
- Audio preloading limited to next segment only
- Automatic disposal of audio players when switching engines
- Efficient text segmentation using iterators

### Network Optimization
- WebSocket connection reuse for Edge TTS
- Chunked audio streaming
- Automatic fallback to offline mode on network failure

### Battery Optimization
- Audio session configuration for speech content
- Efficient wake lock management
- Background processing limitations

## Error Handling and Fallbacks

### TTS Engine Failures
1. **System TTS Failure**: Automatic fallback to Edge TTS if available
2. **Edge TTS Network Issues**: Graceful degradation to System TTS
3. **Voice Model Unavailable**: Fallback to default English voice

### Dictionary TTS Failures
1. **System TTS Failure**: Fallback to Google Translate TTS
2. **Network Unavailable**: Graceful error messaging
3. **Audio Playback Issues**: User notification with retry option

## Troubleshooting

### Common Issues

1. **TTS Not Working on Android**
   - Check TTS engine installation
   - Verify language pack availability
   - Test with different voice models

2. **Edge TTS Connection Failures**
   - Verify internet connectivity
   - Check firewall/proxy settings
   - Validate WebSocket support

3. **Audio Playback Issues**
   - Check device audio permissions
   - Verify audio session configuration
   - Test with different audio formats

### Debug Information

Enable debug logging by setting appropriate log levels in the application. Key log points include:
- TTS engine initialization
- Voice model selection
- Audio streaming status
- Error conditions and fallbacks

## Implementation Strengths

### Robust Architecture
- **Dual-Engine Design**: Seamless fallback between system and cloud TTS
- **Factory Pattern**: Clean separation of concerns and easy extensibility
- **State Management**: Comprehensive state tracking with ValueNotifier pattern
- **Error Recovery**: Multiple fallback mechanisms ensure continuous operation

### User Experience
- **Offline Capability**: System TTS works without internet connection
- **High-Quality Voices**: 400+ neural voices via Microsoft Edge TTS
- **Seamless Integration**: Deep integration with EPUB reader and dictionary
- **Accessibility**: Full screen reader and keyboard navigation support

### Performance Optimization
- **Audio Preloading**: Next segment preparation for uninterrupted playback
- **Memory Efficiency**: Proper resource cleanup and disposal patterns
- **Background Support**: Audio service integration for system-level controls

## Areas for Improvement

### Current Limitations

1. **Edge TTS Dependency**: Relies on undocumented Microsoft API
   - **Risk**: API changes could break functionality
   - **Mitigation**: Implement additional TTS providers (Google Cloud TTS, Amazon Polly)

2. **Limited Offline Voice Quality**: System TTS quality varies by platform
   - **Impact**: Inconsistent user experience across devices
   - **Solution**: Implement downloadable high-quality voice packs

3. **Network Resilience**: Edge TTS requires stable internet connection
   - **Issue**: Poor network conditions cause playback interruptions
   - **Enhancement**: Implement adaptive bitrate and retry mechanisms

4. **Text Processing**: Basic sentence segmentation may miss complex punctuation
   - **Problem**: Unnatural pauses in some languages/content types
   - **Improvement**: Advanced NLP-based text preprocessing

### Technical Debt

1. **JavaScript Bridge Complexity**: Heavy reliance on WebView JavaScript communication
2. **State Synchronization**: Multiple state management patterns across components
3. **Error Handling**: Inconsistent error reporting and recovery strategies

## Extension Guide

### Adding New TTS Providers

1. **Implement BaseTts Interface**:
```dart
class CustomTts extends BaseTts {
  @override
  Future<void> speak({String? content}) async {
    // Implementation
  }

  @override
  Future<void> init(Function getCurrentText, Function getNextText, Function getPrevText) async {
    // Initialization logic
  }
}
```

2. **Update TtsFactory**:
```dart
BaseTts createTts() {
  switch (Prefs().selectedTtsProvider) {
    case 'system': return SystemTts();
    case 'edge': return EdgeTts();
    case 'custom': return CustomTts();
    default: return SystemTts();
  }
}
```

3. **Add Configuration UI**: Extend narrator settings to include new provider options

### Customizing Voice Models

1. **Extend Voice Model List**: Add entries to `lib/utils/tts_model_list.dart`
2. **Update Selection Logic**: Modify voice selection algorithm in SharedPreferences
3. **Test Compatibility**: Ensure new voices work with existing SSML generation

### Integrating Additional Audio Sources

1. **Extend EdgeTTSApi**: Add support for different audio endpoints
2. **Implement Caching**: Add local storage for frequently used audio segments
3. **Add Format Support**: Extend audio format handling in audioplayers integration

## Testing Strategy

### Unit Testing
- **TTS Engine Switching**: Verify factory pattern behavior
- **Settings Persistence**: Test SharedPreferences integration
- **Error Handling**: Validate fallback mechanisms

### Integration Testing
- **EPUB Integration**: Test JavaScript bridge communication
- **Audio Service**: Verify background playback functionality
- **Dictionary TTS**: Test pronunciation feature across different text types

### Platform Testing
- **Android**: Test with different TTS engines and language packs
- **iOS**: Verify system TTS integration and audio session handling
- **Windows**: Test desktop-specific TTS functionality

### Performance Testing
- **Memory Usage**: Monitor audio buffer management
- **Network Efficiency**: Test Edge TTS streaming performance
- **Battery Impact**: Measure power consumption during extended use

## Security Considerations

### Edge TTS API Security
- **Token Management**: Secure handling of trusted client tokens
- **Network Communication**: HTTPS/WSS encryption for all API calls
- **Data Privacy**: No user content stored on external servers

### Local Data Protection
- **Settings Encryption**: Consider encrypting sensitive TTS preferences
- **Audio Caching**: Implement secure temporary file handling
- **Permission Management**: Proper audio and network permission handling

## Maintenance Guidelines

### Regular Maintenance Tasks
1. **Voice Model Updates**: Quarterly updates to available voice list
2. **API Compatibility**: Monitor Microsoft Edge TTS API changes
3. **Dependency Updates**: Keep TTS-related packages current
4. **Performance Monitoring**: Track audio playback metrics

### Monitoring and Logging
- **Error Tracking**: Implement comprehensive error reporting
- **Usage Analytics**: Monitor TTS feature adoption and performance
- **Quality Metrics**: Track audio playback success rates

### Documentation Maintenance
- **API Changes**: Update documentation when TTS APIs change
- **Configuration Updates**: Keep settings documentation current
- **Troubleshooting**: Expand based on user-reported issues

## Future Improvements

### Planned Enhancements
1. **Offline Voice Downloads**: Cache high-quality voices locally
2. **SSML Advanced Features**: Support for more speech markup
3. **Voice Cloning**: Integration with custom voice models
4. **Reading Speed Adaptation**: Dynamic rate adjustment based on content complexity
5. **Multi-language Support**: Automatic language detection and voice switching

### Performance Optimizations
1. **Predictive Preloading**: Load multiple segments ahead
2. **Compression**: Optimize audio format for bandwidth
3. **Caching**: Implement intelligent audio caching
4. **Background Processing**: Improve multithreading for audio generation

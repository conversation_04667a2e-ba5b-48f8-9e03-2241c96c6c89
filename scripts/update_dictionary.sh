#!/bin/bash

# CC-CEDICT Database Update Script
# This script automates the complete process of updating the Chinese dictionary

set -e  # Exit on any error

echo "🚀 CC-CEDICT Database Update Script"
echo "===================================="

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Error: Python 3 is required but not installed."
    echo "Please install Python 3 and try again."
    exit 1
fi

# Step 1: Install required Python library
echo "📦 Step 1: Installing required Python library..."
if ! python3 -c "import cedict_utils" 2>/dev/null; then
    echo "Installing cedict_utils library..."
    python3 -m pip install cedict_utils
    if [ $? -ne 0 ]; then
        echo "❌ Error: Failed to install cedict_utils library."
        echo "Please install it manually: pip install cedict_utils"
        exit 1
    fi
    echo "✅ cedict_utils library installed successfully"
else
    echo "✅ cedict_utils library already installed"
fi

# Step 2: Convert CC-CEDICT to SQLite (cedict_utils handles download automatically)
echo "🔄 Step 2: Converting CC-CEDICT to SQLite database..."
echo "   (This will automatically download the latest CC-CEDICT data)"
python3 convert_cedict_to_sqlite.py

# Check if conversion was successful
if [ ! -f "chinese_dictionary.db" ]; then
    echo "❌ Error: Database conversion failed."
    exit 1
fi

# Step 3: Backup existing database
echo "💾 Step 3: Backing up existing database..."
if [ -f "../assets/dictionary/chinese_dictionary.db" ]; then
    cp "../assets/dictionary/chinese_dictionary.db" "../assets/dictionary/chinese_dictionary_backup_$(date +%Y%m%d_%H%M%S).db"
    echo "✅ Existing database backed up"
else
    echo "ℹ️  No existing database found (first time setup)"
fi

# Step 4: Install new database
echo "📁 Step 4: Installing new database..."
cp chinese_dictionary.db ../assets/dictionary/chinese_dictionary.db

# Get database info
DB_SIZE=$(du -h ../assets/dictionary/chinese_dictionary.db | cut -f1)
ENTRY_COUNT=$(sqlite3 ../assets/dictionary/chinese_dictionary.db "SELECT COUNT(*) FROM dictionary;" 2>/dev/null || echo "Unknown")

echo "✅ Database installation complete!"
echo "   📊 Database size: $DB_SIZE"
echo "   📚 Total entries: $ENTRY_COUNT"

# Step 5: Clean up
echo "🧹 Step 5: Cleaning up temporary files..."
rm -f chinese_dictionary.db
echo "✅ Cleanup complete"

# Final instructions
echo ""
echo "🎉 CC-CEDICT Database Update Complete!"
echo "======================================"
echo ""
echo "Next steps:"
echo "1. 🔄 Rebuild your Flutter app:"
echo "   flutter clean && flutter pub get && flutter run"
echo ""
echo "2. 🧪 Test the following characters to confirm correct pronunciations:"
echo "   • 没 → should show 'méi' (HSK 1) with 'have not; not'"
echo "   • 行 → should show 'xíng' (HSK 2) with 'to walk; to go'"
echo "   • 中 → should show 'zhōng' (HSK 1) with 'middle; center'"
echo ""
echo "3. 🗑️  Optional: Remove downloads directory to save space:"
echo "   rm -rf downloads/"
echo ""
echo "The new complete CC-CEDICT database is now ready! 🚀"

# CC-CEDICT Database Update Guide

This guide will help you update the Chinese dictionary database with the latest, complete CC-CEDICT data.

## 📋 Prerequisites

- Python 3.7 or higher
- Internet connection (for automatic CC-CEDICT download)
- pip (Python package manager)

## 🚀 Step-by-Step Instructions

### Step 1: Run the Automated Script (Recommended)

1. **Navigate to the scripts directory:**
   ```bash
   cd scripts
   ```

2. **Run the automated update script:**
   ```bash
   ./update_dictionary.sh
   ```

   This script will:
   - ✅ Install required Python libraries automatically
   - ✅ Download the latest CC-CEDICT data automatically
   - ✅ Convert to SQLite format
   - ✅ Backup existing database
   - ✅ Install new database
   - ✅ Clean up temporary files

### Step 2: Manual Process (Alternative)

If you prefer to run the conversion manually:

1. **Install required library:**
   ```bash
   pip install cedict_utils
   ```

2. **Run the conversion script:**
   ```bash
   python convert_cedict_to_sqlite.py
   ```

3. **Wait for conversion to complete:**
   - The script will automatically download and process ~120,000+ entries
   - Progress will be shown every 10,000 entries
   - Takes approximately 2-5 minutes depending on your system

### Step 3: Install the New Database (Manual Process Only)

If you used the manual process, install the database:

1. **Copy the generated database:**
   ```bash
   cp chinese_dictionary.db ../assets/dictionary/chinese_dictionary.db
   ```

*Note: The automated script handles this step automatically.*

### Step 4: Update Flutter App

1. **Clean and rebuild the Flutter app:**
   ```bash
   cd ..  # Go back to project root
   flutter clean
   flutter pub get
   flutter run
   ```

2. **The new database will be automatically used!**

## 📊 What This Fixes

The new complete CC-CEDICT database will include:

### ✅ **Complete Character Coverage**
- **没**: Both méi (HSK 1, primary) and mò (secondary) pronunciations
- **行**: Both xíng (HSK 2, primary) and háng (secondary) pronunciations  
- **中**: Both zhōng (HSK 1, primary) and zhòng (secondary) pronunciations
- **为**: Both wéi (HSK 2, primary) and wèi (secondary) pronunciations
- **All other multi-pronunciation characters**

### ✅ **Proper Prioritization**
- HSK levels correctly assigned
- Frequency scores for common vs rare words
- Primary pronunciations appear first

### ✅ **Complete Definitions**
- ~120,000+ dictionary entries (vs current incomplete set)
- All standard CC-CEDICT definitions
- Proper traditional/simplified character mappings

## 🔧 Technical Details

### Database Schema
```sql
CREATE TABLE dictionary (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    traditional TEXT NOT NULL,
    simplified TEXT NOT NULL,
    pinyin TEXT NOT NULL,
    definitions TEXT NOT NULL,
    hsk_level INTEGER,
    frequency INTEGER
);
```

### Conversion Features
- **HSK Level Detection**: Automatically assigns HSK 1-6 levels based on word lists
- **Frequency Calculation**: Assigns frequency scores based on word length and common patterns
- **Duplicate Prevention**: Skips duplicate simplified+pinyin combinations
- **Proper Indexing**: Creates optimized database indices for fast lookups

### File Locations
- **Source**: `cedict_1_0_ts_utf-8_mdbg.txt` (downloaded)
- **Generated**: `scripts/chinese_dictionary.db`
- **Final**: `assets/dictionary/chinese_dictionary.db`

## 🧪 Testing the Update

After updating, test these characters to confirm correct primary pronunciations:

1. **没** → Should show "méi" (HSK 1) with "have not; not" definition
2. **行** → Should show "xíng" (HSK 2) with "to walk; to go" definition
3. **中** → Should show "zhōng" (HSK 1) with "middle; center" definition
4. **为** → Should show "wéi" (HSK 2) with "for; to act as" definition

## 🚨 Troubleshooting

### Python Not Found
```bash
# Install Python 3
# macOS: brew install python3
# Ubuntu: sudo apt install python3
# Windows: Download from python.org
```

### Permission Errors
```bash
# Make script executable
chmod +x convert_cedict_to_sqlite.py
```

### Large File Size
The new database will be larger (~50-100MB vs current smaller incomplete version). This is normal and expected for a complete dictionary.

## 📝 Notes

- The conversion script preserves all CC-CEDICT data integrity
- HSK levels are assigned based on official HSK word lists in `assets/hsk-json-all/`
- Frequency scores help prioritize common words over rare ones
- The database format is fully compatible with the existing Flutter app code

#!/usr/bin/env python3
"""
CC-CEDICT to SQLite Converter
Downloads and converts the latest CC-CEDICT data to SQLite database format
for use in the Flutter Chinese dictionary app.

Usage:
    python convert_cedict_to_sqlite.py

Output:
    chinese_dictionary.db - SQLite database ready for Flutter app
"""

import sqlite3
import sys
import json
from pathlib import Path

try:
    from cedict_utils.cedict import CedictParser
except ImportError:
    print("❌ Error: cedict_utils library not found.")
    print("Please install it with: pip install cedict_utils")
    sys.exit(1)

# HSK level mapping (based on common HSK word lists)
HSK_WORDS = {
    1: set(),
    2: set(),
    3: set(),
    4: set(),
    5: set(),
    6: set()
}

def load_hsk_data():
    """Load HSK word lists from JSON files if available"""
    script_dir = Path(__file__).parent
    assets_dir = script_dir.parent / "assets" / "hsk-json-all"

    for level in range(1, 7):
        hsk_file = assets_dir / f"hsk-level-{level}.json"
        if hsk_file.exists():
            try:
                with open(hsk_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    for item in data:
                        # Try different possible field names
                        word = None
                        if 'hanzi' in item:
                            word = item['hanzi']
                        elif 'character' in item:
                            word = item['character']
                        elif 'simplified' in item:
                            word = item['simplified']
                        elif 'word' in item:
                            word = item['word']

                        if word:
                            HSK_WORDS[level].add(word)

                print(f"Loaded {len(HSK_WORDS[level])} HSK {level} words")
            except Exception as e:
                print(f"Warning: Could not load HSK {level} data: {e}")

def get_hsk_level(simplified, traditional):
    """Determine HSK level for a word"""
    for level in range(1, 7):
        if simplified in HSK_WORDS[level] or traditional in HSK_WORDS[level]:
            return level
    return None

def download_and_parse_cedict():
    """Download and parse CC-CEDICT using cedict_utils"""
    print("📥 Downloading and parsing latest CC-CEDICT data...")

    try:
        parser = CedictParser()
        entries = parser.parse()

        print(f"✅ Successfully parsed {len(entries)} CC-CEDICT entries")
        return entries

    except Exception as e:
        print(f"❌ Error downloading/parsing CC-CEDICT: {e}")
        print("Please check your internet connection and try again.")
        return None

def create_database(db_path):
    """Create SQLite database with proper schema"""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Create dictionary table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS dictionary (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            traditional TEXT NOT NULL,
            simplified TEXT NOT NULL,
            pinyin TEXT NOT NULL,
            definitions TEXT NOT NULL,
            hsk_level INTEGER,
            frequency INTEGER
        )
    ''')
    
    # Create character info table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS character_info (
            character TEXT PRIMARY KEY,
            radical TEXT,
            components TEXT
        )
    ''')
    
    # Create indices for faster lookups
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_simplified ON dictionary(simplified)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_traditional ON dictionary(traditional)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_pinyin ON dictionary(pinyin)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_hsk_level ON dictionary(hsk_level)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_frequency ON dictionary(frequency)')
    
    conn.commit()
    return conn

def calculate_frequency_score(simplified, traditional, definitions, pinyin):
    """Calculate frequency score based on various factors"""
    base_score = 1000

    # Shorter words are generally more common
    length_bonus = max(0, 5 - len(simplified)) * 100

    # Single characters get frequency boost
    if len(simplified) == 1:
        length_bonus += 200

    # Common definition patterns get frequency boost
    common_patterns = [
        'to be', 'to have', 'to do', 'to go', 'to come', 'to see', 'to say',
        'good', 'big', 'small', 'new', 'old', 'one', 'two', 'three',
        'person', 'people', 'time', 'day', 'year', 'month', 'week',
        'not', 'have not', 'no', 'negative'
    ]

    definition_text = ' '.join(definitions).lower()
    pattern_bonus = sum(50 for pattern in common_patterns if pattern in definition_text)

    # Special handling for common characters with multiple pronunciations
    pronunciation_priority = 0
    if simplified == '没':
        # 'mei2' (have not) is much more common than 'mo4' (drowned)
        if 'mei' in pinyin.lower():
            pronunciation_priority = 300  # Higher priority for mei
        elif 'mo' in pinyin.lower():
            pronunciation_priority = 50   # Lower priority for mo
    elif simplified == '行':
        # 'xing2' (to walk) is more common than 'hang2' (row/line)
        if 'xing' in pinyin.lower():
            pronunciation_priority = 200
        elif 'hang' in pinyin.lower():
            pronunciation_priority = 100
    elif simplified == '中':
        # 'zhong1' (middle) is more common than 'zhong4' (hit target)
        if 'zhong1' in pinyin.lower():
            pronunciation_priority = 300
        elif 'zhong4' in pinyin.lower():
            pronunciation_priority = 100
    elif simplified == '为':
        # 'wei2' (for) is more common than 'wei4' (to do)
        if 'wei2' in pinyin.lower():
            pronunciation_priority = 200
        elif 'wei4' in pinyin.lower():
            pronunciation_priority = 100

    return max(1, base_score - length_bonus - pattern_bonus - pronunciation_priority)

def convert_cedict_to_sqlite(output_db):
    """Download CC-CEDICT and convert to SQLite database"""
    print(f"Creating SQLite database: {output_db}")

    # Download and parse CC-CEDICT
    entries = download_and_parse_cedict()
    if not entries:
        return False

    # Load HSK data
    load_hsk_data()

    # Create database
    conn = create_database(output_db)
    cursor = conn.cursor()

    # Process CC-CEDICT entries
    entry_count = 0
    duplicate_count = 0

    for i, entry in enumerate(entries):
        if i % 10000 == 0 and i > 0:
            print(f"Processed {i} entries, {entry_count} inserted...")

        # Extract data from cedict_utils entry object
        try:
            traditional = entry.traditional
            simplified = entry.simplified
            pinyin = entry.pinyin
            definitions = entry.meanings  # cedict_utils uses 'meanings' not 'definitions'
        except AttributeError as e:
            print(f"Warning: Skipping malformed entry: {e}")
            continue

        # Check for duplicates (same simplified + pinyin combination)
        cursor.execute(
            'SELECT id FROM dictionary WHERE simplified = ? AND pinyin = ?',
            (simplified, pinyin)
        )
        if cursor.fetchone():
            duplicate_count += 1
            continue

        # Get HSK level
        hsk_level = get_hsk_level(simplified, traditional)

        # Calculate frequency score
        frequency = calculate_frequency_score(simplified, traditional, definitions, pinyin)

        # Insert into database
        cursor.execute('''
            INSERT INTO dictionary
            (traditional, simplified, pinyin, definitions, hsk_level, frequency)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (
            traditional,
            simplified,
            pinyin,
            '/'.join(definitions),
            hsk_level,
            frequency
        ))

        entry_count += 1

    conn.commit()
    conn.close()

    print(f"✅ Conversion complete!")
    print(f"   📊 Total entries: {entry_count}")
    print(f"   🔄 Duplicates skipped: {duplicate_count}")
    print(f"   💾 Database saved to: {output_db}")

    return True

def main():
    print("🚀 CC-CEDICT to SQLite Converter")
    print("=================================")
    print("This script will download the latest CC-CEDICT data and convert it to SQLite.")
    print()

    output_db = "chinese_dictionary.db"

    # Check if output file already exists
    if Path(output_db).exists():
        response = input(f"⚠️  {output_db} already exists. Overwrite? (y/N): ")
        if not response.lower().startswith('y'):
            print("Operation cancelled.")
            sys.exit(0)

    # Convert CC-CEDICT to SQLite
    success = convert_cedict_to_sqlite(output_db)

    if not success:
        print("❌ Conversion failed!")
        sys.exit(1)

    print(f"\n🎉 Success! Next steps:")
    print(f"1. Copy {output_db} to assets/dictionary/chinese_dictionary.db")
    print(f"2. Rebuild your Flutter app: flutter clean && flutter run")
    print(f"3. The new complete dictionary will be used automatically")
    print(f"\n💡 Tip: You can also use the automated update script:")
    print(f"   ./update_dictionary.sh")

if __name__ == "__main__":
    main()

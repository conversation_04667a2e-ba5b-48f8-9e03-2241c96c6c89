**English** |

<br>

<h1 align="center">📚 Dasso Reader</h1>
<p align="center">
  <img src="https://img.shields.io/badge/license-Commercial-blue" alt="Commercial License">
  <a href="https://github.com/dassodev/dasso-reader/releases"><img src="https://img.shields.io/github/downloads/dassodev/dasso-reader/total" alt="Downloads"></a>
  <a href="https://github.com/dassodev/dasso-reader/stargazers"><img src="https://img.shields.io/github/stars/dassodev/dasso-reader" alt="stars"></a>
  <img src="https://img.shields.io/badge/platform-Android%20%7C%20iOS%20%7C%20Windows%20%7C%20macOS-lightgrey" alt="Platform">
</p>

**Dasso Reader** - A comprehensive Chinese learning platform and e-book reader designed for Chinese language learners. Combining powerful e-book reading capabilities with an integrated HSK learning system, AI-powered assistance, and modern Material 3 design.

## 🎯 What Makes Dasso Reader Special

**📖 E-book Reader + 🎓 Chinese Learning Platform = 🚀 Complete Learning Experience**

## 🌟 Key Features

### 🎓 **HSK Learning System** (NEW!)
- **Complete HSK 1-6 curriculum** with 5,000+ characters
- **Audio pronunciations** for every character with native speaker quality
- **Interactive learning modes**: Learn, Practice, and Review
- **Stroke order animation** for proper character writing
- **Progress tracking** and spaced repetition system
- **Comprehensive character database** with pinyin, definitions, and examples

### 📖 **Advanced E-book Reader**
- **Multiple formats**: EPUB, MOBI, AZW3, FB2, TXT
- **Chinese text optimization** with proper font rendering
- **Dictionary integration** with instant character lookup
- **Bionic reading** support for enhanced comprehension
- **Cross-device sync** via WebDAV

### 🤖 **AI-Powered Learning**
- **Multiple AI providers**: OpenAI, Claude, Gemini, DeepSeek
- **Context-aware assistance** for reading comprehension
- **Translation and explanation** of complex passages
- **Smart content summarization**

### 🎨 **Modern Material 3 Design**
- **Responsive interface** that adapts to all screen sizes
- **Dark/Light themes** with system integration
- **Customizable typography** and spacing
- **Smooth animations** and intuitive navigation

### 📊 **Learning Analytics**
- **Reading statistics** with detailed charts
- **HSK progress tracking** across all levels
- **Weekly/Monthly/Yearly reports**
- **Learning heatmap** to visualize your study consistency

### 💡 **Smart Features**
- **Text-to-Speech** with natural Chinese pronunciation
- **Full-text search** across your entire library
- **Smart notes system** with export to TXT, CSV, Markdown
- **Cross-platform sync** keeps everything in sync

## 📱 Download & Installation

### 🚀 **Get Started**
```bash
# Clone the repository
git clone https://github.com/dassodev/dasso-reader.git

# Install dependencies
flutter pub get

# Generate localization files
flutter gen-l10n

# Generate code
dart run build_runner build --delete-conflicting-outputs

# Run the app
flutter run
```

### 📦 **Releases**
- **Latest Release**: [Download from GitHub Releases](https://github.com/dassodev/dasso-reader/releases/latest)
- **Supported Platforms**: Android, iOS, Windows, macOS, Linux (coming soon)

### 🔧 **Requirements**
- **Flutter**: 3.29.0 or higher
- **Dart**: 3.6.0 or higher
- **Android**: API level 21+ (Android 5.0+)
- **iOS**: iOS 12.0+

## 🆕 What's New in v2.0

### 🎓 **Complete HSK Learning Integration**
This major update transforms Dasso Reader from a simple e-book reader into a comprehensive Chinese learning platform:

- **5,000+ HSK Characters**: Complete database covering HSK levels 1-6
- **Native Audio Pronunciations**: High-quality audio for every character
- **Interactive Learning Modes**:
  - **Learn Mode**: Introduction to new characters with stroke order
  - **Practice Mode**: Spaced repetition exercises
  - **Review Mode**: Comprehensive testing and reinforcement
- **Progress Tracking**: Visual progress indicators and learning statistics
- **Smart Algorithm**: Adaptive learning based on your performance

### 🎨 **Modern Material 3 Design**
- **Complete UI overhaul** with Google's latest Material 3 design language
- **Responsive layouts** that work perfectly on phones, tablets, and desktops
- **Enhanced accessibility** with better contrast and larger touch targets
- **Smooth animations** and micro-interactions for a premium feel

### 🧹 **Project Optimization**
- **89% size reduction**: Optimized from 3.2GB to 342MB
- **Faster performance**: Improved loading times and responsiveness
- **Cleaner codebase**: Removed unused dependencies and files
- **Better maintainability**: Updated to latest Flutter 3.29.0

### 🔧 **Technical Improvements**
- **Enhanced dictionary service** with better Chinese text segmentation
- **Improved AI integration** with more reliable responses
- **Better error handling** and user feedback
- **Updated dependencies** for security and performance

## 🗺️ Roadmap

### ✅ **Completed in v2.0**
- [X] Complete HSK learning system (HSK 1-6)
- [X] 5,000+ audio pronunciations
- [X] Material 3 design system
- [X] Project optimization (89% size reduction)
- [X] Enhanced dictionary integration
- [X] Improved AI assistance
- [X] Cross-platform responsive design

## 🆘 Support & Community

### 📞 **Get Help**
- **Issues**: [Report bugs or request features](https://github.com/dassodev/dasso-reader/issues/new/choose)
- **Discussions**: [Join community discussions](https://github.com/dassodev/dasso-reader/discussions)
- **Documentation**: [Check the troubleshooting guide](./docs/troubleshooting.md#English)

### 🤝 **Connect With Us**
- **GitHub**: [@dassodev](https://github.com/dassodev)
- **Email**: <EMAIL>

I will do my best to respond to all issues and questions as quickly as possible.

## 🛠️ Development Information

### ⚠️ **Important Notice**

**Dasso Reader is a commercial application.** The source code is available for reference and learning purposes only.

### 🚀 **Getting Started for Development**

If you want to build and run Dasso Reader locally for learning purposes:

#### **Prerequisites**
- **Flutter 3.29.0+** - [Install Flutter](https://flutter.dev/docs/get-started/install)
- **Dart 3.6.0+** - Comes with Flutter
- **Git** - For version control

#### **Setup Instructions**
```bash
# 1. Clone the repository
git clone https://github.com/dassodev/dasso-reader.git
cd dasso-reader

# 2. Install dependencies
flutter pub get

# 3. Generate localization files
flutter gen-l10n

# 4. Generate code (Riverpod, JSON serialization, etc.)
dart run build_runner build --delete-conflicting-outputs

# 5. Run the app
flutter run
```

#### **Project Structure**
```
lib/
├── config/          # App configuration and design system
├── models/          # Data models and entities
├── providers/       # Riverpod state management
├── services/        # Business logic and external APIs
├── widgets/         # Reusable UI components
└── pages/           # Screen implementations
```

### 🤝 **Feedback & Suggestions**

Since this is a commercial application, we don't accept direct code contributions. However, we welcome:

- 🐛 **Bug Reports**: Help us identify and fix issues
- 💡 **Feature Requests**: Suggest new features or improvements
- 📝 **Documentation**: Report unclear or missing documentation
- 🌍 **Translations**: Help us support more languages

### 📋 **How to Provide Feedback**
1. **Open an Issue**: [Report bugs or suggest features](https://github.com/dassodev/dasso-reader/issues)
2. **Join Discussions**: [Share ideas and feedback](https://github.com/dassodev/dasso-reader/discussions)
3. **Contact Us**: Email <NAME_EMAIL>

### 🏆 **Recognition**
Contributors who provide valuable feedback, bug reports, or suggestions will be acknowledged in our release notes and documentation.


## 📄 License

**Dasso Reader** is a **commercial application**. All rights reserved.

```
Copyright (c) 2025 Dasso Developer. All rights reserved.

This software and its source code are proprietary and confidential.
Unauthorized copying, distribution, modification, or use of this software
is strictly prohibited without explicit written permission from the copyright holder.

For licensing inquiries, please contact: <EMAIL>
```

### 📋 **Usage Terms**
institutions
- 💼 **Commercial Use**: Requires commercial license (contact us)
- 🚫 **Redistribution**: Not permitted without written authorization
- 🚫 **Modification**: Source code modification not permitted

## 🙏 Acknowledgments

### **Core Technologies**
- **[Flutter](https://flutter.dev)** - The amazing cross-platform framework
- **[foliate-js](https://github.com/johnfactotum/foliate-js)** - Excellent EPUB rendering engine (MIT License)
- **[Riverpod](https://riverpod.dev)** - Powerful state management solution

### **Chinese Learning Resources**
- **HSK Character Database** - Comprehensive character definitions and examples
- **Audio Pronunciations** - High-quality native speaker recordings
- **Stroke Order Data** - Accurate character writing sequences

### **Design & UI**
- **[Material 3](https://m3.material.io)** - Google's latest design system
- **[Chinese Font Library](https://pub.dev/packages/chinese_font_library)** - Beautiful Chinese typography

### **AI & Services**
- **OpenAI, Claude, Gemini, DeepSeek** - AI assistance providers
- **Dictionary Services** - Chinese-English translation and definitions

### **Community**
Special thanks to all users for their valuable feedback and support in making Dasso Reader better.

---

<p align="center">
  <strong>Made with ❤️ for Chinese language learners worldwide</strong>
</p>

<p align="center">
  <a href="https://github.com/dassodev/dasso-reader">⭐ Star us on GitHub</a> •
  <a href="https://github.com/dassodev/dasso-reader/issues">🐛 Report Bug</a> •
  <a href="https://github.com/dassodev/dasso-reader/discussions">💬 Discussions</a>
</p>


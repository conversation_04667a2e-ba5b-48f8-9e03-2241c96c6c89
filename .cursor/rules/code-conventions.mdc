---
description: 
globs: 
alwaysApply: true
---
# Code Conventions

## Naming Conventions
- **Classes & Enums** - PascalCase (e.g., `BookReader`, `ReadingStatus`)
- **Variables & Methods** - camelCase (e.g., `bookTitle`, `parseChapter()`)
- **Files** - snake_case (e.g., `book_reader.dart`, `reading_status.dart`)
- **Constants** - SCREAMING_SNAKE_CASE (e.g., `MAX_CHAPTER_SIZE`, `DEFAULT_FONT_SIZE`)
- **Private members** - Prefix with underscore (e.g., `_privateVariable`)
- **Extension methods** - Descriptive verbs (e.g., `parseAsBook()`, `formatAsTitle()`)

## File Organization
- One class per file, named after the primary class
- Extensions should be in separate files ending with `.extension.dart`
- Generated files should not be modified manually
- Group related files in feature-specific directories
- Keep file sizes under 300-400 lines for maintainability

## State Management
- Use Provider pattern for state management
- StateNotifier for complex state
- ValueNotifier for simple state
- Follow Riverpod patterns:
  - Use `Provider` for simple dependencies
  - Use `StateProvider` for simple state
  - Use `StateNotifierProvider` for complex state
  - Use `FutureProvider` for async data
  - Use `StreamProvider` for reactive data
- Prefer immutable state objects
- Implement proper state disposal to prevent memory leaks

## Widget Structure
- Stateless widgets preferred when possible
- Extract complex UI into smaller widget components
- Use const constructors when applicable

## Asynchronous Code Patterns
- Use AsyncValue for representing async operations
- Properly handle loading, data, and error states
- Use Future.wait for parallel operations
- Implement retry logic for network operations
- Use computed properties for derived state

## Linting Rules
- Use `flutter_lints` 3.0.0+ settings as a baseline
- Enable strict mode where possible
- Add custom lint rules for project-specific conventions
- Enforce consistent code formatting
- Run linter as part of CI/CD process

## Comments and Documentation
- Use /// for documentation comments
- Document all public APIs
- Include code examples where helpful
- Explain complex algorithms and business logic

---
description: 
globs: 
alwaysApply: true
---
# Chinese Learning Features

## Dictionary & Translation
- Support tap-to-translate functionality
- Implement character decomposition
- Support radical lookup
- Include both simplified and traditional characters
- Provide word frequency information
- Implement local dictionary for offline usage
- Support integration with external Chinese learning APIs
- Add contextual vocabulary suggestions
- Implement analytics for frequently looked-up characters

## Reading Aids
- Display pinyin (above text, inline, or hidden)
- Support HSK level indicators
- Implement reading difficulty estimation
- Provide character stroke order diagrams
- Support annotation and highlighting
- Add intelligent word segmentation for Chinese text
- Implement optimized character stroke animations
- Add grammar pattern recognition and highlighting
- Support component-based character learning

## Pronunciation & Audio
- Optimize TTS for natural Chinese pronunciation
- Support multiple TTS engines
- Allow adjustable reading speed
- Support sentence-by-sentence playback
- Implement proper audio focus management
- Add audio waveform visualization
- Implement pitch contour visualization for tones
- Support recording and comparison with native audio
- Optimize audio memory usage on lower-end devices

## Learning Progression
- Implement spaced repetition for vocabulary
- Track known vs. unknown characters
- Support vocabulary lists and flashcards
- Provide reading statistics
- Implement progress tracking

## Content Support
- Support multiple ebook formats
- Proper rendering of Chinese characters
- Handle special formatting for language learning
- Support dual-language texts
- Implement proper text extraction for dictionary lookup

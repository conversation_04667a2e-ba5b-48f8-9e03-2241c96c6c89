---
description: 
globs: 
alwaysApply: true
---
# Project Structure

## Directory Organization

### Root Level
- `lib/` - All Dart source code
- `assets/` - Static resources (images, fonts, etc.)
- `android/` - Android-specific configuration
- `test/` - Unit and widget tests
- `docs/` - Documentation files
- `scripts/` - Utility scripts for development

### lib/ Directory Structure
- `main.dart` - Application entry point
- `models/` - Data models and business entities
- `providers/` - State management providers
- `dao/` - Data access objects for database interactions
- `service/` - Business logic and external services
- `utils/` - Utility functions and helper classes
- `widgets/` - Reusable UI components
- `page/` - Application screens
- `l10n/` - Localization resources
- `config/` - Application configuration
- `enums/` - Enumeration definitions
- `gen/` - Generated code (don't edit manually)

## File Naming Conventions
- Feature-first organization
- Use snake_case for filenames
- UI components should end with `_screen.dart`, `_page.dart`, or `_widget.dart`
- Data models should end with `_model.dart`
- State providers should end with `_provider.dart`
- Services should end with `_service.dart`

## Asset Organization
- Images: `assets/images/`
- Fonts: `assets/fonts/`
- JSON files: `assets/json/`
- Icon assets: organized in appropriate subfolders

## Code Generation
- Generated files should go in `lib/gen/`
- Don't manually edit files in the `gen/` directory
- Use appropriate build commands to regenerate when source changes

## Import Organization
- Group imports in the following order:
  1. Dart core libraries
  2. Flutter packages
  3. Third-party packages
  4. Project imports (absolute)
  5. Relative imports
- Use absolute imports for project files where possible

## Modular Architecture
- Implement a more modular architecture for better code isolation
- Use feature-first organization where appropriate
- Maintain clear separation of concerns between modules
- Define clear interfaces between modules
- Minimize cross-module dependencies
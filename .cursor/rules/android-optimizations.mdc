---
description: 
globs: 
alwaysApply: true
---
# Android Optimizations

## Performance Guidelines
- Use lazy loading for book content
- Implement paging for large collections
- Minimize UI rebuilds with proper widget hierarchy
- Use `const` constructors whenever possible
- Avoid unnecessary widget rebuilds
- Use isolates for heavy computations (dictionary lookup, text analysis)
- Implement efficient background download management
- Apply memory optimization for image caching with size limits
- Utilize compression techniques for stored content
- Profile and optimize startup time with deferred component loading

## Battery & Resource Management
- Suspend background processes when not actively reading
- Release resources when pages are closed
- Optimize TTS engine usage
- Implement proper image caching
- Use efficient rendering techniques for book pages
- Implement adaptive refresh rates based on content type
- Use workmanager for scheduled tasks
- Implement proper wake lock management during reading sessions
- Apply network bandwidth optimization for content fetching

## Storage Management
- Cache downloaded content properly
- Implement cleanup methods for temporary files
- Support proper file management
- Allow user control over storage allocation

## UI/UX Considerations
- Follow Material Design guidelines
- Support both light and dark themes
- Ensure proper handling of different screen sizes
- Implement proper keyboard navigation
- Support gesture-based navigation

## Android-Specific Features
- Properly handle Android lifecycle events
- Implement proper permission handling
- Support Android-specific navigation patterns
- Use Android platform channels when needed

---
description: 
globs: 
alwaysApply: true
---
# Development Workflow

## Version Control
- Use feature branches for new functionality
- Format commit messages as `type(scope): message`
- Types: feat, fix, docs, style, refactor, test, chore
- Keep commits focused and atomic
- Regularly merge from main to avoid conflicts
- Use Pull Requests for code review
- Require at least one reviewer approval before merging

## Dependency Management
- Keep dependencies in [pubspec.yaml](mdc:pubspec.yaml) up to date
- Pin versions to avoid breaking changes
- Use dev_dependencies for development tools
- Regularly audit dependencies for security issues
- Document the purpose of each dependency
- Prefer official Flutter packages where available
- Periodically review and remove unused dependencies

## Build Process
- Use Flutter flavors for different environments
- Implement proper signing configuration
- Automate versioning in [android/app/build.gradle](mdc:android/app/build.gradle)
- Include proper ProGuard rules
- Optimize APK size for distribution
- Set up automated build process using CI/CD
- Generate build reports for size and performance metrics

## Flutter Version Management
- Document Flutter SDK version requirements
- Test thoroughly when upgrading Flutter versions
- Include migration steps for major version updates
- Maintain compatibility with at least N-1 Flutter version

## Refactoring
- Maintain backward compatibility
- Update tests when refactoring
- Document major architecture changes
- Follow the boy scout rule: leave code cleaner than you found it
- Use TODOs sparingly and with clear descriptions
